const axios = require('axios');
require('dotenv').config({ path: './config/config.env' });

class VapiService {
  constructor() {
    this.baseURL = 'https://api.vapi.ai';
    this.apiKey = process.env.VAPI_SECRET_KEY;
    
    if (!this.apiKey) {
      console.error('VAPI_SECRET_KEY not found in environment variables');
      throw new Error('VAPI_SECRET_KEY is required');
    }

    this.client = axios.create({
      baseURL: this.baseURL,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('VapiService initialized with API key:', this.apiKey ? '✓' : '✗');
  }





  // Create assistant in Vapi
  async createAssistant(assistantData) {
    try {
      console.log('Creating assistant in Vapi:', assistantData.name);
      
      const response = await this.client.post('/assistant', assistantData);
      
      console.log('Vapi assistant created successfully:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Error creating assistant in Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Get assistant from Vapi
  async getAssistant(assistantId) {
    try {
      console.log('Getting assistant from Vapi:', assistantId);
      
      const response = await this.client.get(`/assistant/${assistantId}`);
      
      console.log('Vapi assistant retrieved successfully:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Error getting assistant from Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // List assistants from Vapi
  async listAssistants(params = {}) {
    try {
      console.log('Listing assistants from Vapi');
      
      const response = await this.client.get('/assistant', { params });
      
      console.log('Vapi assistants retrieved successfully:', response.data.length || 0);
      return response.data;
    } catch (error) {
      console.error('Error listing assistants from Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Create call in Vapi
  async createCall(callData) {
    try {
      console.log('Creating call in Vapi');
      
      const response = await this.client.post('/call', callData);
      
      console.log('Vapi call created successfully:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Error creating call in Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Create workflow in Vapi
  async createWorkflow(workflowData) {
    try {
      console.log('Creating workflow in Vapi:', workflowData.name);

      const response = await this.client.post('/workflow', workflowData);

      console.log('Vapi workflow created successfully:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Error creating workflow in Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Get workflow from Vapi
  async getWorkflow(workflowId) {
    try {
      console.log('Getting workflow from Vapi:', workflowId);

      const response = await this.client.get(`/workflow/${workflowId}`);

      console.log('Vapi workflow retrieved successfully:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Error getting workflow from Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // List workflows from Vapi
  async listWorkflows(params = {}) {
    try {
      console.log('Listing workflows from Vapi');

      const response = await this.client.get('/workflow', { params });

      console.log('Vapi workflows retrieved successfully:', response.data.length || 0);
      return response.data;
    } catch (error) {
      console.error('Error listing workflows from Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Update workflow in Vapi
  async updateWorkflow(workflowId, workflowData) {
    try {
      console.log('Updating workflow in Vapi:', workflowId);

      const response = await this.client.patch(`/workflow/${workflowId}`, workflowData);

      console.log('Vapi workflow updated successfully:', response.data.id);
      return response.data;
    } catch (error) {
      console.error('Error updating workflow in Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Delete workflow from Vapi
  async deleteWorkflow(workflowId) {
    try {
      console.log('Deleting workflow from Vapi:', workflowId);

      await this.client.delete(`/workflow/${workflowId}`);

      console.log('Vapi workflow deleted successfully:', workflowId);
      return true;
    } catch (error) {
      console.error('Error deleting workflow from Vapi:', error.response?.data || error.message);
      throw new Error(`Vapi API Error: ${error.response?.data?.message || error.message}`);
    }
  }

  // Test connection to Vapi
  async testConnection() {
    try {
      console.log('Testing Vapi connection...');

      // Try to list assistants as a simple test
      await this.client.get('/assistant?limit=1');

      console.log('✅ Vapi connection successful');
      return true;
    } catch (error) {
      console.error('❌ Vapi connection failed:', error.response?.data || error.message);
      return false;
    }
  }
}

module.exports = new VapiService();
