{"name": "ai_agent", "version": "1.0.0", "main": "index.js", "scripts": {"start": "nodemon index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-tabs": "^1.1.12", "@vapi-ai/server-sdk": "^0.2.2", "@vapi-ai/web": "^2.1.8", "axios": "^1.7.9", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "form-data": "^4.0.2", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "mysql2": "^3.11.4", "nodemon": "^3.1.7", "reactflow": "^11.11.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}}