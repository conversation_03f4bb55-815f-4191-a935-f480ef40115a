const pool = require('../../config/DBConnection');
const { v4: uuidv4 } = require('uuid');

// VAPI Workflow Controller (Based on Official VAPI API Documentation)
class VapiWorkflowController {
  
  // GET /api/vapi/workflows - Get all VAPI workflows
  async getAllWorkflows(req, res) {
    try {
      const connection = await pool.getConnection();

      try {
        // Get all workflows
        const [workflows] = await connection.execute(
          'SELECT * FROM vapi_workflows ORDER BY created_at DESC'
        );

        // Get nodes and edges for each workflow
        const workflowsWithRelations = await Promise.all(
          workflows.map(async (workflow) => {
            const [nodes] = await connection.execute(
              'SELECT * FROM vapi_workflow_nodes WHERE workflow_id = ? ORDER BY created_at',
              [workflow.id]
            );

            const [edges] = await connection.execute(
              'SELECT * FROM vapi_workflow_edges WHERE workflow_id = ? ORDER BY created_at',
              [workflow.id]
            );

            return this.transformWorkflowResponse({
              ...workflow,
              nodes,
              edges,
            });
          })
        );

        res.json(workflowsWithRelations);
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error('Error fetching VAPI workflows:', error);
      res.status(500).json({ error: 'Failed to fetch workflows' });
    }
  }

  // GET /api/vapi/workflows/:id - Get specific VAPI workflow
  async getWorkflowById(req, res) {
    try {
      const { id } = req.params;
      const connection = await pool.getConnection();

      try {
        // Get workflow
        const [workflows] = await connection.execute(
          'SELECT * FROM vapi_workflows WHERE id = ?',
          [id]
        );

        if (workflows.length === 0) {
          return res.status(404).json({ error: 'Workflow not found' });
        }

        const workflow = workflows[0];

        // Get nodes
        const [nodes] = await connection.execute(
          'SELECT * FROM vapi_workflow_nodes WHERE workflow_id = ? ORDER BY created_at',
          [id]
        );

        // Get edges
        const [edges] = await connection.execute(
          'SELECT * FROM vapi_workflow_edges WHERE workflow_id = ? ORDER BY created_at',
          [id]
        );

        const response = this.transformWorkflowResponse({
          ...workflow,
          nodes,
          edges,
        });

        res.json(response);
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error('Error fetching VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to fetch workflow' });
    }
  }

  // POST /api/vapi/workflows - Create new VAPI workflow
  async createWorkflow(req, res) {
    try {
      const {
        name,
        description,
        nodes = [],
        edges = [],
        model,
        transcriber,
        voice,
        globalPrompt,
        observabilityPlan,
        backgroundSound = 'off',
        hooks = [],
        credentials = [],
        server,
        compliancePlan,
        analysisPlan,
        artifactPlan,
        startSpeakingPlan,
        stopSpeakingPlan,
        monitorPlan,
        backgroundSpeechDenoisingPlan,
        credentialIds = [],
        keypadInputPlan,
      } = req.body;

      // Validate required fields
      if (!name) {
        return res.status(400).json({ error: 'Workflow name is required' });
      }

      const connection = await pool.getConnection();

      try {
        await connection.beginTransaction();

        const workflowId = uuidv4();

        // Create workflow
        await connection.execute(
          `INSERT INTO vapi_workflows (
            id, name, description, status, model, transcriber, voice, global_prompt,
            observability_plan, background_sound, hooks, credentials, server,
            compliance_plan, analysis_plan, artifact_plan, start_speaking_plan,
            stop_speaking_plan, monitor_plan, background_speech_denoising_plan,
            credential_ids, keypad_input_plan
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            workflowId,
            name,
            description,
            'draft',
            model ? JSON.stringify(model) : null,
            transcriber ? JSON.stringify(transcriber) : null,
            voice ? JSON.stringify(voice) : null,
            globalPrompt,
            observabilityPlan ? JSON.stringify(observabilityPlan) : null,
            backgroundSound,
            JSON.stringify(hooks),
            JSON.stringify(credentials),
            server ? JSON.stringify(server) : null,
            compliancePlan ? JSON.stringify(compliancePlan) : null,
            analysisPlan ? JSON.stringify(analysisPlan) : null,
            artifactPlan ? JSON.stringify(artifactPlan) : null,
            startSpeakingPlan ? JSON.stringify(startSpeakingPlan) : null,
            stopSpeakingPlan ? JSON.stringify(stopSpeakingPlan) : null,
            monitorPlan ? JSON.stringify(monitorPlan) : null,
            backgroundSpeechDenoisingPlan ? JSON.stringify(backgroundSpeechDenoisingPlan) : null,
            JSON.stringify(credentialIds),
            keypadInputPlan ? JSON.stringify(keypadInputPlan) : null,
          ]
        );

        // Create nodes
        for (const [index, node] of nodes.entries()) {
          await connection.execute(
            `INSERT INTO vapi_workflow_nodes (
              id, workflow_id, node_id, type, name, position, data, is_start
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              uuidv4(),
              workflowId,
              node.id || `node-${index}`,
              node.type,
              node.name,
              JSON.stringify(node.position || { x: 0, y: 0 }),
              JSON.stringify(node),
              node.isStart || false,
            ]
          );
        }

        // Create edges
        for (const [index, edge] of edges.entries()) {
          await connection.execute(
            `INSERT INTO vapi_workflow_edges (
              id, workflow_id, edge_id, source, target, condition, condition_type, data
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              uuidv4(),
              workflowId,
              edge.id || `edge-${index}`,
              edge.source || edge.from,
              edge.target || edge.to,
              edge.condition,
              edge.conditionType || 'ai',
              JSON.stringify(edge),
            ]
          );
        }

        await connection.commit();

        // Fetch the created workflow with relations
        const [workflows] = await connection.execute(
          'SELECT * FROM vapi_workflows WHERE id = ?',
          [workflowId]
        );

        const [workflowNodes] = await connection.execute(
          'SELECT * FROM vapi_workflow_nodes WHERE workflow_id = ?',
          [workflowId]
        );

        const [workflowEdges] = await connection.execute(
          'SELECT * FROM vapi_workflow_edges WHERE workflow_id = ?',
          [workflowId]
        );

        const response = this.transformWorkflowResponse({
          ...workflows[0],
          nodes: workflowNodes,
          edges: workflowEdges,
        });

        res.status(201).json(response);
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error('Error creating VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to create workflow' });
    }
  }

  // PUT /api/vapi/workflows/:id - Update VAPI workflow
  async updateWorkflow(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      // Check if workflow exists
      const existingWorkflow = await prisma.vapiWorkflow.findUnique({
        where: { id },
      });

      if (!existingWorkflow) {
        return res.status(404).json({ error: 'Workflow not found' });
      }

      // Update workflow
      const workflow = await prisma.vapiWorkflow.update({
        where: { id },
        data: {
          ...updateData,
          updatedAt: new Date(),
        },
        include: {
          nodes: true,
          edges: true,
        },
      });

      const response = this.transformWorkflowResponse(workflow);
      res.json(response);
    } catch (error) {
      console.error('Error updating VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to update workflow' });
    }
  }

  // PATCH /api/vapi/workflows/:id/auto-save - Auto-save workflow
  async autoSaveWorkflow(req, res) {
    try {
      const { id } = req.params;
      const { nodes, edges, ...otherData } = req.body;

      // Update workflow with auto-save data
      const workflow = await prisma.$transaction(async (prisma) => {
        // Update main workflow data
        const updatedWorkflow = await prisma.vapiWorkflow.update({
          where: { id },
          data: {
            ...otherData,
            updatedAt: new Date(),
          },
        });

        // Update nodes if provided
        if (nodes) {
          // Delete existing nodes
          await prisma.vapiWorkflowNode.deleteMany({
            where: { workflowId: id },
          });

          // Create new nodes
          await prisma.vapiWorkflowNode.createMany({
            data: nodes.map((node, index) => ({
              workflowId: id,
              nodeId: node.id || `node-${index}`,
              type: node.type,
              name: node.name,
              position: JSON.stringify(node.position || { x: 0, y: 0 }),
              data: JSON.stringify(node),
              isStart: node.isStart || false,
            })),
          });
        }

        // Update edges if provided
        if (edges) {
          // Delete existing edges
          await prisma.vapiWorkflowEdge.deleteMany({
            where: { workflowId: id },
          });

          // Create new edges
          await prisma.vapiWorkflowEdge.createMany({
            data: edges.map((edge, index) => ({
              workflowId: id,
              edgeId: edge.id || `edge-${index}`,
              source: edge.source || edge.from,
              target: edge.target || edge.to,
              condition: edge.condition,
              conditionType: edge.conditionType || 'ai',
              data: JSON.stringify(edge),
            })),
          });
        }

        return updatedWorkflow;
      });

      // Fetch complete workflow with relations
      const completeWorkflow = await prisma.vapiWorkflow.findUnique({
        where: { id },
        include: {
          nodes: true,
          edges: true,
        },
      });

      const response = this.transformWorkflowResponse(completeWorkflow);
      res.json(response);
    } catch (error) {
      console.error('Error auto-saving VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to auto-save workflow' });
    }
  }

  // DELETE /api/vapi/workflows/:id - Delete VAPI workflow
  async deleteWorkflow(req, res) {
    try {
      const { id } = req.params;

      // Check if workflow exists
      const existingWorkflow = await prisma.vapiWorkflow.findUnique({
        where: { id },
      });

      if (!existingWorkflow) {
        return res.status(404).json({ error: 'Workflow not found' });
      }

      // Delete workflow (cascade will handle nodes and edges)
      await prisma.vapiWorkflow.delete({
        where: { id },
      });

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to delete workflow' });
    }
  }

  // POST /api/vapi/workflows/:id/execute - Execute/Test workflow
  async executeWorkflow(req, res) {
    try {
      const { id } = req.params;
      const { phoneNumber } = req.body;

      // Check if workflow exists
      const workflow = await prisma.vapiWorkflow.findUnique({
        where: { id },
        include: {
          nodes: true,
          edges: true,
        },
      });

      if (!workflow) {
        return res.status(404).json({ error: 'Workflow not found' });
      }

      // Create execution record
      const execution = await prisma.vapiWorkflowExecution.create({
        data: {
          workflowId: id,
          status: 'running',
          phoneNumber,
          startedAt: new Date(),
          variables: JSON.stringify({}),
        },
      });

      // Here you would integrate with actual VAPI API to execute the workflow
      // For now, we'll simulate the execution
      setTimeout(async () => {
        await prisma.vapiWorkflowExecution.update({
          where: { id: execution.id },
          data: {
            status: 'completed',
            completedAt: new Date(),
          },
        });
      }, 5000);

      res.json({
        id: execution.id,
        workflowId: id,
        status: 'running',
        startedAt: execution.startedAt,
        phoneNumber,
      });
    } catch (error) {
      console.error('Error executing VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to execute workflow' });
    }
  }

  // POST /api/vapi/workflows/:id/validate - Validate workflow
  async validateWorkflow(req, res) {
    try {
      const { id } = req.params;

      const workflow = await prisma.vapiWorkflow.findUnique({
        where: { id },
        include: {
          nodes: true,
          edges: true,
        },
      });

      if (!workflow) {
        return res.status(404).json({ error: 'Workflow not found' });
      }

      const errors = [];

      // Validate workflow structure
      if (workflow.nodes.length === 0) {
        errors.push('Workflow must have at least one node');
      }

      const startNodes = workflow.nodes.filter(node => node.isStart);
      if (startNodes.length === 0) {
        errors.push('Workflow must have a start node');
      }
      if (startNodes.length > 1) {
        errors.push('Workflow can only have one start node');
      }

      // Validate node connections
      const nodeIds = workflow.nodes.map(node => node.nodeId);
      for (const edge of workflow.edges) {
        if (!nodeIds.includes(edge.source)) {
          errors.push(`Edge references non-existent source node: ${edge.source}`);
        }
        if (!nodeIds.includes(edge.target)) {
          errors.push(`Edge references non-existent target node: ${edge.target}`);
        }
      }

      res.json({
        isValid: errors.length === 0,
        errors,
      });
    } catch (error) {
      console.error('Error validating VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to validate workflow' });
    }
  }

  // POST /api/vapi/workflows/:id/clone - Clone workflow
  async cloneWorkflow(req, res) {
    try {
      const { id } = req.params;
      const { name } = req.body;

      const originalWorkflow = await prisma.vapiWorkflow.findUnique({
        where: { id },
        include: {
          nodes: true,
          edges: true,
        },
      });

      if (!originalWorkflow) {
        return res.status(404).json({ error: 'Workflow not found' });
      }

      // Create cloned workflow
      const clonedWorkflow = await prisma.vapiWorkflow.create({
        data: {
          name: name || `${originalWorkflow.name} (Copy)`,
          description: originalWorkflow.description,
          model: originalWorkflow.model,
          transcriber: originalWorkflow.transcriber,
          voice: originalWorkflow.voice,
          globalPrompt: originalWorkflow.globalPrompt,
          status: 'draft',
          nodes: {
            create: originalWorkflow.nodes.map(node => ({
              nodeId: node.nodeId,
              type: node.type,
              name: node.name,
              position: node.position,
              data: node.data,
              isStart: node.isStart,
            })),
          },
          edges: {
            create: originalWorkflow.edges.map(edge => ({
              edgeId: edge.edgeId,
              source: edge.source,
              target: edge.target,
              condition: edge.condition,
              conditionType: edge.conditionType,
              data: edge.data,
            })),
          },
        },
        include: {
          nodes: true,
          edges: true,
        },
      });

      const response = this.transformWorkflowResponse(clonedWorkflow);
      res.status(201).json(response);
    } catch (error) {
      console.error('Error cloning VAPI workflow:', error);
      res.status(500).json({ error: 'Failed to clone workflow' });
    }
  }

  // Transform workflow response to match VAPI API format
  transformWorkflowResponse(workflow) {
    return {
      id: workflow.id,
      orgId: workflow.org_id || 'default',
      createdAt: workflow.created_at,
      updatedAt: workflow.updated_at,
      name: workflow.name,
      description: workflow.description,
      status: workflow.status,
      nodes: workflow.nodes?.map(node => {
        const nodeData = JSON.parse(node.data || '{}');
        return {
          ...nodeData,
          id: node.node_id,
          type: node.type,
          name: node.name,
          position: JSON.parse(node.position || '{"x":0,"y":0}'),
          isStart: node.is_start,
        };
      }) || [],
      edges: workflow.edges?.map(edge => {
        const edgeData = JSON.parse(edge.data || '{}');
        return {
          ...edgeData,
          id: edge.edge_id,
          from: edge.source,
          to: edge.target,
          source: edge.source,
          target: edge.target,
          condition: edge.condition,
          conditionType: edge.condition_type,
        };
      }) || [],
      model: workflow.model ? JSON.parse(workflow.model) : null,
      transcriber: workflow.transcriber ? JSON.parse(workflow.transcriber) : null,
      voice: workflow.voice ? JSON.parse(workflow.voice) : null,
      globalPrompt: workflow.global_prompt,
      observabilityPlan: workflow.observability_plan ? JSON.parse(workflow.observability_plan) : null,
      backgroundSound: workflow.background_sound,
      hooks: JSON.parse(workflow.hooks || '[]'),
      credentials: JSON.parse(workflow.credentials || '[]'),
      server: workflow.server ? JSON.parse(workflow.server) : null,
      compliancePlan: workflow.compliance_plan ? JSON.parse(workflow.compliance_plan) : null,
      analysisPlan: workflow.analysis_plan ? JSON.parse(workflow.analysis_plan) : null,
      artifactPlan: workflow.artifact_plan ? JSON.parse(workflow.artifact_plan) : null,
      startSpeakingPlan: workflow.start_speaking_plan ? JSON.parse(workflow.start_speaking_plan) : null,
      stopSpeakingPlan: workflow.stop_speaking_plan ? JSON.parse(workflow.stop_speaking_plan) : null,
      monitorPlan: workflow.monitor_plan ? JSON.parse(workflow.monitor_plan) : null,
      backgroundSpeechDenoisingPlan: workflow.background_speech_denoising_plan ? JSON.parse(workflow.background_speech_denoising_plan) : null,
      credentialIds: JSON.parse(workflow.credential_ids || '[]'),
      keypadInputPlan: workflow.keypad_input_plan ? JSON.parse(workflow.keypad_input_plan) : null,
    };
  }
}

module.exports = new VapiWorkflowController();
