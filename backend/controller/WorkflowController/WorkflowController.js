const axios = require("axios");
const WorkflowModel = require("../../model/WorkflowModel/WorkflowModel");
const VapiService = require("../../services/VapiService");

const VAPI_BASE_URL = "https://api.vapi.ai";
const VAPI_SECRET_KEY = process.env.VAPI_SECRET_KEY;

// List all workflows
const listWorkflows = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = "" } = req.query;
    const user_id = req.user.id;

    let vapiWorkflows = [];

    // Try to get workflows from Vapi API
    try {
      const response = await axios.get(`${VAPI_BASE_URL}/workflow`, {
        headers: {
          Authorization: `Bearer ${VAPI_SECRET_KEY}`,
          "Content-Type": "application/json"
        },
        params: {
          limit: parseInt(limit),
          offset: (parseInt(page) - 1) * parseInt(limit)
        }
      });
      vapiWorkflows = Array.isArray(response.data) ? response.data : [];
    } catch (vapiError) {
      console.warn("Vapi API error, falling back to local workflows:", vapiError.response?.data || vapiError.message);
      // Continue with empty Vapi workflows array
    }

    // Get local workflow records
    const localWorkflows = await WorkflowModel.getWorkflowsByUserId(user_id, {
      page: parseInt(page),
      limit: parseInt(limit),
      search
    });

    // If we have Vapi workflows, merge them with local data
    let mergedWorkflows = [];
    if (vapiWorkflows.length > 0) {
      mergedWorkflows = vapiWorkflows.map(vapiWorkflow => {
        const localWorkflow = localWorkflows.workflows.find(
          local => local.workflow_id === vapiWorkflow.id
        );
        return {
          ...vapiWorkflow,
          localData: localWorkflow || null
        };
      });
    } else {
      // If no Vapi workflows, return local workflows with mock structure
      mergedWorkflows = localWorkflows.workflows.map(localWorkflow => ({
        id: localWorkflow.workflow_id,
        name: localWorkflow.name,
        description: localWorkflow.description,
        nodes: [],
        edges: [],
        createdAt: localWorkflow.created_at,
        updatedAt: localWorkflow.updated_at,
        localData: localWorkflow
      }));
    }

    res.status(200).json({
      success: true,
      message: "Workflows retrieved successfully",
      data: {
        workflows: mergedWorkflows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(Math.max(vapiWorkflows.length, localWorkflows.workflows.length) / parseInt(limit)),
          totalItems: Math.max(vapiWorkflows.length, localWorkflows.workflows.length),
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error("Error listing workflows:", error.message);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve workflows",
      error: error.message
    });
  }
};

// Get workflow by ID
const getWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    // Get workflow from Vapi API
    const response = await axios.get(`${VAPI_BASE_URL}/workflow/${id}`, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    const vapiWorkflow = response.data;

    // Get local workflow data
    const localWorkflow = await WorkflowModel.getWorkflowByWorkflowId(id);

    res.status(200).json({
      success: true,
      message: "Workflow retrieved successfully",
      data: {
        ...vapiWorkflow,
        localData: localWorkflow
      }
    });
  } catch (error) {
    console.error("Error getting workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Create workflow
const createWorkflow = async (req, res) => {
  try {
    const user_id = req.user.id;
    const workflowData = req.body;

    // Create workflow in Vapi API
    const response = await axios.post(`${VAPI_BASE_URL}/workflow`, workflowData, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    const vapiWorkflow = response.data;

    // Store workflow reference in local database
    const localWorkflowData = {
      user_id,
      workflow_id: vapiWorkflow.id,
      vapi_workflow_id: vapiWorkflow.id,
      org_id: vapiWorkflow.orgId || "synced_from_vapi",
      name: vapiWorkflow.name,
      description: workflowData.description || "",
      nodes: vapiWorkflow.nodes || [],
      edges: vapiWorkflow.edges || [],
      model: vapiWorkflow.model || null,
      transcriber: vapiWorkflow.transcriber || null,
      voice: vapiWorkflow.voice || null,
      global_prompt: vapiWorkflow.globalPrompt || "",
      background_sound: vapiWorkflow.backgroundSound || "off",
      credentials: vapiWorkflow.credentials || null,
      credential_ids: vapiWorkflow.credentialIds || null,
      variables: {},
      triggers: [],
      status: "active",
      version: "1.0.0",
      tags: [],
      metadata: {
        nodeCount: vapiWorkflow.nodes?.length || 0,
        edgeCount: vapiWorkflow.edges?.length || 0,
        lastModified: new Date().toISOString()
      },
      execution_count: 0
    };

    const localWorkflowId = await WorkflowModel.createWorkflow(localWorkflowData);

    res.status(201).json({
      success: true,
      message: "Workflow created successfully",
      data: {
        ...vapiWorkflow,
        localData: { id: localWorkflowId, ...localWorkflowData }
      }
    });
  } catch (error) {
    console.error("Error creating workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to create workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Update workflow
const updateWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;
    const workflowData = req.body;

    // Update workflow in Vapi API
    const response = await axios.patch(`${VAPI_BASE_URL}/workflow/${id}`, workflowData, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    const vapiWorkflow = response.data;

    // Update local workflow data
    const localWorkflowData = {
      name: vapiWorkflow.name,
      description: workflowData.description || "",
      metadata: {
        nodeCount: vapiWorkflow.nodes?.length || 0,
        edgeCount: vapiWorkflow.edges?.length || 0,
        lastModified: new Date().toISOString()
      }
    };

    await WorkflowModel.updateWorkflowByWorkflowId(id, localWorkflowData);

    res.status(200).json({
      success: true,
      message: "Workflow updated successfully",
      data: vapiWorkflow
    });
  } catch (error) {
    console.error("Error updating workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to update workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Delete workflow
const deleteWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const user_id = req.user.id;

    // Delete workflow from Vapi API
    await axios.delete(`${VAPI_BASE_URL}/workflow/${id}`, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    // Delete local workflow data
    await WorkflowModel.deleteWorkflowByWorkflowId(id);

    res.status(200).json({
      success: true,
      message: "Workflow deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to delete workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Test workflow (create call)
const testWorkflow = async (req, res) => {
  try {
    const { id } = req.params;
    const { phoneNumber, metadata = {} } = req.body;

    // Create call with workflow - using proper Vapi format
    const callData = {
      workflowId: id,
      phoneNumberId: process.env.VAPI_PHONE_NUMBER_ID, // Use configured phone number ID
      customer: {
        number: phoneNumber || "+15551234567" // Customer number to call
      },
      metadata: {
        ...metadata,
        testCall: true,
        timestamp: new Date().toISOString()
      }
    };

    const call = await VapiService.createCall(callData);

    res.status(200).json({
      success: true,
      message: "Workflow test call initiated successfully",
      data: call
    });
  } catch (error) {
    console.error("Error testing workflow:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Failed to test workflow",
      error: error.response?.data?.message || error.message
    });
  }
};

// Get workflow templates
const getWorkflowTemplates = async (req, res) => {
  try {
    const templates = [
      {
        id: "basic-greeting",
        name: "Basic Greeting Workflow",
        description: "Simple greeting workflow with information collection",
        category: "Basic",
        nodes: [
          {
            type: "conversation",
            name: "greeting",
            isStart: true,
            prompt: "Greet the user and ask how you can help them today.",
            firstMessage: "Hello! How can I assist you today?"
          }
        ],
        edges: []
      },
      {
        id: "appointment-scheduling",
        name: "Appointment Scheduling",
        description: "Complete appointment booking workflow",
        category: "Business",
        nodes: [
          {
            type: "conversation",
            name: "greeting",
            isStart: true,
            prompt: "Greet the user and ask about scheduling an appointment.",
            firstMessage: "Hello! I can help you schedule an appointment. What service are you interested in?"
          }
        ],
        edges: []
      },
      {
        id: "customer-support",
        name: "Customer Support",
        description: "Customer support workflow with escalation",
        category: "Support",
        nodes: [
          {
            type: "conversation",
            name: "support-greeting",
            isStart: true,
            prompt: "Greet the customer and ask about their issue.",
            firstMessage: "Hello! I'm here to help with any questions or issues you may have."
          }
        ],
        edges: []
      }
    ];

    res.status(200).json({
      success: true,
      message: "Workflow templates retrieved successfully",
      data: templates
    });
  } catch (error) {
    console.error("Error getting workflow templates:", error.message);
    res.status(500).json({
      success: false,
      message: "Failed to retrieve workflow templates",
      error: error.message
    });
  }
};

module.exports = {
  listWorkflows,
  getWorkflow,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  testWorkflow,
  getWorkflowTemplates
};
