const axios = require("axios");
require("dotenv").config({ path: "./config/config.env" });

const VAPI_BASE_URL = "https://api.vapi.ai";
// Use the same key as AssistantController for consistency
const VAPI_SECRET_KEY = process.env.VAPI_SECRET_KEY || "aa6161d2-7ba0-4182-96aa-fee4a9f14fd8";

console.log("VAPI_SECRET_KEY loaded:", VAPI_SECRET_KEY ? "✓" : "✗");
console.log("Environment VAPI_SECRET_KEY:", process.env.VAPI_SECRET_KEY ? "✓" : "✗");

// List all phone numbers
const listPhoneNumbers = async (req, res) => {
  try {
    console.log("Fetching phone numbers from VAPI...");
    console.log("Using API key:", VAPI_SECRET_KEY ? `${VAPI_SECRET_KEY.substring(0, 8)}...` : "MISSING");
    console.log("Request URL:", `${VAPI_BASE_URL}/phone-number`);

    const response = await axios.get(`${VAPI_BASE_URL}/phone-number`, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    console.log("Phone numbers fetched successfully:", response.data?.length || 0, "numbers");

    res.status(200).json({
      success: true,
      message: "Phone numbers retrieved successfully",
      data: response.data
    });
  } catch (error) {
    console.error("Error fetching phone numbers:");
    console.error("Status:", error.response?.status);
    console.error("Status Text:", error.response?.statusText);
    console.error("Error Data:", error.response?.data);
    console.error("Error Message:", error.message);

    const status = error.response?.status || 500;
    res.status(status).json({
      success: false,
      message: "Failed to fetch phone numbers",
      error: error.response?.data || error.message
    });
  }
};

// Get phone number by ID
const getPhoneNumber = async (req, res) => {
  try {
    const { id } = req.params;

    const response = await axios.get(`${VAPI_BASE_URL}/phone-number/${id}`, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    res.status(200).json({
      success: true,
      message: "Phone number retrieved successfully",
      data: response.data
    });
  } catch (error) {
    console.error("Error fetching phone number:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    res.status(status).json({
      success: false,
      message: "Failed to fetch phone number",
      error: error.response?.data || error.message
    });
  }
};

// Create phone number
const createPhoneNumber = async (req, res) => {
  try {
    const {
      twilioAccountSid,
      twilioAuthToken,
      twilioPhoneNumber,
      name,
      assistantId,
      squadId,
      workflowId,
      fallbackDestination,
      serverUrl,
      serverUrlSecret
    } = req.body;

    if (!twilioAccountSid || !twilioAuthToken || !twilioPhoneNumber) {
      return res.status(400).json({
        success: false,
        message: "Twilio account SID, auth token, and phone number are required"
      });
    }

    const phoneNumberData = {
      twilioAccountSid,
      twilioAuthToken,
      twilioPhoneNumber,
      name: name || `Phone Number ${twilioPhoneNumber}`,
      assistantId,
      squadId,
      workflowId,
      fallbackDestination,
      serverUrl,
      serverUrlSecret
    };

    const response = await axios.post(
      `${VAPI_BASE_URL}/phone-number`,
      phoneNumberData,
      {
        headers: {
          Authorization: `Bearer ${VAPI_SECRET_KEY}`,
          "Content-Type": "application/json"
        }
      }
    );

    res.status(201).json({
      success: true,
      message: "Phone number created successfully",
      data: response.data
    });
  } catch (error) {
    console.error("Error creating phone number:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    res.status(status).json({
      success: false,
      message: "Failed to create phone number",
      error: error.response?.data || error.message
    });
  }
};

// Update phone number
const updatePhoneNumber = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const response = await axios.patch(
      `${VAPI_BASE_URL}/phone-number/${id}`,
      updateData,
      {
        headers: {
          Authorization: `Bearer ${VAPI_SECRET_KEY}`,
          "Content-Type": "application/json"
        }
      }
    );

    res.status(200).json({
      success: true,
      message: "Phone number updated successfully",
      data: response.data
    });
  } catch (error) {
    console.error("Error updating phone number:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    res.status(status).json({
      success: false,
      message: "Failed to update phone number",
      error: error.response?.data || error.message
    });
  }
};

// Delete phone number
const deletePhoneNumber = async (req, res) => {
  try {
    const { id } = req.params;

    await axios.delete(`${VAPI_BASE_URL}/phone-number/${id}`, {
      headers: {
        Authorization: `Bearer ${VAPI_SECRET_KEY}`,
        "Content-Type": "application/json"
      }
    });

    res.status(200).json({
      success: true,
      message: "Phone number deleted successfully"
    });
  } catch (error) {
    console.error("Error deleting phone number:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    res.status(status).json({
      success: false,
      message: "Failed to delete phone number",
      error: error.response?.data || error.message
    });
  }
};

// Buy phone number (Twilio integration)
const buyPhoneNumber = async (req, res) => {
  try {
    const { areaCode, countryCode, phoneNumber, twilioAccountSid, twilioAuthToken } = req.body;

    if (!twilioAccountSid || !twilioAuthToken) {
      return res.status(400).json({
        success: false,
        message: "Twilio account SID and auth token are required"
      });
    }

    // This would integrate with Twilio to purchase a phone number
    // For now, we'll return a mock response
    const mockPurchasedNumber = {
      phoneNumber: phoneNumber || `+1${areaCode}5551234`,
      friendlyName: `Purchased Number ${Date.now()}`,
      capabilities: {
        voice: true,
        sms: true,
        mms: false
      },
      status: "purchased",
      cost: "$1.00"
    };

    res.status(201).json({
      success: true,
      message: "Phone number purchased successfully",
      data: mockPurchasedNumber
    });
  } catch (error) {
    console.error("Error purchasing phone number:", error);
    res.status(500).json({
      success: false,
      message: "Failed to purchase phone number",
      error: error.message
    });
  }
};

// Search available phone numbers
const searchPhoneNumbers = async (req, res) => {
  try {
    const { areaCode, countryCode, contains, nearLatLong } = req.query;

    // Mock available numbers - in real implementation, this would query Twilio
    const mockAvailableNumbers = [
      { phoneNumber: `+1${areaCode || '555'}1234567`, locality: "New York", region: "NY", cost: "$1.00" },
      { phoneNumber: `+1${areaCode || '555'}2345678`, locality: "New York", region: "NY", cost: "$1.00" },
      { phoneNumber: `+1${areaCode || '555'}3456789`, locality: "New York", region: "NY", cost: "$1.00" },
      { phoneNumber: `+1${areaCode || '555'}4567890`, locality: "New York", region: "NY", cost: "$1.00" },
      { phoneNumber: `+1${areaCode || '555'}5678901`, locality: "New York", region: "NY", cost: "$1.00" }
    ];

    res.status(200).json({
      success: true,
      message: "Available phone numbers retrieved successfully",
      data: mockAvailableNumbers
    });
  } catch (error) {
    console.error("Error searching phone numbers:", error);
    res.status(500).json({
      success: false,
      message: "Failed to search phone numbers",
      error: error.message
    });
  }
};

module.exports = {
  listPhoneNumbers,
  getPhoneNumber,
  createPhoneNumber,
  updatePhoneNumber,
  deletePhoneNumber,
  buyPhoneNumber,
  searchPhoneNumbers
};
