const axios = require("axios");
const crypto = require("crypto");
require("dotenv").config();

const VAPI_BASE_URL = "https://api.vapi.ai";
const VAPI_SECRET_KEY = process.env.VAPI_SECRET_KEY;

// Handle server message webhook
const handleServerMessage = async (req, res) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: "Message is required"
      });
    }

    // Verify webhook signature if provided
    const signature = req.headers['x-vapi-signature'];
    if (signature) {
      const isValid = verifyWebhookSignature(req.body, signature);
      if (!isValid) {
        return res.status(401).json({
          success: false,
          message: "Invalid webhook signature"
        });
      }
    }

    // Process different message types
    switch (message.type) {
      case "conversation-update":
        await handleConversationUpdate(message);
        break;
      case "function-call":
        await handleFunctionCall(message);
        break;
      case "hang":
        await handleHang(message);
        break;
      case "speech-update":
        await handleSpeechUpdate(message);
        break;
      case "status-update":
        await handleStatusUpdate(message);
        break;
      case "transcript":
        await handleTranscript(message);
        break;
      case "tool-calls":
        await handleToolCalls(message);
        break;
      case "transfer-destination-request":
        await handleTransferDestinationRequest(message);
        break;
      case "user-interrupted":
        await handleUserInterrupted(message);
        break;
      case "end-of-call-report":
        await handleEndOfCallReport(message);
        break;
      default:
        console.log(`Unknown message type: ${message.type}`);
    }

    res.status(200).json({
      success: true,
      message: "Webhook processed successfully"
    });
  } catch (error) {
    console.error("Error processing server message webhook:", error);
    res.status(500).json({
      success: false,
      message: "Failed to process webhook",
      error: error.message
    });
  }
};

// Handle client message webhook
const handleClientMessage = async (req, res) => {
  try {
    const { message } = req.body;

    if (!message) {
      return res.status(400).json({
        success: false,
        message: "Message is required"
      });
    }

    // Process client-side messages
    switch (message.type) {
      case "conversation-update":
        await handleClientConversationUpdate(message);
        break;
      case "function-call":
        await handleClientFunctionCall(message);
        break;
      case "hang":
        await handleClientHang(message);
        break;
      case "model-output":
        await handleModelOutput(message);
        break;
      case "speech-update":
        await handleClientSpeechUpdate(message);
        break;
      case "status-update":
        await handleClientStatusUpdate(message);
        break;
      case "transfer-update":
        await handleTransferUpdate(message);
        break;
      case "transcript":
        await handleClientTranscript(message);
        break;
      case "tool-calls":
        await handleClientToolCalls(message);
        break;
      case "user-interrupted":
        await handleClientUserInterrupted(message);
        break;
      case "voice-input":
        await handleVoiceInput(message);
        break;
      default:
        console.log(`Unknown client message type: ${message.type}`);
    }

    res.status(200).json({
      success: true,
      message: "Client webhook processed successfully"
    });
  } catch (error) {
    console.error("Error processing client message webhook:", error);
    res.status(500).json({
      success: false,
      message: "Failed to process client webhook",
      error: error.message
    });
  }
};

// Verify webhook signature
const verifyWebhookSignature = (payload, signature) => {
  try {
    const secret = process.env.VAPI_WEBHOOK_SECRET || "your-webhook-secret";
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(JSON.stringify(payload))
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    console.error("Error verifying webhook signature:", error);
    return false;
  }
};

// Message handlers
const handleConversationUpdate = async (message) => {
  console.log("Conversation update:", message);
  // Store conversation updates in database
};

const handleFunctionCall = async (message) => {
  console.log("Function call:", message);
  // Handle function calls and return responses
};

const handleHang = async (message) => {
  console.log("Call hang:", message);
  // Handle call hang events
};

const handleSpeechUpdate = async (message) => {
  console.log("Speech update:", message);
  // Handle speech updates
};

const handleStatusUpdate = async (message) => {
  console.log("Status update:", message);
  // Handle status updates
};

const handleTranscript = async (message) => {
  console.log("Transcript:", message);
  // Store transcripts in database
};

const handleToolCalls = async (message) => {
  console.log("Tool calls:", message);
  // Handle tool calls
};

const handleTransferDestinationRequest = async (message) => {
  console.log("Transfer destination request:", message);
  // Handle transfer requests
};

const handleUserInterrupted = async (message) => {
  console.log("User interrupted:", message);
  // Handle user interruptions
};

const handleEndOfCallReport = async (message) => {
  console.log("End of call report:", message);
  // Store call reports and analytics
};

// Client message handlers
const handleClientConversationUpdate = async (message) => {
  console.log("Client conversation update:", message);
};

const handleClientFunctionCall = async (message) => {
  console.log("Client function call:", message);
};

const handleClientHang = async (message) => {
  console.log("Client hang:", message);
};

const handleModelOutput = async (message) => {
  console.log("Model output:", message);
};

const handleClientSpeechUpdate = async (message) => {
  console.log("Client speech update:", message);
};

const handleClientStatusUpdate = async (message) => {
  console.log("Client status update:", message);
};

const handleTransferUpdate = async (message) => {
  console.log("Transfer update:", message);
};

const handleClientTranscript = async (message) => {
  console.log("Client transcript:", message);
};

const handleClientToolCalls = async (message) => {
  console.log("Client tool calls:", message);
};

const handleClientUserInterrupted = async (message) => {
  console.log("Client user interrupted:", message);
};

const handleVoiceInput = async (message) => {
  console.log("Voice input:", message);
};

// Get webhook configuration
const getWebhookConfig = async (req, res) => {
  try {
    const webhookConfig = {
      serverUrl: process.env.VAPI_SERVER_URL || "https://your-server.com/api/webhooks/server",
      serverSecret: process.env.VAPI_WEBHOOK_SECRET || "your-webhook-secret",
      supportedEvents: [
        "conversation-update",
        "function-call",
        "hang",
        "speech-update",
        "status-update",
        "transcript",
        "tool-calls",
        "transfer-destination-request",
        "user-interrupted",
        "end-of-call-report"
      ],
      clientEvents: [
        "conversation-update",
        "function-call",
        "hang",
        "model-output",
        "speech-update",
        "status-update",
        "transfer-update",
        "transcript",
        "tool-calls",
        "user-interrupted",
        "voice-input"
      ]
    };

    res.status(200).json({
      success: true,
      message: "Webhook configuration retrieved successfully",
      data: webhookConfig
    });
  } catch (error) {
    console.error("Error fetching webhook configuration:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch webhook configuration",
      error: error.message
    });
  }
};

// Test webhook
const testWebhook = async (req, res) => {
  try {
    const { url, secret, eventType } = req.body;

    if (!url || !eventType) {
      return res.status(400).json({
        success: false,
        message: "URL and event type are required"
      });
    }

    // Create test payload
    const testPayload = {
      message: {
        type: eventType,
        timestamp: new Date().toISOString(),
        callId: "test_call_123",
        data: {
          test: true,
          message: "This is a test webhook"
        }
      }
    };

    // Send test webhook
    const response = await axios.post(url, testPayload, {
      headers: {
        "Content-Type": "application/json",
        "X-Vapi-Signature": secret ? crypto
          .createHmac('sha256', secret)
          .update(JSON.stringify(testPayload))
          .digest('hex') : undefined
      },
      timeout: 5000
    });

    res.status(200).json({
      success: true,
      message: "Webhook test completed successfully",
      data: {
        url,
        eventType,
        responseStatus: response.status,
        responseTime: Date.now() - Date.now() // This would be calculated properly
      }
    });
  } catch (error) {
    console.error("Error testing webhook:", error);
    res.status(500).json({
      success: false,
      message: "Webhook test failed",
      error: error.message
    });
  }
};

module.exports = {
  handleServerMessage,
  handleClientMessage,
  getWebhookConfig,
  testWebhook
};
