const axios = require("axios");
require("dotenv").config();


const createCall = async (req, res) => {
  const { id: assistantId, phoneNumber } = req.body;
  if (!assistantId || !phoneNumber) {
    return res.status(400).json({
      success: false,
      message: 'assistantId and phoneNumber (+E.164) are required',
    });
  }
  // normalize to +E.164
  let dest = phoneNumber.replace(/\D/g, '');
  if (!dest.startsWith('+')) dest = '+' + dest;

  const payload = {
    assistantId,
    customer: { number: dest },
    phoneNumberId: process.env.VAPI_PHONE_NUMBER_ID  // ← string ID, not an object
  };

  try {
    const { data } = await axios.post(
      'https://api.vapi.ai/call/phone',
      payload,
      {
        headers: {
          Authorization: `Bearer ${process.env.VAPI_SECRET_KEY}`,
          'Content-Type': 'application/json',
        },
      }
    );
    return res.status(201).json({
      success: true,
      callId:  data.id,
      status:  data.status,
    });
  } catch (err) {
    console.error('Vapi call error:', err.response?.data || err.message);
    const status  = err.response?.status || 500;
    const message = err.response?.data?.message || 'Failed to create call';
    return res.status(status).json({ success: false, message, error: err.response?.data });
  }
};




const getAllCalls = async (req, res) => {
  try {
    // Try to fetch from VAPI first
    try {
      const vapiResponse = await axios.get("https://api.vapi.ai/call", {
        headers: {
          Authorization: `Bearer ${process.env.VAPI_SECRET_KEY}`,
          "Content-Type": "application/json",
        },
      });

      // Add recording URLs to the calls
      const callsWithRecordings = vapiResponse.data.map(call => ({
        ...call,
        recordingUrl: call.status === "ended" ? `/api/outboundcall/recording/${call.id}` : null,
        recording: call.status === "ended" ? "available" : null
      }));

      return res.status(200).json({
        success: true,
        message: "List of calls retrieved successfully",
        data: callsWithRecordings,
      });
    } catch (error) {
      // If VAPI fails, return mock data for demo
      if (error.response?.status === 401 || error.response?.status === 403) {
        const mockCalls = [
          {
            id: "call_1",
            assistantId: "asst_demo_1",
            phoneNumber: "+1234567890",
            status: "ended",
            startedAt: new Date(Date.now() - 3600000).toISOString(),
            endedAt: new Date(Date.now() - 3300000).toISOString(),
            duration: 300,
            cost: 0.25,
            endedReason: "customer-ended-call",
            recordingUrl: `/api/outboundcall/recording/call_1`,
            recording: "available",
            transcript: "Hello, this is a demo call transcript...",
            createdAt: new Date(Date.now() - 3600000).toISOString(),
            updatedAt: new Date(Date.now() - 3300000).toISOString()
          },
          {
            id: "call_2",
            assistantId: "asst_demo_2",
            phoneNumber: "+1987654321",
            status: "ended",
            startedAt: new Date(Date.now() - 7200000).toISOString(),
            endedAt: new Date(Date.now() - 6900000).toISOString(),
            duration: 450,
            cost: 0.38,
            endedReason: "assistant-ended-call",
            recordingUrl: `/api/outboundcall/recording/call_2`,
            recording: "available",
            transcript: "Thank you for calling. This is another demo transcript...",
            createdAt: new Date(Date.now() - 7200000).toISOString(),
            updatedAt: new Date(Date.now() - 6900000).toISOString()
          },
          {
            id: "call_3",
            assistantId: "asst_demo_1",
            phoneNumber: "+1555123456",
            status: "in-progress",
            startedAt: new Date(Date.now() - 300000).toISOString(),
            duration: 300,
            cost: 0,
            recordingUrl: null,
            recording: null,
            createdAt: new Date(Date.now() - 300000).toISOString(),
            updatedAt: new Date(Date.now() - 60000).toISOString()
          }
        ];

        return res.status(200).json({
          success: true,
          message: "List of calls retrieved successfully (demo data)",
          data: mockCalls,
        });
      }
      throw error;
    }
  } catch (error) {
    console.error("Error listing calls:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || "Failed to fetch calls";
    return res.status(status).json({
      success: false,
      message,
      error: error.response?.data || error.message,
    });
  }
};
const getCallsbyID = async (req, res) => {
  const { id } = req.params;
  try {
    const vapiResponse = await axios.get(`https://api.vapi.ai/call/${id}`, {
      headers: {
        Authorization: `Bearer ${process.env.VAPI_SECRET_KEY}`,
        "Content-Type": "application/json",
      },
    });

    
    return res.status(200).json({
      success: true,
      message: "Call details retrieved successfully",
      data: vapiResponse.data,
    });
  } catch (error) {
    console.error("Error fetching call:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || "Failed to fetch call details";
    return res.status(status).json({
      success: false,
      message,
      error: error.response?.data || error.message,
    });
  }
};

const getCallRecording = async (req, res) => {
  const { id } = req.params;
  try {
    // For demo purposes, return a mock recording URL
    // In production, this would fetch the actual recording from VAPI
    const mockRecordingData = {
      recordingUrl: `/api/recordings/${id}/stream`,
      downloadUrl: `/api/recordings/${id}/download`,
      format: "mp3",
      duration: "15:30",
      fileSize: "12.5 MB",
      status: "available"
    };

    return res.status(200).json({
      success: true,
      message: "Call recording retrieved successfully",
      data: mockRecordingData,
    });
  } catch (error) {
    console.error("Error fetching call recording:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || "Failed to fetch call recording";
    return res.status(status).json({
      success: false,
      message,
      error: error.response?.data || error.message,
    });
  }
};

// End call
const endCall = async (req, res) => {
  const { id } = req.params;
  try {
    // Try to end the call via VAPI
    const vapiResponse = await axios.patch(`https://api.vapi.ai/call/${id}`,
      { status: "ended" },
      {
        headers: {
          Authorization: `Bearer ${process.env.VAPI_SECRET_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    return res.status(200).json({
      success: true,
      message: "Call ended successfully",
      data: vapiResponse.data,
    });
  } catch (error) {
    console.error("Error ending call:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || "Failed to end call";
    return res.status(status).json({
      success: false,
      message,
      error: error.response?.data || error.message,
    });
  }
};

// Get call transcript
const getCallTranscript = async (req, res) => {
  const { id } = req.params;
  try {
    // For demo purposes, return mock transcript data
    // In production, this would fetch from VAPI or your transcript service
    const mockTranscript = {
      callId: id,
      transcript: "Hello, this is a sample transcript of the call. The conversation included discussion about the product features and pricing.",
      confidence: 0.95,
      language: "en-US",
      duration: "15:30",
      wordCount: 150,
      generatedAt: new Date().toISOString()
    };

    return res.status(200).json({
      success: true,
      message: "Call transcript retrieved successfully",
      data: mockTranscript,
    });
  } catch (error) {
    console.error("Error fetching call transcript:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || "Failed to fetch call transcript";
    return res.status(status).json({
      success: false,
      message,
      error: error.response?.data || error.message,
    });
  }
};

// Get call statistics
const getCallStats = async (req, res) => {
  try {
    // For demo purposes, return mock statistics
    // In production, this would aggregate data from your database or VAPI
    const mockStats = {
      totalCalls: 1247,
      successfulCalls: 1089,
      failedCalls: 158,
      averageDuration: 185, // seconds
      totalCost: 234.56,
      callsByStatus: [
        { status: "completed", count: 1089 },
        { status: "failed", count: 158 },
        { status: "in-progress", count: 12 },
        { status: "queued", count: 8 }
      ],
      callsByHour: Array.from({ length: 24 }, (_, i) => ({
        hour: i,
        count: Math.floor(Math.random() * 50) + 10
      })),
      callsByDay: Array.from({ length: 7 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        count: Math.floor(Math.random() * 200) + 50
      }))
    };

    return res.status(200).json({
      success: true,
      message: "Call statistics retrieved successfully",
      data: mockStats,
    });
  } catch (error) {
    console.error("Error fetching call stats:", error.response?.data || error.message);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || "Failed to fetch call statistics";
    return res.status(status).json({
      success: false,
      message,
      error: error.response?.data || error.message,
    });
  }
};

// Create bulk outbound calls
const createBulkCalls = async (req, res) => {
  const { assistantId, phoneNumbers, metadata, maxDurationSeconds, firstMessage, voicemailMessage } = req.body;

  if (!assistantId || !phoneNumbers || !Array.isArray(phoneNumbers) || phoneNumbers.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'assistantId and phoneNumbers array are required',
    });
  }

  const results = [];
  const errors = [];

  for (const phoneNumber of phoneNumbers) {
    try {
      // Normalize to +E.164
      let dest = phoneNumber.replace(/\D/g, '');
      if (!dest.startsWith('+')) dest = '+' + dest;

      const payload = {
        assistantId,
        customer: { number: dest },
        phoneNumberId: process.env.VAPI_PHONE_NUMBER_ID,
        metadata,
        maxDurationSeconds,
        firstMessage,
        voicemailMessage
      };

      const { data } = await axios.post(
        'https://api.vapi.ai/call/phone',
        payload,
        {
          headers: {
            Authorization: `Bearer ${process.env.VAPI_SECRET_KEY}`,
            'Content-Type': 'application/json',
          },
        }
      );

      results.push({
        phoneNumber: dest,
        callId: data.id,
        status: data.status,
        success: true
      });
    } catch (error) {
      errors.push({
        phoneNumber,
        error: error.response?.data?.message || error.message,
        success: false
      });
    }
  }

  return res.status(201).json({
    success: true,
    message: `Bulk calls initiated: ${results.length} successful, ${errors.length} failed`,
    data: {
      successful: results,
      failed: errors,
      totalRequested: phoneNumbers.length,
      totalSuccessful: results.length,
      totalFailed: errors.length
    }
  });
};

// Schedule outbound call
const scheduleCall = async (req, res) => {
  const { assistantId, phoneNumber, scheduledAt, metadata, maxDurationSeconds, firstMessage, voicemailMessage } = req.body;

  if (!assistantId || !phoneNumber || !scheduledAt) {
    return res.status(400).json({
      success: false,
      message: 'assistantId, phoneNumber, and scheduledAt are required',
    });
  }

  // For demo purposes, we'll just return a scheduled call object
  // In production, you would store this in a database and use a job scheduler
  const scheduledCall = {
    id: `scheduled_${Date.now()}`,
    assistantId,
    phoneNumber,
    scheduledAt,
    status: "scheduled",
    metadata,
    maxDurationSeconds,
    firstMessage,
    voicemailMessage,
    createdAt: new Date().toISOString()
  };

  return res.status(201).json({
    success: true,
    message: "Call scheduled successfully",
    data: scheduledCall
  });
};

module.exports = {
  createCall,
  getAllCalls,
  getCallsbyID,
  getCallRecording,
  endCall,
  getCallTranscript,
  getCallStats,
  createBulkCalls,
  scheduleCall,
};
