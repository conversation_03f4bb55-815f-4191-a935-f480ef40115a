const axios = require("axios");
require("dotenv").config({ path: "./config/config.env" });

const VAPI_BASE_URL = "https://api.vapi.ai";
const VAPI_SECRET_KEY = process.env.VAPI_SECRET_KEY || "aa6161d2-7ba0-4182-96aa-fee4a9f14fd8";

console.log("Voices Controller - VAPI_SECRET_KEY loaded:", VAPI_SECRET_KEY ? "✓" : "✗");

// Get available voices from different providers
const getAvailableVoices = async (req, res) => {
  try {
    const supportedVoices = {
      "11labs": [
        { id: "sarah", name: "<PERSON>", provider: "11labs", gender: "female", accent: "american", description: "Warm and professional" },
        { id: "rachel", name: "<PERSON>", provider: "11labs", gender: "female", accent: "american", description: "Clear and articulate" },
        { id: "domi", name: "<PERSON><PERSON>", provider: "11labs", gender: "female", accent: "american", description: "Energetic and friendly" },
        { id: "bella", name: "<PERSON>", provider: "11labs", gender: "female", accent: "american", description: "Soft and gentle" },
        { id: "antoni", name: "Antoni", provider: "11labs", gender: "male", accent: "american", description: "Deep and authoritative" },
        { id: "elli", name: "Elli", provider: "11labs", gender: "female", accent: "american", description: "Young and vibrant" },
        { id: "josh", name: "Josh", provider: "11labs", gender: "male", accent: "american", description: "Casual and friendly" },
        { id: "arnold", name: "Arnold", provider: "11labs", gender: "male", accent: "american", description: "Strong and confident" },
        { id: "adam", name: "Adam", provider: "11labs", gender: "male", accent: "american", description: "Professional and clear" },
        { id: "sam", name: "Sam", provider: "11labs", gender: "male", accent: "american", description: "Versatile and natural" }
      ],
      "playht": [
        { id: "jennifer", name: "Jennifer", provider: "playht", gender: "female", accent: "american", description: "Professional news anchor" },
        { id: "melissa", name: "Melissa", provider: "playht", gender: "female", accent: "american", description: "Warm and conversational" },
        { id: "will", name: "Will", provider: "playht", gender: "male", accent: "american", description: "Authoritative and clear" },
        { id: "chris", name: "Chris", provider: "playht", gender: "male", accent: "american", description: "Friendly and approachable" },
        { id: "matt", name: "Matt", provider: "playht", gender: "male", accent: "american", description: "Professional and reliable" },
        { id: "jack", name: "Jack", provider: "playht", gender: "male", accent: "american", description: "Energetic and engaging" },
        { id: "ruby", name: "Ruby", provider: "playht", gender: "female", accent: "british", description: "Elegant British accent" },
        { id: "davis", name: "Davis", provider: "playht", gender: "male", accent: "american", description: "Deep and resonant" }
      ],
      "azure": [
        { id: "andrew", name: "Andrew", provider: "azure", gender: "male", accent: "american", description: "Natural and expressive" },
        { id: "brian", name: "Brian", provider: "azure", gender: "male", accent: "american", description: "Professional and clear" },
        { id: "emma", name: "Emma", provider: "azure", gender: "female", accent: "american", description: "Warm and friendly" },
        { id: "jenny", name: "Jenny", provider: "azure", gender: "female", accent: "american", description: "Conversational and natural" },
        { id: "guy", name: "Guy", provider: "azure", gender: "male", accent: "american", description: "Confident and articulate" },
        { id: "aria", name: "Aria", provider: "azure", gender: "female", accent: "american", description: "Expressive and engaging" },
        { id: "davis", name: "Davis", provider: "azure", gender: "male", accent: "american", description: "Professional narrator" },
        { id: "jane", name: "Jane", provider: "azure", gender: "female", accent: "american", description: "Clear and pleasant" }
      ],
      "openai": [
        { id: "alloy", name: "Alloy", provider: "openai", gender: "neutral", accent: "american", description: "Balanced and versatile" },
        { id: "echo", name: "Echo", provider: "openai", gender: "male", accent: "american", description: "Clear and direct" },
        { id: "fable", name: "Fable", provider: "openai", gender: "female", accent: "british", description: "Storytelling voice" },
        { id: "onyx", name: "Onyx", provider: "openai", gender: "male", accent: "american", description: "Deep and authoritative" },
        { id: "nova", name: "Nova", provider: "openai", gender: "female", accent: "american", description: "Bright and energetic" },
        { id: "shimmer", name: "Shimmer", provider: "openai", gender: "female", accent: "american", description: "Soft and gentle" }
      ],
      "deepgram": [
        { id: "aura-asteria-en", name: "Asteria", provider: "deepgram", gender: "female", accent: "american", description: "Professional and clear" },
        { id: "aura-luna-en", name: "Luna", provider: "deepgram", gender: "female", accent: "american", description: "Warm and conversational" },
        { id: "aura-stella-en", name: "Stella", provider: "deepgram", gender: "female", accent: "american", description: "Friendly and approachable" },
        { id: "aura-athena-en", name: "Athena", provider: "deepgram", gender: "female", accent: "american", description: "Intelligent and articulate" },
        { id: "aura-hera-en", name: "Hera", provider: "deepgram", gender: "female", accent: "american", description: "Authoritative and confident" },
        { id: "aura-orion-en", name: "Orion", provider: "deepgram", gender: "male", accent: "american", description: "Strong and reliable" },
        { id: "aura-arcas-en", name: "Arcas", provider: "deepgram", gender: "male", accent: "american", description: "Professional and trustworthy" },
        { id: "aura-perseus-en", name: "Perseus", provider: "deepgram", gender: "male", accent: "american", description: "Dynamic and engaging" }
      ]
    };

    const { provider } = req.query;
    
    if (provider && supportedVoices[provider]) {
      res.status(200).json({
        success: true,
        message: `Available voices for ${provider} retrieved successfully`,
        data: { [provider]: supportedVoices[provider] }
      });
    } else {
      res.status(200).json({
        success: true,
        message: "All available voices retrieved successfully",
        data: supportedVoices
      });
    }
  } catch (error) {
    console.error("Error fetching available voices:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch available voices",
      error: error.message
    });
  }
};

// Test voice with sample text
const testVoice = async (req, res) => {
  try {
    const { provider, voiceId, text, speed, pitch, stability } = req.body;

    if (!provider || !voiceId || !text) {
      return res.status(400).json({
        success: false,
        message: "Provider, voiceId, and text are required"
      });
    }

    // Create a test assistant with the specified voice
    const testAssistant = {
      model: {
        provider: "openai",
        model: "gpt-3.5-turbo"
      },
      voice: {
        provider,
        voiceId,
        speed: speed || 1,
        pitch: pitch || 1,
        stability: stability || 0.5
      },
      firstMessage: text
    };

    const response = await axios.post(
      `${VAPI_BASE_URL}/assistant`,
      testAssistant,
      {
        headers: {
          Authorization: `Bearer ${VAPI_SECRET_KEY}`,
          "Content-Type": "application/json"
        }
      }
    );

    // Clean up - delete the test assistant
    if (response.data.id) {
      try {
        await axios.delete(`${VAPI_BASE_URL}/assistant/${response.data.id}`, {
          headers: {
            Authorization: `Bearer ${VAPI_SECRET_KEY}`
          }
        });
      } catch (deleteError) {
        console.warn("Failed to delete test assistant:", deleteError.message);
      }
    }

    res.status(200).json({
      success: true,
      message: "Voice test completed successfully",
      data: {
        provider,
        voiceId,
        testText: text,
        status: "working"
      }
    });
  } catch (error) {
    console.error("Error testing voice:", error.response?.data || error.message);
    res.status(500).json({
      success: false,
      message: "Voice test failed",
      error: error.response?.data || error.message
    });
  }
};

// Create voice configuration
const createVoiceConfig = async (req, res) => {
  try {
    const { provider, voiceId, speed, pitch, stability, similarity, style, useCache } = req.body;

    if (!provider || !voiceId) {
      return res.status(400).json({
        success: false,
        message: "Provider and voiceId are required"
      });
    }

    const voiceConfig = {
      provider,
      voiceId,
      speed: speed || 1,
      pitch: pitch || 1,
      stability: stability || 0.5,
      similarity: similarity || 0.75,
      style: style || 0,
      useCache: useCache !== false
    };

    res.status(201).json({
      success: true,
      message: "Voice configuration created successfully",
      data: voiceConfig
    });
  } catch (error) {
    console.error("Error creating voice configuration:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create voice configuration",
      error: error.message
    });
  }
};

// Get voice usage statistics
const getVoiceStats = async (req, res) => {
  try {
    const mockStats = {
      totalCharacters: 125000,
      totalRequests: 850,
      averageResponseTime: 650,
      topVoices: [
        { voiceId: "sarah", provider: "11labs", usage: 35 },
        { voiceId: "andrew", provider: "azure", usage: 25 },
        { voiceId: "alloy", provider: "openai", usage: 20 },
        { voiceId: "jennifer", provider: "playht", usage: 20 }
      ],
      costBreakdown: {
        "11labs": 25.50,
        "azure": 15.25,
        "openai": 12.75,
        "playht": 18.50,
        "deepgram": 8.25,
        total: 80.25
      }
    };

    res.status(200).json({
      success: true,
      message: "Voice statistics retrieved successfully",
      data: mockStats
    });
  } catch (error) {
    console.error("Error fetching voice statistics:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch voice statistics",
      error: error.message
    });
  }
};

module.exports = {
  getAvailableVoices,
  testVoice,
  createVoiceConfig,
  getVoiceStats
};
