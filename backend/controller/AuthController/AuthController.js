const jwt = require("jsonwebtoken");
require("dotenv").config();

const secretKey = "ASAJKLDSLKDJLASJDLA";

// Demo login for testing workflows
const demoLogin = async (req, res) => {
  try {
    // Create a demo user token
    const demoUser = {
      id: 999999, // Numeric ID for database compatibility
      email: '<EMAIL>',
      name: 'Demo User',
      org_id: 'demo_org',
      role: 'admin'
    };

    // Generate JWT token
    const token = jwt.sign(demoUser, secretKey, { expiresIn: '24h' });

    res.status(200).json({
      success: true,
      message: "Demo login successful",
      data: {
        token,
        user: demoUser
      }
    });
  } catch (error) {
    console.error("Error in demo login:", error.message);
    res.status(500).json({
      success: false,
      message: "Demo login failed",
      error: error.message
    });
  }
};

// Get current user info
const getCurrentUser = async (req, res) => {
  try {
    const user = req.user;
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: "User not authenticated"
      });
    }

    res.status(200).json({
      success: true,
      message: "User info retrieved successfully",
      data: user
    });
  } catch (error) {
    console.error("Error getting current user:", error.message);
    res.status(500).json({
      success: false,
      message: "Failed to get user info",
      error: error.message
    });
  }
};

module.exports = {
  demoLogin,
  getCurrentUser
};
