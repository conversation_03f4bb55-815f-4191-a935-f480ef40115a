const express = require("express");
const webhooksController = require("../../controller/WebhooksController/WebhooksController");
const authMiddleware = require("../../middleware/authMiddleware");

const router = express.Router();

// Handle server message webhook (no auth middleware for webhooks from VAPI)
router.post("/server", webhooksController.handleServerMessage);

// Handle client message webhook (no auth middleware for webhooks from VAPI)
router.post("/client", webhooksController.handleClientMessage);

// Get webhook configuration (requires auth)
router.get("/config", authMiddleware, webhooksController.getWebhookConfig);

// Test webhook (requires auth)
router.post("/test", authMiddleware, webhooksController.testWebhook);

module.exports = router;
