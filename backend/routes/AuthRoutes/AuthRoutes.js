const express = require("express");
const authController = require("../../controller/AuthController/AuthController");
const authMiddleware = require("../../middleware/authMiddleware");

const router = express.Router();

// Demo login (no auth required)
router.post("/demo-login", authController.demoLogin);

// Get current user (auth required)
router.get("/me", authMiddleware, authController.getCurrentUser);

module.exports = router;
