const express = require("express");
const outboundCall = require("../../controller/outboundCallController/outboundCall");
const authMiddleware = require("../../middleware/authMiddleware");

const router = express.Router();


router.post("/createCall", authMiddleware, outboundCall.createCall);


router.get("/getAllCalls", authMiddleware, outboundCall.getAllCalls);

router.get("/getCallsbyID/:id", authMiddleware, outboundCall.getCallsbyID);

// End call
router.post("/endCall/:id", authMiddleware, outboundCall.endCall);

// Get call transcript
router.get("/transcript/:id", authMiddleware, outboundCall.getCallTranscript);

// Get call statistics
router.get("/stats", authMiddleware, outboundCall.getCallStats);

// Bulk create calls
router.post("/createBulkCalls", authMiddleware, outboundCall.createBulkCalls);

// Schedule call
router.post("/schedule", authMiddleware, outboundCall.scheduleCall);

// Recording endpoints for outbound calls
router.get("/recording/:id", authMiddleware, outboundCall.getCallRecording);

module.exports = router;
