const express = require('express');
const router = express.Router();
const vapiWorkflowController = require('../src/controllers/vapiWorkflowController');

/**
 * @swagger
 * components:
 *   schemas:
 *     VapiWorkflow:
 *       type: object
 *       required:
 *         - name
 *         - nodes
 *         - edges
 *       properties:
 *         id:
 *           type: string
 *           description: Unique identifier for the workflow
 *         name:
 *           type: string
 *           maxLength: 80
 *           description: Name of the workflow
 *         description:
 *           type: string
 *           description: Description of the workflow
 *         status:
 *           type: string
 *           enum: [draft, active, archived]
 *           description: Status of the workflow
 *         nodes:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/VapiWorkflowNode'
 *         edges:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/VapiWorkflowEdge'
 *         model:
 *           type: object
 *           description: Model configuration for the workflow
 *         transcriber:
 *           type: object
 *           description: Transcriber configuration for the workflow
 *         voice:
 *           type: object
 *           description: Voice configuration for the workflow
 *         globalPrompt:
 *           type: string
 *           maxLength: 5000
 *           description: Global prompt for the workflow
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *     
 *     VapiWorkflowNode:
 *       type: object
 *       required:
 *         - type
 *         - name
 *       properties:
 *         id:
 *           type: string
 *         type:
 *           type: string
 *           enum: [conversation, apiRequest, transferCall, endCall, tool]
 *         name:
 *           type: string
 *         position:
 *           type: object
 *           properties:
 *             x:
 *               type: number
 *             y:
 *               type: number
 *         isStart:
 *           type: boolean
 *         firstMessage:
 *           type: string
 *         prompt:
 *           type: string
 *         extractVariables:
 *           type: array
 *           items:
 *             type: object
 *         url:
 *           type: string
 *         method:
 *           type: string
 *           enum: [GET, POST, PUT, DELETE, PATCH]
 *         destination:
 *           type: string
 *         toolId:
 *           type: string
 *     
 *     VapiWorkflowEdge:
 *       type: object
 *       required:
 *         - from
 *         - to
 *       properties:
 *         id:
 *           type: string
 *         from:
 *           type: string
 *         to:
 *           type: string
 *         condition:
 *           type: string
 *         conditionType:
 *           type: string
 *           enum: [ai, logical, combined]
 *     
 *     VapiWorkflowExecution:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         workflowId:
 *           type: string
 *         status:
 *           type: string
 *           enum: [running, completed, failed, cancelled]
 *         phoneNumber:
 *           type: string
 *         startedAt:
 *           type: string
 *           format: date-time
 *         completedAt:
 *           type: string
 *           format: date-time
 *         error:
 *           type: string
 */

/**
 * @swagger
 * /api/vapi/workflows:
 *   get:
 *     summary: Get all VAPI workflows
 *     tags: [VAPI Workflows]
 *     responses:
 *       200:
 *         description: List of VAPI workflows
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/VapiWorkflow'
 *       500:
 *         description: Server error
 */
router.get('/', vapiWorkflowController.getAllWorkflows);

/**
 * @swagger
 * /api/vapi/workflows/{id}:
 *   get:
 *     summary: Get VAPI workflow by ID
 *     tags: [VAPI Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: VAPI workflow details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/VapiWorkflow'
 *       404:
 *         description: Workflow not found
 *       500:
 *         description: Server error
 */
router.get('/:id', vapiWorkflowController.getWorkflowById);

/**
 * @swagger
 * /api/vapi/workflows:
 *   post:
 *     summary: Create new VAPI workflow
 *     tags: [VAPI Workflows]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - nodes
 *               - edges
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 80
 *               description:
 *                 type: string
 *               nodes:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/VapiWorkflowNode'
 *               edges:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/VapiWorkflowEdge'
 *               model:
 *                 type: object
 *               transcriber:
 *                 type: object
 *               voice:
 *                 type: object
 *               globalPrompt:
 *                 type: string
 *                 maxLength: 5000
 *     responses:
 *       201:
 *         description: VAPI workflow created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/VapiWorkflow'
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Server error
 */
router.post('/', vapiWorkflowController.createWorkflow);

/**
 * @swagger
 * /api/vapi/workflows/{id}:
 *   put:
 *     summary: Update VAPI workflow
 *     tags: [VAPI Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VapiWorkflow'
 *     responses:
 *       200:
 *         description: VAPI workflow updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/VapiWorkflow'
 *       404:
 *         description: Workflow not found
 *       500:
 *         description: Server error
 */
router.put('/:id', vapiWorkflowController.updateWorkflow);

/**
 * @swagger
 * /api/vapi/workflows/{id}/auto-save:
 *   patch:
 *     summary: Auto-save VAPI workflow
 *     tags: [VAPI Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               nodes:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/VapiWorkflowNode'
 *               edges:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/VapiWorkflowEdge'
 *     responses:
 *       200:
 *         description: VAPI workflow auto-saved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/VapiWorkflow'
 *       404:
 *         description: Workflow not found
 *       500:
 *         description: Server error
 */
router.patch('/:id/auto-save', vapiWorkflowController.autoSaveWorkflow);

/**
 * @swagger
 * /api/vapi/workflows/{id}:
 *   delete:
 *     summary: Delete VAPI workflow
 *     tags: [VAPI Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       204:
 *         description: VAPI workflow deleted successfully
 *       404:
 *         description: Workflow not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', vapiWorkflowController.deleteWorkflow);

/**
 * @swagger
 * /api/vapi/workflows/{id}/execute:
 *   post:
 *     summary: Execute/Test VAPI workflow
 *     tags: [VAPI Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 description: Phone number to call for testing
 *     responses:
 *       200:
 *         description: VAPI workflow execution started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/VapiWorkflowExecution'
 *       404:
 *         description: Workflow not found
 *       500:
 *         description: Server error
 */
router.post('/:id/execute', vapiWorkflowController.executeWorkflow);

/**
 * @swagger
 * /api/vapi/workflows/{id}/validate:
 *   post:
 *     summary: Validate VAPI workflow structure
 *     tags: [VAPI Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID
 *     responses:
 *       200:
 *         description: Workflow validation result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isValid:
 *                   type: boolean
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       404:
 *         description: Workflow not found
 *       500:
 *         description: Server error
 */
router.post('/:id/validate', vapiWorkflowController.validateWorkflow);

/**
 * @swagger
 * /api/vapi/workflows/{id}/clone:
 *   post:
 *     summary: Clone VAPI workflow
 *     tags: [VAPI Workflows]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Workflow ID to clone
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name for the cloned workflow
 *     responses:
 *       201:
 *         description: VAPI workflow cloned successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/VapiWorkflow'
 *       404:
 *         description: Workflow not found
 *       500:
 *         description: Server error
 */
router.post('/:id/clone', vapiWorkflowController.cloneWorkflow);

module.exports = router;
