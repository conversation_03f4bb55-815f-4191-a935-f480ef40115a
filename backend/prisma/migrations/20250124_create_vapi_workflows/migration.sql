-- CreateTable
CREATE TABLE "VapiWorkflow" (
    "id" TEXT NOT NULL,
    "orgId" TEXT,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "status" TEXT NOT NULL DEFAULT 'draft',
    "model" TEXT,
    "transcriber" TEXT,
    "voice" TEXT,
    "globalPrompt" TEXT,
    "observabilityPlan" TEXT,
    "backgroundSound" TEXT DEFAULT 'off',
    "hooks" TEXT DEFAULT '[]',
    "credentials" TEXT DEFAULT '[]',
    "server" TEXT,
    "compliancePlan" TEXT,
    "analysisPlan" TEXT,
    "artifactPlan" TEXT,
    "startSpeakingPlan" TEXT,
    "stopSpeakingPlan" TEXT,
    "monitorPlan" TEXT,
    "backgroundSpeechDenoisingPlan" TEXT,
    "credentialIds" TEXT DEFAULT '[]',
    "keypadInputPlan" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VapiWorkflow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VapiWorkflowNode" (
    "id" TEXT NOT NULL,
    "workflowId" TEXT NOT NULL,
    "nodeId" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "position" TEXT NOT NULL DEFAULT '{"x":0,"y":0}',
    "data" TEXT NOT NULL DEFAULT '{}',
    "isStart" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VapiWorkflowNode_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VapiWorkflowEdge" (
    "id" TEXT NOT NULL,
    "workflowId" TEXT NOT NULL,
    "edgeId" TEXT NOT NULL,
    "source" TEXT NOT NULL,
    "target" TEXT NOT NULL,
    "condition" TEXT,
    "conditionType" TEXT DEFAULT 'ai',
    "data" TEXT NOT NULL DEFAULT '{}',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VapiWorkflowEdge_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "VapiWorkflowExecution" (
    "id" TEXT NOT NULL,
    "workflowId" TEXT NOT NULL,
    "callId" TEXT,
    "status" TEXT NOT NULL DEFAULT 'running',
    "currentNodeId" TEXT,
    "phoneNumber" TEXT,
    "variables" TEXT DEFAULT '{}',
    "startedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "error" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VapiWorkflowExecution_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "VapiWorkflow_orgId_idx" ON "VapiWorkflow"("orgId");
CREATE INDEX "VapiWorkflow_status_idx" ON "VapiWorkflow"("status");
CREATE INDEX "VapiWorkflow_createdAt_idx" ON "VapiWorkflow"("createdAt");

-- CreateIndex
CREATE INDEX "VapiWorkflowNode_workflowId_idx" ON "VapiWorkflowNode"("workflowId");
CREATE INDEX "VapiWorkflowNode_nodeId_idx" ON "VapiWorkflowNode"("nodeId");
CREATE INDEX "VapiWorkflowNode_type_idx" ON "VapiWorkflowNode"("type");
CREATE UNIQUE INDEX "VapiWorkflowNode_workflowId_nodeId_key" ON "VapiWorkflowNode"("workflowId", "nodeId");

-- CreateIndex
CREATE INDEX "VapiWorkflowEdge_workflowId_idx" ON "VapiWorkflowEdge"("workflowId");
CREATE INDEX "VapiWorkflowEdge_source_idx" ON "VapiWorkflowEdge"("source");
CREATE INDEX "VapiWorkflowEdge_target_idx" ON "VapiWorkflowEdge"("target");
CREATE UNIQUE INDEX "VapiWorkflowEdge_workflowId_edgeId_key" ON "VapiWorkflowEdge"("workflowId", "edgeId");

-- CreateIndex
CREATE INDEX "VapiWorkflowExecution_workflowId_idx" ON "VapiWorkflowExecution"("workflowId");
CREATE INDEX "VapiWorkflowExecution_status_idx" ON "VapiWorkflowExecution"("status");
CREATE INDEX "VapiWorkflowExecution_startedAt_idx" ON "VapiWorkflowExecution"("startedAt");

-- AddForeignKey
ALTER TABLE "VapiWorkflowNode" ADD CONSTRAINT "VapiWorkflowNode_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "VapiWorkflow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VapiWorkflowEdge" ADD CONSTRAINT "VapiWorkflowEdge_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "VapiWorkflow"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VapiWorkflowExecution" ADD CONSTRAINT "VapiWorkflowExecution_workflowId_fkey" FOREIGN KEY ("workflowId") REFERENCES "VapiWorkflow"("id") ON DELETE CASCADE ON UPDATE CASCADE;
