-- VAPI Workflows Database Schema (Based on Official VAPI API Documentation)

-- Main VAPI Workflows Table
CREATE TABLE IF NOT EXISTS vapi_workflows (
    id VARCHAR(255) PRIMARY KEY,
    org_id VARCHAR(255),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status ENUM('draft', 'active', 'archived') DEFAULT 'draft',
    
    -- VAPI Configuration Fields (stored as JSON)
    model JSO<PERSON>,
    transcriber J<PERSON><PERSON>,
    voice JSO<PERSON>,
    global_prompt TEXT,
    observability_plan JSON,
    background_sound VARCHAR(255) DEFAULT 'off',
    hooks JSON,
    credentials JSON,
    server JSON,
    compliance_plan JSON,
    analysis_plan JSON,
    artifact_plan JSON,
    start_speaking_plan JSON,
    stop_speaking_plan JSON,
    monitor_plan JSON,
    background_speech_denoising_plan JSON,
    credential_ids JSON,
    keypad_input_plan JSON,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_org_id (org_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- VAPI Workflow Nodes Table
CREATE TABLE IF NOT EXISTS vapi_workflow_nodes (
    id VARCHAR(255) PRIMARY KEY,
    workflow_id VARCHAR(255) NOT NULL,
    node_id VARCHAR(255) NOT NULL,
    type ENUM('conversation', 'apiRequest', 'transferCall', 'endCall', 'tool') NOT NULL,
    name VARCHAR(255) NOT NULL,
    position JSON NOT NULL,
    data JSON NOT NULL,
    is_start BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (workflow_id) REFERENCES vapi_workflows(id) ON DELETE CASCADE,
    UNIQUE KEY unique_workflow_node (workflow_id, node_id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_node_id (node_id),
    INDEX idx_type (type),
    INDEX idx_is_start (is_start)
);

-- VAPI Workflow Edges Table
CREATE TABLE IF NOT EXISTS vapi_workflow_edges (
    id VARCHAR(255) PRIMARY KEY,
    workflow_id VARCHAR(255) NOT NULL,
    edge_id VARCHAR(255) NOT NULL,
    source VARCHAR(255) NOT NULL,
    target VARCHAR(255) NOT NULL,
    condition TEXT,
    condition_type ENUM('ai', 'logical', 'combined') DEFAULT 'ai',
    data JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (workflow_id) REFERENCES vapi_workflows(id) ON DELETE CASCADE,
    UNIQUE KEY unique_workflow_edge (workflow_id, edge_id),
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_source (source),
    INDEX idx_target (target),
    INDEX idx_condition_type (condition_type)
);

-- VAPI Workflow Executions Table
CREATE TABLE IF NOT EXISTS vapi_workflow_executions (
    id VARCHAR(255) PRIMARY KEY,
    workflow_id VARCHAR(255) NOT NULL,
    call_id VARCHAR(255),
    status ENUM('running', 'completed', 'failed', 'cancelled') DEFAULT 'running',
    current_node_id VARCHAR(255),
    phone_number VARCHAR(50),
    variables JSON,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (workflow_id) REFERENCES vapi_workflows(id) ON DELETE CASCADE,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at),
    INDEX idx_call_id (call_id)
);

-- VAPI Workflow Analytics Table (for tracking performance)
CREATE TABLE IF NOT EXISTS vapi_workflow_analytics (
    id VARCHAR(255) PRIMARY KEY,
    workflow_id VARCHAR(255) NOT NULL,
    execution_id VARCHAR(255),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,4),
    metric_data JSON,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (workflow_id) REFERENCES vapi_workflows(id) ON DELETE CASCADE,
    FOREIGN KEY (execution_id) REFERENCES vapi_workflow_executions(id) ON DELETE CASCADE,
    INDEX idx_workflow_id (workflow_id),
    INDEX idx_execution_id (execution_id),
    INDEX idx_metric_name (metric_name),
    INDEX idx_recorded_at (recorded_at)
);

-- Insert sample VAPI workflow templates
INSERT IGNORE INTO vapi_workflows (
    id, 
    name, 
    description, 
    status,
    global_prompt,
    background_sound
) VALUES 
(
    'vapi-template-blank',
    'Blank VAPI Workflow',
    'A blank workflow template to start building your voice AI',
    'draft',
    'You are a helpful AI assistant. Be friendly and conversational.',
    'off'
),
(
    'vapi-template-info-gathering',
    'Information Gathering Template',
    'Collect user information with variable extraction',
    'draft',
    'You are a helpful assistant that gathers information from users.',
    'off'
),
(
    'vapi-template-appointment',
    'Appointment Scheduling Template',
    'Schedule, reschedule, and cancel appointments',
    'draft',
    'You are an appointment scheduling assistant. Help users manage their appointments.',
    'off'
),
(
    'vapi-template-support',
    'Customer Support Template',
    'Handle customer inquiries with escalation options',
    'draft',
    'You are a customer support assistant. Help resolve customer issues.',
    'off'
);

-- Insert sample nodes for blank template
INSERT IGNORE INTO vapi_workflow_nodes (
    id,
    workflow_id,
    node_id,
    type,
    name,
    position,
    data,
    is_start
) VALUES (
    'vapi-node-start-blank',
    'vapi-template-blank',
    'start-conversation',
    'conversation',
    'Start Conversation',
    '{"x": 250, "y": 100}',
    '{"type": "conversation", "firstMessage": "Hey there!", "prompt": "You are a helpful assistant. Be friendly and conversational.", "extractVariables": []}',
    TRUE
);

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON vapi_workflows TO 'your_app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON vapi_workflow_nodes TO 'your_app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON vapi_workflow_edges TO 'your_app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON vapi_workflow_executions TO 'your_app_user'@'%';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON vapi_workflow_analytics TO 'your_app_user'@'%';
