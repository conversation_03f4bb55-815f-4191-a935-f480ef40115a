-- Enhanced Vapi Platform Database Schema
-- This script creates all necessary tables for a comprehensive Vapi-like platform

-- Enhanced Assistants table with full Vapi features
CREATE TABLE IF NOT EXISTS assistants (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  assistant_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VA<PERSON>HA<PERSON>(255) NOT NULL,
  first_message TEXT,
  system_message TEXT,
  model JSON,
  voice JSON,
  transcriber JSO<PERSON>,
  functions JSON,
  end_call_message TEXT,
  end_call_phrases JSON,
  metadata JSON,
  background_sound VARCHAR(255),
  backchannel_enabled BOOLEAN DEFAULT FALSE,
  background_denoising_enabled BOOLEAN DEFAULT FALSE,
  model_output_in_messages_enabled BOOLEAN DEFAULT FALSE,
  transport_configurations JSON,
  artifact_plan JSON,
  message_plan JSON,
  start_speaking_plan JSON,
  stop_speaking_plan JSON,
  monitor_plan JSON,
  credential_ids JSON,
  server_url VARCHAR(500),
  server_url_secret VARCHAR(255),
  analysis_plan JSON,
  max_duration_seconds INT,
  silence_timeout_seconds INT,
  response_delay_seconds DECIMAL(5,2),
  llm_request_delay_seconds DECIMAL(5,2),
  num_words_to_interrupt_assistant INT,
  max_words_per_spoken_response INT,
  voice_activity_detection JSON,
  hipaa_enabled BOOLEAN DEFAULT FALSE,
  client_messages JSON,
  server_messages JSON,
  phone_number_id VARCHAR(255),
  customer_id VARCHAR(255),
  squad_id VARCHAR(255),

  status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_assistant_id (assistant_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);



-- Enhanced Knowledge Bases table
CREATE TABLE IF NOT EXISTS knowledge_bases (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  kb_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  file_ids JSON,
  urls JSON,
  provider VARCHAR(100) DEFAULT 'openai',
  embedding_model VARCHAR(255) DEFAULT 'text-embedding-ada-002',
  chunk_size INT DEFAULT 1000,
  chunk_overlap INT DEFAULT 200,
  top_k INT DEFAULT 5,
  similarity_threshold DECIMAL(3,2) DEFAULT 0.70,
  preprocessing_config JSON,
  indexing_config JSON,
  retrieval_config JSON,
  status ENUM('creating', 'ready', 'error', 'updating') DEFAULT 'creating',
  metadata JSON,
  documents_count INT DEFAULT 0,
  chunks_count INT DEFAULT 0,
  total_tokens INT DEFAULT 0,
  last_indexed TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_kb_id (kb_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Knowledge Base Search Queries table for analytics
CREATE TABLE IF NOT EXISTS kb_search_queries (
  id INT AUTO_INCREMENT PRIMARY KEY,
  kb_id VARCHAR(255) NOT NULL,
  user_id INT,
  query TEXT NOT NULL,
  results_count INT,
  response_time_ms INT,
  results_preview JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_kb_id (kb_id),
  INDEX idx_user_id (user_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (kb_id) REFERENCES knowledge_bases(kb_id) ON DELETE CASCADE
);

-- Comprehensive Squads table
CREATE TABLE IF NOT EXISTS squads (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  squad_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  members JSON,
  routing_strategy ENUM('round_robin', 'priority', 'skill_based', 'load_balanced') DEFAULT 'round_robin',
  escalation_rules JSON,
  availability_schedule JSON,
  max_concurrent_calls INT DEFAULT 10,
  priority_levels JSON,
  skills_required JSON,
  performance_metrics JSON,
  status ENUM('active', 'inactive') DEFAULT 'active',
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_squad_id (squad_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Comprehensive Calls table
CREATE TABLE IF NOT EXISTS calls (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  call_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  type ENUM('outbound', 'inbound', 'web') DEFAULT 'outbound',
  status ENUM('queued', 'ringing', 'in-progress', 'forwarding', 'ended') DEFAULT 'queued',
  assistant_id VARCHAR(255),
  squad_id VARCHAR(255),

  phone_number_id VARCHAR(255),
  customer_id VARCHAR(255),
  customer_number VARCHAR(50),
  duration INT DEFAULT 0,
  cost DECIMAL(10,4) DEFAULT 0.0000,
  transcript LONGTEXT,
  recording_url VARCHAR(500),
  analysis JSON,
  artifacts JSON,
  end_reason VARCHAR(255),
  started_at TIMESTAMP NULL,
  ended_at TIMESTAMP NULL,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_call_id (call_id),
  INDEX idx_status (status),
  INDEX idx_type (type),
  INDEX idx_assistant_id (assistant_id),
  INDEX idx_created_at (created_at),
  INDEX idx_customer_number (customer_number)
);

-- Enhanced Phone Numbers table
CREATE TABLE IF NOT EXISTS phone_numbers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  phone_number_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  number VARCHAR(50) NOT NULL,
  country_code VARCHAR(10),
  provider VARCHAR(100),
  type ENUM('local', 'toll_free', 'mobile') DEFAULT 'local',
  capabilities JSON,
  sip_uri VARCHAR(255),
  fallback_destination VARCHAR(255),
  assistant_id VARCHAR(255),
  squad_id VARCHAR(255),

  status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_phone_number_id (phone_number_id),
  INDEX idx_number (number),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Enhanced Tools table
CREATE TABLE IF NOT EXISTS tools (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  tool_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  type ENUM('function', 'webhook', 'api', 'database') DEFAULT 'function',
  configuration JSON NOT NULL,
  schema JSON,
  authentication JSON,
  rate_limits JSON,
  status ENUM('active', 'inactive', 'testing') DEFAULT 'active',
  usage_count INT DEFAULT 0,
  last_used TIMESTAMP NULL,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_tool_id (tool_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Enhanced Files table
CREATE TABLE IF NOT EXISTS files (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  file_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VARCHAR(255) NOT NULL,
  original_name VARCHAR(255),
  mime_type VARCHAR(100),
  size_bytes BIGINT,
  url VARCHAR(500),
  storage_provider VARCHAR(100),
  storage_path VARCHAR(500),
  processing_status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
  processing_metadata JSON,
  purpose ENUM('knowledge_base', 'training', 'audio', 'other') DEFAULT 'other',
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_file_id (file_id),
  INDEX idx_purpose (purpose),
  INDEX idx_processing_status (processing_status),
  INDEX idx_created_at (created_at)
);

-- Campaigns table for outbound campaigns
CREATE TABLE IF NOT EXISTS campaigns (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  campaign_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  type ENUM('outbound_calls', 'sms', 'email', 'mixed') DEFAULT 'outbound_calls',
  assistant_id VARCHAR(255),
  squad_id VARCHAR(255),

  phone_number_id VARCHAR(255),
  contact_list_ids JSON,
  schedule_config JSON,
  targeting_rules JSON,
  call_settings JSON,
  status ENUM('draft', 'scheduled', 'running', 'paused', 'completed', 'cancelled') DEFAULT 'draft',
  start_date TIMESTAMP NULL,
  end_date TIMESTAMP NULL,
  total_contacts INT DEFAULT 0,
  completed_contacts INT DEFAULT 0,
  successful_contacts INT DEFAULT 0,
  failed_contacts INT DEFAULT 0,
  total_cost DECIMAL(10,4) DEFAULT 0.0000,
  performance_metrics JSON,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_campaign_id (campaign_id),
  INDEX idx_status (status),
  INDEX idx_type (type),
  INDEX idx_created_at (created_at)
);

-- Sessions table for chat and web sessions
CREATE TABLE IF NOT EXISTS sessions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  session_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  type ENUM('chat', 'web', 'voice') DEFAULT 'chat',
  assistant_id VARCHAR(255),

  customer_id VARCHAR(255),
  status ENUM('active', 'ended', 'timeout') DEFAULT 'active',
  messages JSON,
  context JSON,
  metadata JSON,
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ended_at TIMESTAMP NULL,
  last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_session_id (session_id),
  INDEX idx_type (type),
  INDEX idx_status (status),
  INDEX idx_customer_id (customer_id),
  INDEX idx_created_at (created_at)
);

-- Chats table for individual chat conversations
CREATE TABLE IF NOT EXISTS chats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  chat_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  session_id VARCHAR(255),
  assistant_id VARCHAR(255),
  customer_id VARCHAR(255),
  title VARCHAR(255),
  messages JSON,
  status ENUM('active', 'archived', 'deleted') DEFAULT 'active',
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_chat_id (chat_id),
  INDEX idx_session_id (session_id),
  INDEX idx_customer_id (customer_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Customers table
CREATE TABLE IF NOT EXISTS customers (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  customer_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VARCHAR(255),
  email VARCHAR(255),
  phone VARCHAR(50),
  company VARCHAR(255),
  properties JSON,
  tags JSON,
  status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
  last_contact TIMESTAMP NULL,
  total_calls INT DEFAULT 0,
  total_sessions INT DEFAULT 0,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_customer_id (customer_id),
  INDEX idx_email (email),
  INDEX idx_phone (phone),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Analytics Events table for tracking detailed events
CREATE TABLE IF NOT EXISTS analytics_events (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  org_id VARCHAR(255),
  event_type VARCHAR(100) NOT NULL,
  event_category VARCHAR(100),
  entity_type VARCHAR(100),
  entity_id VARCHAR(255),
  properties JSON,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_event_type (event_type),
  INDEX idx_entity_type (entity_type),
  INDEX idx_entity_id (entity_id),
  INDEX idx_timestamp (timestamp)
);

-- Webhooks table
CREATE TABLE IF NOT EXISTS webhooks (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  webhook_id VARCHAR(255) UNIQUE NOT NULL,
  org_id VARCHAR(255),
  name VARCHAR(255) NOT NULL,
  url VARCHAR(500) NOT NULL,
  events JSON NOT NULL,
  headers JSON,
  secret VARCHAR(255),
  status ENUM('active', 'inactive', 'failed') DEFAULT 'active',
  retry_config JSON,
  last_triggered TIMESTAMP NULL,
  success_count INT DEFAULT 0,
  failure_count INT DEFAULT 0,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_webhook_id (webhook_id),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);

-- Logs table for system and application logs
CREATE TABLE IF NOT EXISTS logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT,
  org_id VARCHAR(255),
  level ENUM('debug', 'info', 'warn', 'error', 'fatal') DEFAULT 'info',
  message TEXT NOT NULL,
  category VARCHAR(100),
  entity_type VARCHAR(100),
  entity_id VARCHAR(255),
  context JSON,
  stack_trace TEXT,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_level (level),
  INDEX idx_category (category),
  INDEX idx_entity_type (entity_type),
  INDEX idx_timestamp (timestamp)
);


