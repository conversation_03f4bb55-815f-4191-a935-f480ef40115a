const mysql = require('mysql2/promise');

async function createVapiTables() {
  const connection = await mysql.createConnection({
    host: '***********',
    user: 'root',
    password: 'l51Qh6kM2vb3npALukrKNMzNAlBogTj0NSH4Gd3IxqMfaP0qfFkp54e7jcknqGNX',
    database: 'ai_agent'
  });

  try {
    console.log('Creating VAPI workflow tables...');

    // Create vapi_workflows table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS vapi_workflows (
        id VARCHAR(255) PRIMARY KEY,
        org_id VARCHAR(255),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        status ENUM('draft', 'active', 'archived') DEFAULT 'draft',
        model JSON,
        transcriber J<PERSON><PERSON>,
        voice JSON,
        global_prompt TEXT,
        observability_plan JSON,
        background_sound VARCHAR(255) DEFAULT 'off',
        hooks <PERSON>SO<PERSON>,
        credentials JSON,
        server JSON,
        compliance_plan JSON,
        analysis_plan JSON,
        artifact_plan JSON,
        start_speaking_plan JSON,
        stop_speaking_plan JSON,
        monitor_plan JSON,
        background_speech_denoising_plan JSON,
        credential_ids JSON,
        keypad_input_plan JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✓ Created vapi_workflows table');

    // Create vapi_workflow_nodes table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS vapi_workflow_nodes (
        id VARCHAR(255) PRIMARY KEY,
        workflow_id VARCHAR(255) NOT NULL,
        node_id VARCHAR(255) NOT NULL,
        type ENUM('conversation', 'apiRequest', 'transferCall', 'endCall', 'tool') NOT NULL,
        name VARCHAR(255) NOT NULL,
        position JSON NOT NULL,
        data JSON NOT NULL,
        is_start BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (workflow_id) REFERENCES vapi_workflows(id) ON DELETE CASCADE,
        UNIQUE KEY unique_workflow_node (workflow_id, node_id)
      )
    `);
    console.log('✓ Created vapi_workflow_nodes table');

    // Create vapi_workflow_edges table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS vapi_workflow_edges (
        id VARCHAR(255) PRIMARY KEY,
        workflow_id VARCHAR(255) NOT NULL,
        edge_id VARCHAR(255) NOT NULL,
        source VARCHAR(255) NOT NULL,
        target VARCHAR(255) NOT NULL,
        \`condition\` TEXT,
        condition_type ENUM('ai', 'logical', 'combined') DEFAULT 'ai',
        data JSON NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (workflow_id) REFERENCES vapi_workflows(id) ON DELETE CASCADE,
        UNIQUE KEY unique_workflow_edge (workflow_id, edge_id)
      )
    `);
    console.log('✓ Created vapi_workflow_edges table');

    // Create vapi_workflow_executions table
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS vapi_workflow_executions (
        id VARCHAR(255) PRIMARY KEY,
        workflow_id VARCHAR(255) NOT NULL,
        call_id VARCHAR(255),
        status ENUM('running', 'completed', 'failed', 'cancelled') DEFAULT 'running',
        current_node_id VARCHAR(255),
        phone_number VARCHAR(50),
        variables JSON,
        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        error TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (workflow_id) REFERENCES vapi_workflows(id) ON DELETE CASCADE
      )
    `);
    console.log('✓ Created vapi_workflow_executions table');

    // Create indexes (with error handling for existing indexes)
    const indexes = [
      'CREATE INDEX idx_vapi_workflows_org_id ON vapi_workflows(org_id)',
      'CREATE INDEX idx_vapi_workflows_status ON vapi_workflows(status)',
      'CREATE INDEX idx_vapi_workflows_created_at ON vapi_workflows(created_at)',
      'CREATE INDEX idx_vapi_workflow_nodes_workflow_id ON vapi_workflow_nodes(workflow_id)',
      'CREATE INDEX idx_vapi_workflow_nodes_node_id ON vapi_workflow_nodes(node_id)',
      'CREATE INDEX idx_vapi_workflow_nodes_type ON vapi_workflow_nodes(type)',
      'CREATE INDEX idx_vapi_workflow_edges_workflow_id ON vapi_workflow_edges(workflow_id)',
      'CREATE INDEX idx_vapi_workflow_edges_source ON vapi_workflow_edges(source)',
      'CREATE INDEX idx_vapi_workflow_edges_target ON vapi_workflow_edges(target)',
      'CREATE INDEX idx_vapi_workflow_executions_workflow_id ON vapi_workflow_executions(workflow_id)',
      'CREATE INDEX idx_vapi_workflow_executions_status ON vapi_workflow_executions(status)',
      'CREATE INDEX idx_vapi_workflow_executions_started_at ON vapi_workflow_executions(started_at)'
    ];

    for (const indexSql of indexes) {
      try {
        await connection.execute(indexSql);
      } catch (error) {
        // Ignore duplicate key errors (index already exists)
        if (error.code !== 'ER_DUP_KEYNAME') {
          console.warn('Index creation warning:', error.message);
        }
      }
    }

    console.log('✓ Created indexes');

    // Insert sample template data
    const templateId = 'vapi-template-blank';
    const nodeId = 'vapi-node-start-blank';
    
    await connection.execute(`
      INSERT IGNORE INTO vapi_workflows (
        id, name, description, status, global_prompt, background_sound
      ) VALUES (?, ?, ?, ?, ?, ?)
    `, [
      templateId,
      'Blank VAPI Workflow',
      'A blank workflow template to start building your voice AI',
      'draft',
      'You are a helpful AI assistant. Be friendly and conversational.',
      'off'
    ]);

    await connection.execute(`
      INSERT IGNORE INTO vapi_workflow_nodes (
        id, workflow_id, node_id, type, name, position, data, is_start
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      nodeId,
      templateId,
      'start-conversation',
      'conversation',
      'Start Conversation',
      JSON.stringify({ x: 250, y: 100 }),
      JSON.stringify({
        type: 'conversation',
        firstMessage: 'Hey there!',
        prompt: 'You are a helpful assistant. Be friendly and conversational.',
        extractVariables: []
      }),
      true
    ]);

    console.log('✓ Inserted sample template data');
    console.log('🎉 All VAPI workflow tables created successfully!');

  } catch (error) {
    console.error('❌ Error creating VAPI tables:', error);
  } finally {
    await connection.end();
  }
}

createVapiTables();
