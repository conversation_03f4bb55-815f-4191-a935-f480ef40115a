// index.js
const express = require("express");
const app = express();
const dotenv = require("dotenv");
const pool = require("./config/DBConnection");
const helmet = require("helmet");
const cors = require("cors");
const { swaggerUi, specs } = require("./config/swagger");
const userRoutes = require("./routes/userRoute/userRoute");
const roleRoutes = require("./routes/roleRoute/roleRoutes");
const contactRoutes = require("./routes/contactRoute/contactRoute");
const authRoutes = require("./routes/AuthRoutes/AuthRoutes");
const AssistantRoutes = require("./routes/AssistantRoutes/AssistantRoutes");
const outboundCallRoutes = require("./routes/outboundRoutes/outboundCallRoutes");
const transciber_voice_model_routes = require("./routes/transcriber_model_voices_routes/transcriber_model_voice_routes");

// New VAPI Feature Routes
const modelsRoutes = require("./routes/ModelsRoutes/ModelsRoutes");
const voicesRoutes = require("./routes/VoicesRoutes/VoicesRoutes");
const transcribersRoutes = require("./routes/TranscribersRoutes/TranscribersRoutes");
const phoneNumbersRoutes = require("./routes/PhoneNumbersRoutes/PhoneNumbersRoutes");
const toolsRoutes = require("./routes/ToolsRoutes/ToolsRoutes");
const filesRoutes = require("./routes/FilesRoutes/FilesRoutes");
const knowledgeBasesRoutes = require("./routes/KnowledgeBasesRoutes/KnowledgeBasesRoutes");
const sessionsRoutes = require("./routes/SessionsRoutes/SessionsRoutes");
const chatsRoutes = require("./routes/ChatsRoutes/ChatsRoutes");
const squadsRoutes = require("./routes/SquadsRoutes/SquadsRoutes");
const workflowRoutes = require("./routes/WorkflowRoutes/WorkflowRoutes");
const vapiWorkflowRoutes = require("./routes/VapiWorkflowRoutes");

const analyticsRoutes = require("./routes/AnalyticsRoutes/AnalyticsRoutes");
const logsRoutes = require("./routes/LogsRoutes/LogsRoutes");
const webhooksRoutes = require("./routes/WebhooksRoutes/WebhooksRoutes");
const recordingsRoutes = require("./routes/RecordingsRoutes/RecordingsRoutes");

dotenv.config({ path: "./config/config.env" });

app.use(express.json());
app.use(helmet());
app.use(cors());

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'VAPI Platform API Documentation'
}));

// Welcome route - redirect to API documentation
app.get('/', (req, res) => {
  res.redirect('/api-docs');
});
// Routes
app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/roles", roleRoutes);
app.use("/api", contactRoutes);
app.use("/api/assistant", AssistantRoutes);
app.use("/api/outboundcall", outboundCallRoutes);
app.use("/api/trans_voice_model", transciber_voice_model_routes);

// New VAPI Feature Routes
app.use("/api/models", modelsRoutes);
app.use("/api/voices", voicesRoutes);
app.use("/api/transcribers", transcribersRoutes);
app.use("/api/phone-numbers", phoneNumbersRoutes);
app.use("/api/tools", toolsRoutes);
app.use("/api/files", filesRoutes);
app.use("/api/knowledge-bases", knowledgeBasesRoutes);
app.use("/api/sessions", sessionsRoutes);
app.use("/api/chats", chatsRoutes);
app.use("/api/squads", squadsRoutes);
app.use("/api/workflows", workflowRoutes);
app.use("/api/vapi/workflows", vapiWorkflowRoutes);


app.use("/api/analytics", analyticsRoutes);
app.use("/api/logs", logsRoutes);
app.use("/api/webhooks", webhooksRoutes);
app.use("/api/recordings", recordingsRoutes);


app.get("/", (req, res) => {
  res.send("User Management API is running.");
});

app.use((req, res) => {
  res.status(404).json({ message: "Route not found." });
});

const PORT = process.env.PORT;
app.listen(PORT, async () => {
  console.log(`Server is running on port ${PORT}`);

  try {
    // Test database connection
    const connection = await pool.getConnection();
    console.log("MySQL Database connected successfully");
    connection.release(); // Release the connection back to the pool
  } catch (err) {
    console.error("Database connection error:", err);
  }
});
