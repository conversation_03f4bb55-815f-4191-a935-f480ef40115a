{"name": "ai-agent", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "build-without-type": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.1.4", "@react-three/drei": "^9.117.3", "@react-three/fiber": "^8.17.10", "@reactflow/background": "^11.3.14", "@reactflow/controls": "^11.2.14", "@reactflow/core": "^11.11.4", "@reactflow/minimap": "^11.7.14", "@tailwindcss/line-clamp": "^0.4.4", "@tanstack/react-query": "^5.61.0", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.11.2", "gsap": "^3.12.5", "jwt-decode": "^4.0.0", "lucide-react": "^0.460.0", "next-themes": "^0.4.3", "papaparse": "^5.4.1", "react": "^18.3.1", "react-day-picker": "^9.6.7", "react-dom": "^18.3.1", "react-hook-form": "^7.53.2", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.3", "react-router-dom": "^6.28.0", "react-secure-storage": "^1.3.2", "react-toastify": "^10.0.6", "reactflow": "^11.11.4", "recharts": "^2.15.3", "sonner": "^1.7.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.1", "xlsx": "^0.18.5", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.13.0", "@tanstack/eslint-plugin-query": "^5.60.1", "@types/node": "^22.9.0", "@types/papaparse": "^5.3.15", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "autoprefixer": "^10.4.20", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.11.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.11.0", "vite": "^5.4.10"}}