// // src/context/AuthContext.tsx
// src/context/AuthContext.tsx

import React, { createContext, useContext, useState, useEffect } from "react";
import secureLocalStorage from "react-secure-storage";
import { jwtDecode } from "jwt-decode";
import { useNavigate } from "react-router-dom";

interface AuthContextType {
  isLoggedIn: boolean;
  token: string | null;
  login: (token: string) => void;
  logout: () => void;
  loading: boolean; // Added loading state
}

// Define the structure of your JWT payload
interface JwtPayload {
  exp: number; // Expiration time (in seconds since epoch)
  // Add other fields if necessary
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [token, setToken] = useState<string | null>(null);
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true); // Initialize loading as true
  const navigate = useNavigate();

  const logout = () => {
    secureLocalStorage.removeItem("token");
    setToken(null);
    setIsLoggedIn(false);
    navigate("/login"); // Redirect to login after logout
  };

  const checkTokenValidity = (storedToken: string) => {
    try {
      const decoded: JwtPayload = jwtDecode(storedToken);
      const currentTime = Date.now() / 1000; // Current time in seconds

      if (decoded.exp < currentTime) {
        console.log("Token has expired.");
        logout();
      } else {
        setToken(storedToken);
        setIsLoggedIn(true);
        console.log("Token is valid.");
      }
    } catch (error) {
      console.error("Error decoding token:", error);
      logout();
    }
  };

  useEffect(() => {
    // Introduce a 3-second delay before checking the token
    const timer = setTimeout(() => {
      const storedToken = secureLocalStorage.getItem("token");

      if (storedToken && typeof storedToken === "string") {
        checkTokenValidity(storedToken);
      } else {
        console.log("No token found in secure storage.");
        setIsLoggedIn(false);
      }

      setLoading(false); // Set loading to false after checking
    }, 3000); // 3-second delay

    return () => clearTimeout(timer); // Cleanup the timer on unmount
  }, []); // Empty dependency array ensures this runs only once on mount

  const login = (newToken: string) => {
    secureLocalStorage.setItem("token", newToken);
    checkTokenValidity(newToken); // Validate the token upon login
  };

  return (
    <AuthContext.Provider value={{ isLoggedIn, token, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

// import React, { createContext, useContext, useState, useEffect } from "react";
// import secureLocalStorage from "react-secure-storage";

// interface AuthContextType {
//   isLoggedIn: boolean;
//   token: string | null;
//   login: (token: string) => void;
//   logout: () => void;
//   loading: boolean; // Added loading state
// }

// const AuthContext = createContext<AuthContextType | undefined>(undefined);

// export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
//   children,
// }) => {
//   const [token, setToken] = useState<string | null>(null);
//   const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
//   const [loading, setLoading] = useState<boolean>(true); // Initialize loading as true

//   useEffect(() => {
//     // Introduce a 3-second delay before checking the token

//     const storedToken = secureLocalStorage.getItem("token");

//     if (storedToken && typeof storedToken === "string") {
//       setToken(storedToken);
//       setIsLoggedIn(true);
//       console.log("Token found in secure storage:", storedToken);
//     } else {
//       console.log("No token found in secure storage.");
//     }

//     setLoading(false); // Set loading to false after checking
//   }, []); // Empty dependency array ensures this runs only once on mount

//   const login = (newToken: string) => {
//     secureLocalStorage.setItem("token", newToken);
//     setToken(newToken);
//     setIsLoggedIn(true);
//   };

//   const logout = () => {
//     secureLocalStorage.removeItem("token");
//     setToken(null);
//     setIsLoggedIn(false);
//   };

//   return (
//     <AuthContext.Provider value={{ isLoggedIn, token, login, logout, loading }}>
//       {children}
//     </AuthContext.Provider>
//   );
// };

// // Custom hook to use the AuthContext
// export const useAuth = (): AuthContextType => {
//   const context = useContext(AuthContext);
//   if (!context) {
//     throw new Error("useAuth must be used within an AuthProvider");
//   }
//   return context;
// };
