import React, { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { 
  ArrowLeft, 
  Download, 
  Share2, 
  Edit, 
  Trash, 
  FileAudio, 
  Calendar, 
  Clock, 
  User, 
  Phone,
  DollarSign,
  BarChart3,
  MessageSquare,
  Save,
  X
} from "lucide-react";
import AudioPlayer from "@/components/AudioPlayer";
import Page from "@/components/Page";
import {
  getRecordingById,
  getRecordingAnalytics,
  getRecordingTranscript,
  updateRecordingTranscript,
  deleteRecording,
  downloadRecording,
  shareRecording,
  type Recording,
  type RecordingAnalytics
} from "@/api/services/recordings/recordingService";
import { toast } from "sonner";

const RecordingDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isEditingTranscript, setIsEditingTranscript] = useState(false);
  const [transcriptText, setTranscriptText] = useState("");

  // Fetch recording details
  const {
    data: recording,
    isLoading: recordingLoading,
    error: recordingError
  } = useQuery({
    queryKey: ["recording", id],
    queryFn: () => getRecordingById(id!),
    enabled: !!id,
  });

  // Fetch recording analytics
  const {
    data: analytics,
    isLoading: analyticsLoading
  } = useQuery({
    queryKey: ["recording-analytics", id],
    queryFn: () => getRecordingAnalytics(id!),
    enabled: !!id,
  });

  // Fetch recording transcript
  const {
    data: transcript,
    isLoading: transcriptLoading
  } = useQuery({
    queryKey: ["recording-transcript", id],
    queryFn: () => getRecordingTranscript(id!),
    enabled: !!id,
  });

  // Update transcript mutation
  const updateTranscriptMutation = useMutation({
    mutationFn: (newTranscript: string) => updateRecordingTranscript(id!, newTranscript),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["recording-transcript", id] });
      setIsEditingTranscript(false);
      toast.success("Transcript updated successfully");
    },
  });

  // Delete recording mutation
  const deleteRecordingMutation = useMutation({
    mutationFn: () => deleteRecording(id!),
    onSuccess: () => {
      toast.success("Recording deleted successfully");
      navigate("/recordings");
    },
  });

  // Download recording mutation
  const downloadRecordingMutation = useMutation({
    mutationFn: () => downloadRecording(id!, `${recording?.recordingName}.${recording?.format}`),
  });

  // Share recording mutation
  const shareRecordingMutation = useMutation({
    mutationFn: (shareData: any) => shareRecording(id!, shareData),
  });

  const handleEditTranscript = () => {
    setTranscriptText(transcript?.transcript || "");
    setIsEditingTranscript(true);
  };

  const handleSaveTranscript = () => {
    updateTranscriptMutation.mutate(transcriptText);
  };

  const handleCancelEdit = () => {
    setIsEditingTranscript(false);
    setTranscriptText("");
  };

  const handleDelete = () => {
    if (confirm(`Are you sure you want to delete "${recording?.recordingName}"?`)) {
      deleteRecordingMutation.mutate();
    }
  };

  const handleDownload = () => {
    downloadRecordingMutation.mutate();
  };

  const handleShare = () => {
    shareRecordingMutation.mutate({ expiresIn: "7d" });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case "Sales":
        return "default";
      case "Support":
        return "secondary";
      case "Follow-up":
        return "outline";
      default:
        return "secondary";
    }
  };

  if (recordingLoading) {
    return (
      <Page>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </Page>
    );
  }

  if (recordingError || !recording) {
    return (
      <Page>
        <div className="text-center py-8">
          <h3 className="text-lg font-medium mb-2">Recording not found</h3>
          <p className="text-muted-foreground mb-4">
            The recording you're looking for doesn't exist or has been deleted.
          </p>
          <Button onClick={() => navigate("/recordings")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Recordings
          </Button>
        </div>
      </Page>
    );
  }

  return (
    <Page>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/recordings")}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Recordings
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{recording.recordingName}</h1>
            <p className="text-muted-foreground">Recording ID: {recording.id}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            Download
          </Button>
          <Button variant="outline" size="sm" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button variant="destructive" size="sm" onClick={handleDelete}>
            <Trash className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Audio Player */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileAudio className="h-5 w-5 mr-2" />
                Audio Player
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AudioPlayer
                src={`/api/recordings/${recording.id}/stream`}
                title={recording.recordingName}
                duration={recording.duration}
                onDownload={handleDownload}
                onShare={handleShare}
              />
            </CardContent>
          </Card>

          {/* Transcript */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Transcript
                </CardTitle>
                {!isEditingTranscript && (
                  <Button variant="outline" size="sm" onClick={handleEditTranscript}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {transcriptLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              ) : isEditingTranscript ? (
                <div className="space-y-4">
                  <Textarea
                    value={transcriptText}
                    onChange={(e) => setTranscriptText(e.target.value)}
                    placeholder="Enter transcript..."
                    rows={10}
                  />
                  <div className="flex items-center space-x-2">
                    <Button 
                      size="sm" 
                      onClick={handleSaveTranscript}
                      disabled={updateTranscriptMutation.isPending}
                    >
                      <Save className="h-4 w-4 mr-2" />
                      Save
                    </Button>
                    <Button variant="outline" size="sm" onClick={handleCancelEdit}>
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="prose max-w-none">
                  <p className="whitespace-pre-wrap">
                    {transcript?.transcript || "No transcript available"}
                  </p>
                  {transcript && (
                    <div className="mt-4 text-sm text-muted-foreground">
                      <p>Confidence: {(transcript.confidence * 100).toFixed(1)}%</p>
                      <p>Word count: {transcript.wordCount}</p>
                      <p>Language: {transcript.language}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Recording Info */}
          <Card>
            <CardHeader>
              <CardTitle>Recording Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Type</span>
                <Badge variant={getTypeBadgeVariant(recording.type)}>
                  {recording.type}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status</span>
                <Badge variant={recording.status === "completed" ? "default" : "secondary"}>
                  {recording.status}
                </Badge>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Created</p>
                    <p className="text-xs text-muted-foreground">
                      {formatDate(recording.createdAt)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Duration</p>
                    <p className="text-xs text-muted-foreground">{recording.duration}</p>
                  </div>
                </div>

                {recording.phoneNumber && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Phone Number</p>
                      <p className="text-xs text-muted-foreground">{recording.phoneNumber}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Cost</p>
                    <p className="text-xs text-muted-foreground">${recording.cost.toFixed(2)}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Analytics */}
          {analytics && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Analytics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Play Count</span>
                  <span className="text-sm font-medium">{analytics.playCount}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Downloads</span>
                  <span className="text-sm font-medium">{analytics.downloadCount}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Shares</span>
                  <span className="text-sm font-medium">{analytics.shareCount}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Completion Rate</span>
                  <span className="text-sm font-medium">{analytics.completionRate}</span>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </Page>
  );
};

export default RecordingDetail;
