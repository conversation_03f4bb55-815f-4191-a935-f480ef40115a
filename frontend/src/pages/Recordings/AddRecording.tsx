// src/pages/Recordings/AddRecording.tsx

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  SheetClose,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  Calendar as CalendarIcon,
  Mic,
  Clock,
  Upload,
  FileAudio,
  Tag,
  User,
  Info,
  Play,
  Pause,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

export function AddRecording() {
  const [recordingDate, setRecordingDate] = useState<Date>();
  const [isPlaying, setIsPlaying] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  // Toggle play/pause
  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  return (
    <>
      <SheetTitle className="text-xl font-semibold jakarta bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-1">
        Add Voice Recording
      </SheetTitle>
      <SheetDescription className="text-muted-foreground mb-6">
        Upload or record a new voice conversation for your AI agent.
      </SheetDescription>
      
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <FileAudio className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Recording Details</h3>
          </div>
          
          <div>
            <Label htmlFor="name" className="text-sm font-medium text-foreground mb-1.5 block">
              Recording Name
            </Label>
            <Input 
              id="name" 
              placeholder="Enter recording name" 
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20" 
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="date" className="text-sm font-medium text-foreground mb-1.5 block">
                Recording Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20",
                      !recordingDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {recordingDate ? format(recordingDate, "PPP") : <span>Select date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={recordingDate}
                    onSelect={setRecordingDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div>
              <Label htmlFor="duration" className="text-sm font-medium text-foreground mb-1.5 block">
                Duration
              </Label>
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input 
                  id="duration" 
                  placeholder="HH:MM:SS" 
                  className="w-full pl-10 border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20" 
                />
              </div>
            </div>
          </div>
          
          <div>
            <Label htmlFor="contact" className="text-sm font-medium text-foreground mb-1.5 block">
              Contact
            </Label>
            <Select>
              <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                <SelectValue placeholder="Select a contact" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="jane">Jane Smith</SelectItem>
                <SelectItem value="john">John Doe</SelectItem>
                <SelectItem value="alice">Alice Johnson</SelectItem>
                <SelectItem value="bob">Bob Williams</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="notes" className="text-sm font-medium text-foreground mb-1.5 block">
              Notes
            </Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this recording..."
              className="w-full min-h-[80px] border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Upload className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Upload Recording</h3>
          </div>
          
          <div className="border-2 border-dashed border-border/50 rounded-lg p-6 text-center">
            {uploadedFile ? (
              <div className="space-y-4">
                <div className="flex flex-col items-center justify-center">
                  <FileAudio className="h-10 w-10 text-primary mb-2" />
                  <h3 className="text-sm font-medium">{uploadedFile.name}</h3>
                  <p className="text-xs text-muted-foreground">
                    {(uploadedFile.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                </div>
                
                <div className="w-full bg-muted rounded-full h-1.5">
                  <div className="bg-primary h-1.5 rounded-full w-full"></div>
                </div>
                
                <div className="flex items-center justify-center">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="rounded-full h-8 w-8 p-0 mr-2"
                    onClick={togglePlayPause}
                  >
                    {isPlaying ? (
                      <Pause className="h-4 w-4" />
                    ) : (
                      <Play className="h-4 w-4" />
                    )}
                  </Button>
                  <span className="text-sm">00:00 / {uploadedFile.name.includes('.mp3') ? '03:45' : '00:00'}</span>
                </div>
                
                <Button
                  type="button"
                  variant="outline"
                  className="text-xs border-border/50 hover:bg-muted/20"
                  onClick={() => setUploadedFile(null)}
                >
                  Replace File
                </Button>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center">
                <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                <h3 className="text-sm font-medium mb-1">Upload Audio File</h3>
                <p className="text-xs text-muted-foreground mb-3">
                  Drag and drop an audio file here or click to browse
                </p>
                <Input
                  type="file"
                  id="file-upload"
                  accept="audio/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
                <Button
                  type="button"
                  variant="outline"
                  className="text-xs border-border/50 hover:bg-muted/20"
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  Browse Files
                </Button>
              </div>
            )}
          </div>
          
          <div className="flex items-center p-3 bg-muted/20 rounded-lg border border-border/50">
            <Info className="h-4 w-4 text-muted-foreground mr-2 flex-shrink-0" />
            <p className="text-xs text-muted-foreground">
              Supported formats: MP3, WAV, M4A. Maximum file size: 50MB
            </p>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Tag className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Tags</h3>
          </div>
          
          <div>
            <Label htmlFor="tags" className="text-sm font-medium text-foreground mb-1.5 block">
              Add Tags
            </Label>
            <Input 
              id="tags" 
              placeholder="Enter tags separated by commas" 
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20" 
            />
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="px-2 py-1 bg-muted/20">
              <span>Sales Call</span>
              <button className="ml-1 text-muted-foreground hover:text-foreground">×</button>
            </Badge>
            <Badge variant="outline" className="px-2 py-1 bg-muted/20">
              <span>Support</span>
              <button className="ml-1 text-muted-foreground hover:text-foreground">×</button>
            </Badge>
            <Badge variant="outline" className="px-2 py-1 bg-muted/20">
              <span>Follow-up</span>
              <button className="ml-1 text-muted-foreground hover:text-foreground">×</button>
            </Badge>
          </div>
        </div>
      </div>
      
      <div className="mt-8 flex justify-end space-x-3">
        <SheetClose asChild>
          <Button type="button" variant="outline" className="border-border/50 text-foreground hover:bg-muted/20">
            Cancel
          </Button>
        </SheetClose>
        
        <SheetClose asChild>
          <Button type="submit" className="btn-gradient text-white">
            Save Recording
          </Button>
        </SheetClose>
      </div>
    </>
  );
}
