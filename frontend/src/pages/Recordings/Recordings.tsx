import { useState, useEffect } from "react";
import { ModernTable } from "@/components/ModernTable";
import Header from "@/components/Header";
import Page from "@/components/Page";
import { useSearchParams, useNavigate } from "react-router-dom";
import RecordingGrid from "@/components/RecordingGrid";
import CustomPagination from "@/components/CustomPagination";
import RecordingFilterPopover from "@/components/RecordingFilterPopover";
import AudioPlayer from "@/components/AudioPlayer";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAllRecordings,
  deleteRecording,
  downloadRecording,
  shareRecording,
  getRecordingStreamUrl,
  type Recording,
  type RecordingFilters
} from "@/api/services/recordings/recordingService";
import {
  Edit,
  Eye,
  Trash,
  Plus,
  Calendar,
  FileAudio,
  Clock,
  Download,
  Play,
  Pause,
  User,
  Tag,
  Share2,
  MessageSquare,
} from "lucide-react";
import { AddRecording } from "./AddRecording";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";

const Recordings = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const [debouncedSearch, setDebouncedSearch] = useState(
    searchParams.get("search") || ""
  );
  const [searchQuery, setSearchQuery] = useState(debouncedSearch);
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    searchParams.get("view") === "grid" ? "grid" : "list"
  );
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [activeFilters, setActiveFilters] = useState<RecordingFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  // Extract current page from searchParams
  const currentPage = parseInt(searchParams.get("page") || "1");
  const queryClient = useQueryClient();

  // Prepare filters for API call
  const apiFilters: RecordingFilters = {
    search: searchQuery || undefined,
    ...activeFilters
  };

  // Fetch recordings using React Query
  const {
    data: recordingsResponse,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ["recordings", currentPage, apiFilters],
    queryFn: () => getAllRecordings(currentPage, 10, apiFilters),
    keepPreviousData: true,
  });

  // Delete recording mutation
  const deleteRecordingMutation = useMutation({
    mutationFn: deleteRecording,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["recordings"] });
    },
  });

  // Download recording mutation
  const downloadRecordingMutation = useMutation({
    mutationFn: ({ id, filename }: { id: string; filename?: string }) =>
      downloadRecording(id, filename),
  });

  // Share recording mutation
  const shareRecordingMutation = useMutation({
    mutationFn: ({ id, shareData }: { id: string; shareData: any }) =>
      shareRecording(id, shareData),
  });

  // Debouncing effect for search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(debouncedSearch);
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set("search", debouncedSearch);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    }, 500); // Reduced debounce time for better UX

    return () => clearTimeout(handler);
  }, [debouncedSearch, setSearchParams]);

  // Update URL when view mode changes
  useEffect(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (viewMode === "grid") {
        newParams.set("view", "grid");
      } else {
        newParams.delete("view");
      }
      return newParams;
    });
  }, [viewMode, setSearchParams]);

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", page.toString());
      return newParams;
    });
  };

  const handleSearchChange = (value: string) => {
    setDebouncedSearch(value);
  };

  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  const handleFilterChange = (filters: RecordingFilters) => {
    setActiveFilters(filters);
    setShowFilters(
      Object.keys(filters).some(
        (key) =>
          filters[key as keyof RecordingFilters] !== null &&
          filters[key as keyof RecordingFilters] !== undefined &&
          filters[key as keyof RecordingFilters] !== ""
      )
    );
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  // Get recordings data from API response
  const recordings = recordingsResponse?.recordings || [];
  const pagination = recordingsResponse?.pagination;

  // Function to get recording type based on name
  const getRecordingType = (recordingName: string) => {
    const lowerName = recordingName.toLowerCase();
    if (lowerName.includes("sales") || lowerName.includes("demo"))
      return "Sales";
    if (lowerName.includes("support") || lowerName.includes("feedback"))
      return "Support";
    if (lowerName.includes("follow-up")) return "Follow-up";
    return "General";
  };

  // Function to get badge variant based on recording type
  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case "Sales":
        return "success";
      case "Support":
        return "info";
      case "Follow-up":
        return "purple";
      default:
        return "secondary";
    }
  };

  // Toggle play/pause for a recording
  const togglePlayPause = (id: string) => {
    if (playingId === id) {
      setPlayingId(null);
    } else {
      setPlayingId(id);
    }
  };

  // Handle recording actions
  const handleDownload = (recording: Recording) => {
    downloadRecordingMutation.mutate({
      id: recording.id,
      filename: `${recording.recordingName}.${recording.format}`
    });
  };

  const handleShare = (recording: Recording) => {
    shareRecordingMutation.mutate({
      id: recording.id,
      shareData: { expiresIn: "7d" as const }
    });
  };

  const handleDelete = (recording: Recording) => {
    if (confirm(`Are you sure you want to delete "${recording.recordingName}"?`)) {
      deleteRecordingMutation.mutate(recording.id);
    }
  };

  const handleViewDetails = (recording: Recording) => {
    navigate(`/recordings/${recording.id}`);
  };

  return (
    <Page>
      <Header
        title="Voice Recordings"
        buttonText="Recording"
        action={() => console.log("Adding...")}
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={true}
        sheetContent={<AddRecording />}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        showViewToggle={true}
      />

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="px-3 py-1">
            <FileAudio className="h-3 w-3 mr-1" />
            <span>
              {isLoading ? "Loading..." : `${pagination?.totalRecordings || 0} Recordings`}
            </span>
          </Badge>
          {error && (
            <Badge variant="destructive" className="px-3 py-1">
              Error loading recordings
            </Badge>
          )}
        </div>

        <RecordingFilterPopover onFilterChange={handleFilterChange} />
      </div>
      
      {showFilters && (
        <div className="mb-4">
          {/* Active filters will be displayed by the FilterPopover component */}
        </div>
      )}

      {viewMode === "list" ? (
        <ModernTable
          data={recordings}
          isSelectable={true}
          showColumnSelection={true}
          onRowClick={(item) => handleViewDetails(item as Recording)}
          isLoading={isLoading}
          columns={[
          {
            header: "Recording",
            accessor: "recordingName",
            className: "min-w-[300px]",
            cell: (value, item) => {
              const recording = item as Recording;
              return (
                <div className="flex items-center space-x-3">
                  <div className="h-9 w-9 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                    <FileAudio className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-foreground">
                      {String(value)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      ID: {recording.id}
                    </div>
                  </div>
                </div>
              );
            },
            sortable: true,
          },
          {
            header: "Type",
            accessor: "type",
            className: "min-w-[120px]",
            cell: (value) => {
              return (
                <Badge
                  variant={getTypeBadgeVariant(String(value))}
                  className="capitalize"
                >
                  <Tag className="h-3 w-3 mr-1" />
                  {String(value)}
                </Badge>
              );
            },
            sortable: true,
          },
          {
            header: "Duration",
            accessor: "duration",
            className: "min-w-[120px]",
            cell: (value, item) => {
              const recording = item as Recording;
              return (
                <div className="flex items-center space-x-2">
                  <AudioPlayer
                    src={`/api/recordings/${recording.id}/stream`}
                    title={recording.recordingName}
                    duration={recording.duration}
                    compact={true}
                    onPlay={() => setPlayingId(recording.id)}
                    onPause={() => setPlayingId(null)}
                  />
                </div>
              );
            },
            sortable: true,
          },
          {
            header: "Date",
            accessor: "date",
            className: "min-w-[150px]",
            cell: (value) => (
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{formatDate(String(value))}</span>
              </div>
            ),
            sortable: true,
          },
          {
            header: "Contact",
            accessor: "phoneNumber",
            className: "min-w-[150px]",
            cell: (value, item) => {
              const recording = item as Recording;
              // Extract contact name from recording name if possible
              const nameMatch = recording.recordingName.match(
                /with\s+([A-Za-z\s]+)($|\s*\()/
              );
              const contactName = nameMatch ? nameMatch[1] : "Unknown";

              return (
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm">{contactName}</div>
                    {recording.phoneNumber && (
                      <div className="text-xs text-muted-foreground">
                        {recording.phoneNumber}
                      </div>
                    )}
                  </div>
                </div>
              );
            },
          },
        ]}
        actions={[
          {
            label: "View Details",
            icon: <Eye className="h-4 w-4" />,
            onClick: (item) => handleViewDetails(item as Recording),
          },
          {
            label: "Download",
            icon: <Download className="h-4 w-4" />,
            onClick: (item) => handleDownload(item as Recording),
          },
          {
            label: "Share",
            icon: <Share2 className="h-4 w-4" />,
            onClick: (item) => handleShare(item as Recording),
          },
          {
            label: "Edit Recording",
            icon: <Edit className="h-4 w-4" />,
            onClick: (item) => console.log("Editing", item),
          },
          {
            label: "Delete Recording",
            icon: <Trash className="h-4 w-4" />,
            onClick: (item) => handleDelete(item as Recording),
          },
        ]}
        emptyState={
          <div className="text-center p-8">
            <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <FileAudio className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-lg font-medium mb-1">No recordings found</h3>
            <p className="text-muted-foreground mb-4">
              Get started by adding your first voice recording
            </p>
            <button
              className="btn-gradient px-4 py-2 rounded-md text-white flex items-center justify-center mx-auto"
              onClick={() => console.log("Add recording")}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Recording
            </button>
          </div>
        }
      />
      ) : (
        <RecordingGrid
          data={recordings}
          onItemClick={(item) => handleViewDetails(item as Recording)}
          actions={[
            {
              label: "View Details",
              icon: <Eye className="h-4 w-4" />,
              onClick: (item: any) => handleViewDetails(item as Recording),
            },
            {
              label: "Download",
              icon: <Download className="h-4 w-4" />,
              onClick: (item: any) => handleDownload(item as Recording),
            },
            {
              label: "Share",
              icon: <Share2 className="h-4 w-4" />,
              onClick: (item: any) => handleShare(item as Recording),
            },
            {
              label: "Edit Recording",
              icon: <Edit className="h-4 w-4" />,
              onClick: (item: any) => console.log("Editing", item),
            },
            {
              label: "Delete Recording",
              icon: <Trash className="h-4 w-4" />,
              onClick: (item: any) => handleDelete(item as Recording),
            },
          ]}
          formatDate={formatDate}
          getRecordingType={(name: string) => (recordings.find(r => r.recordingName === name)?.type || "General")}
          getTypeBadgeVariant={getTypeBadgeVariant}
        />
      )}
      {pagination && pagination.totalPages > 1 && (
        <div className="mt-8 border-t border-gray-200 pt-4">
          <CustomPagination
            currentPage={currentPage}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </Page>
  );
};

export default Recordings;
