// NotFound.tsx

import React, { useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button"; // Adjust the import path based on your project structure
import { XCircleIcon } from "@heroicons/react/24/solid"; // Optional: For an additional icon
import gsap from "gsap";

const NotFound: React.FC = () => {
  const navigate = useNavigate();
  const svgRef = useRef<SVGSVGElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const messageRef = useRef<HTMLParagraphElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const tl = gsap.timeline({ defaults: { ease: "power1.out" } });

    tl.from(svgRef.current, {
      duration: 1,
      y: -50,
      opacity: 0,
      scale: 0.5,
    })
      .from(
        titleRef.current,
        {
          duration: 0.5,
          y: 20,
          opacity: 0,
        },
        "-=0.5"
      )
      .from(
        messageRef.current,
        {
          duration: 0.5,
          y: 20,
          opacity: 0,
        },
        "-=0.3"
      )
      .from(
        buttonRef.current,
        {
          duration: 0.5,
          y: 20,
          opacity: 0,
        },
        "-=0.3"
      );
  }, []);

  const handleGoHome = () => {
    navigate("/");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background text-primary px-4">
      <div className="max-w-md w-full text-center space-y-6">
        {/* Animated SVG */}
        <svg
          ref={svgRef}
          xmlns="http://www.w3.org/2000/svg"
          className="mx-auto h-40 w-40 text-primary animate-pulse"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          {/* Example SVG: Magnifying Glass */}
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>

        {/* Error Code */}
        <h1 className="text-6xl font-bold">404</h1>

        {/* Error Message */}
        <p className="text-xl">
          Oops! The page you're looking for doesn't exist.
        </p>

        {/* Call-to-Action Button */}
        <Button
          variant="default"
          size="lg"
          onClick={handleGoHome}
          className="flex items-center justify-center mx-auto"
        >
          <XCircleIcon className="h-5 w-5 mr-2" />
          Go to Homepage
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
