import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Play, Volume2, BarChart3, Mic } from "lucide-react";
import { toast } from "sonner";

import {
  getAvailableVoices,
  testVoiceSynthesis,
  getVoiceStats,
  createVoiceConfig,
  cloneVoice
} from "@/api/services/voices/voiceService";
import type { VoiceProvider, VoiceConfig } from "@/api/services/voices/voiceService";

const Voices = () => {
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [selectedVoice, setSelectedVoice] = useState<string>("");
  const [testText, setTestText] = useState("Hello, this is a test of the voice synthesis.");
  const [speed, setSpeed] = useState(1.0);
  const [pitch, setPitch] = useState(1.0);
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);
  const [cloneFiles, setCloneFiles] = useState<File[]>([]);
  const [cloneName, setCloneName] = useState("");
  const [cloneDescription, setCloneDescription] = useState("");

  const queryClient = useQueryClient();

  // Fetch available voices
  const { data: voicesData, isLoading: voicesLoading, error: voicesError } = useQuery({
    queryKey: ["voices", "available"],
    queryFn: getAvailableVoices,
    retry: 1,
  });

  // Fetch voice statistics
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ["voices", "stats"],
    queryFn: getVoiceStats,
    retry: 1,
  });

  // Test voice mutation
  const testVoiceMutation = useMutation({
    mutationFn: testVoiceSynthesis,
    onSuccess: (data) => {
      toast.success("Voice test completed successfully");
      console.log("Test result:", data);
    },
    onError: (error: any) => {
      console.error("Voice test failed:", error);
      toast.error(error.response?.data?.message || "Voice test failed");
    },
  });

  // Create voice config mutation
  const createConfigMutation = useMutation({
    mutationFn: createVoiceConfig,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["voices"] });
      setIsConfigDialogOpen(false);
      toast.success("Voice configuration created successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create voice configuration");
    },
  });

  // Clone voice mutation
  const cloneVoiceMutation = useMutation({
    mutationFn: cloneVoice,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["voices"] });
      setIsCloneDialogOpen(false);
      setCloneFiles([]);
      setCloneName("");
      setCloneDescription("");
      toast.success("Voice cloned successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to clone voice");
    },
  });

  const handleTestVoice = () => {
    if (!selectedProvider || !selectedVoice) {
      toast.error("Please select a provider and voice");
      return;
    }

    testVoiceMutation.mutate({
      provider: selectedProvider,
      voiceId: selectedVoice,
      text: testText,
      speed,
      pitch,
    });
  };

  const handleCreateConfig = (data: VoiceConfig) => {
    createConfigMutation.mutate(data);
  };

  const handleCloneVoice = () => {
    if (!selectedProvider || !cloneName || cloneFiles.length === 0) {
      toast.error("Please fill in all required fields and upload audio files");
      return;
    }

    cloneVoiceMutation.mutate({
      provider: selectedProvider,
      name: cloneName,
      description: cloneDescription,
      files: cloneFiles,
    });
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setCloneFiles(files);
  };

  const providers = voicesData?.data || {};

  if (voicesError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Voice Synthesis</h1>
          <p className="text-muted-foreground">
            Manage and configure text-to-speech voices from different providers
          </p>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                Unable to connect to the backend API. Please ensure the backend server is running.
              </p>
              <Button onClick={() => window.location.reload()}>
                Retry Connection
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Voice Synthesis</h1>
          <p className="text-muted-foreground">
            Manage and configure text-to-speech voices from different providers
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Play className="h-4 w-4 mr-2" />
                Test Voice
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Test Voice Synthesis</DialogTitle>
                <DialogDescription>
                  Test a voice with custom text and settings
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="provider">Provider</Label>
                    <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(providers).map((provider) => (
                          <SelectItem key={provider} value={provider}>
                            {provider}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="voice">Voice</Label>
                    <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select voice" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedProvider && Array.isArray(providers[selectedProvider]) && providers[selectedProvider]?.map((voice: any) => (
                          <SelectItem key={voice.id} value={voice.id}>
                            {voice.name} {voice.gender && `(${voice.gender})`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="text">Test Text</Label>
                  <Textarea
                    id="text"
                    value={testText}
                    onChange={(e) => setTestText(e.target.value)}
                    placeholder="Enter text to synthesize..."
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="speed">Speed: {speed}</Label>
                    <Input
                      id="speed"
                      type="range"
                      min="0.5"
                      max="2"
                      step="0.1"
                      value={speed}
                      onChange={(e) => setSpeed(parseFloat(e.target.value))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="pitch">Pitch: {pitch}</Label>
                    <Input
                      id="pitch"
                      type="range"
                      min="0.5"
                      max="2"
                      step="0.1"
                      value={pitch}
                      onChange={(e) => setPitch(parseFloat(e.target.value))}
                    />
                  </div>
                </div>
                <Button
                  onClick={handleTestVoice}
                  disabled={testVoiceMutation.isPending}
                  className="w-full"
                >
                  {testVoiceMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Test Voice
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isCloneDialogOpen} onOpenChange={setIsCloneDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Mic className="h-4 w-4 mr-2" />
                Clone Voice
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Clone Voice</DialogTitle>
                <DialogDescription>
                  Create a custom voice clone from audio samples
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="clone-provider">Provider</Label>
                  <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="11labs">11Labs</SelectItem>
                      <SelectItem value="playht">PlayHT</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="clone-name">Voice Name</Label>
                  <Input
                    id="clone-name"
                    value={cloneName}
                    onChange={(e) => setCloneName(e.target.value)}
                    placeholder="Enter voice name..."
                  />
                </div>
                <div>
                  <Label htmlFor="clone-description">Description (Optional)</Label>
                  <Textarea
                    id="clone-description"
                    value={cloneDescription}
                    onChange={(e) => setCloneDescription(e.target.value)}
                    placeholder="Describe the voice..."
                  />
                </div>
                <div>
                  <Label htmlFor="clone-files">Audio Files</Label>
                  <Input
                    id="clone-files"
                    type="file"
                    multiple
                    accept="audio/*"
                    onChange={handleFileUpload}
                  />
                  {cloneFiles.length > 0 && (
                    <div className="mt-2 text-sm text-muted-foreground">
                      {cloneFiles.length} file(s) selected
                    </div>
                  )}
                </div>
                <Button
                  onClick={handleCloneVoice}
                  disabled={cloneVoiceMutation.isPending}
                  className="w-full"
                >
                  {cloneVoiceMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Clone Voice
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Volume2 className="h-4 w-4 mr-2" />
                Create Config
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Voice Configuration</DialogTitle>
                <DialogDescription>
                  Create a new voice configuration for your assistants
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Voice configuration form will be implemented here.
                </p>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="providers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="providers">Available Voices</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          {voicesLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid gap-6">
              {Object.keys(providers).length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <p className="text-muted-foreground">
                        No voices available. Please check your backend configuration.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                Object.entries(providers).map(([providerName, voices]: [string, any]) => (
                  <Card key={providerName}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Volume2 className="h-5 w-5" />
                        {providerName}
                      </CardTitle>
                      <CardDescription>
                        {Array.isArray(voices) ? voices.length : 0} voices available
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {Array.isArray(voices) && voices.map((voice: any) => (
                          <Card key={voice.id || voice.name} className="p-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h4 className="font-semibold">{voice.name}</h4>
                                <div className="flex gap-1">
                                  {voice.gender && (
                                    <Badge variant="secondary">{voice.gender}</Badge>
                                  )}
                                  {voice.accent && (
                                    <Badge variant="outline">{voice.accent}</Badge>
                                  )}
                                </div>
                              </div>
                              {voice.description && (
                                <p className="text-sm text-muted-foreground">
                                  {voice.description}
                                </p>
                              )}
                              {voice.language && (
                                <div className="text-xs text-muted-foreground">
                                  Language: {voice.language}
                                </div>
                              )}
                              {voice.previewUrl && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="w-full"
                                  onClick={() => {
                                    const audio = new Audio(voice.previewUrl);
                                    audio.play().catch(console.error);
                                  }}
                                >
                                  <Play className="h-3 w-3 mr-1" />
                                  Preview
                                </Button>
                              )}
                            </div>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          {statsLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Synthesis</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statsData?.data?.totalSynthesis?.toLocaleString() || 0}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Characters</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statsData?.data?.totalCharacters?.toLocaleString() || 0}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Latency</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statsData?.data?.averageLatency || 0}ms
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {((statsData?.data?.errorRate || 0) * 100).toFixed(1)}%
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Voices;
