import { ModernTable } from "@/components/ModernTable";
import Header from "@/components/Header";
import Page from "@/components/Page";
import {
  Edit,
  Eye,
  Trash,
  Plus,
  Users,
  Activity,
  Circle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import AgentGrid from "@/components/AgentGrid";
import FilterPopover from "@/components/FilterPopover";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";

// apis
import { fetchAllAgents, deleteAssistant, getAssistantById } from "@/api/services/agents/agentService";
import CustomPagination from "@/components/CustomPagination";
import { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";

import Loading from "@/Loading";

const Agents = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const [debouncedSearch, setDebouncedSearch] = useState(
    searchParams.get("search") || ""
  );
  const [searchQuery, setSearchQuery] = useState(debouncedSearch);
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    searchParams.get("view") === "grid" ? "grid" : "list"
  );
  const [activeFilters, setActiveFilters] = useState<any>({});
  const [showFilters, setShowFilters] = useState(false);

  // Extract current page from searchParams
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Delete agent mutation
  const deleteAgentMutation = useMutation({
    mutationFn: deleteAssistant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
    },
  });

  // Debouncing effect for search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(debouncedSearch);
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set("search", debouncedSearch);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    }, 500); // Reduced debounce time for better UX

    return () => clearTimeout(handler);
  }, [debouncedSearch, setSearchParams]);

  // Update URL when view mode changes
  useEffect(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (viewMode === "grid") {
        newParams.set("view", "grid");
      } else {
        newParams.delete("view");
      }
      return newParams;
    });
  }, [viewMode, setSearchParams]);

  const { data, isLoading, error } = useQuery({
    queryKey: ["agents", currentPage, searchQuery, activeFilters],
    queryFn: () => fetchAllAgents(currentPage, searchQuery),
  });

  if (isLoading) return <Loading />;

  if (error) {
    console.error("Error fetching agents", error);
    return <div>Error loading agents</div>;
  }

  // Filter data based on activeFilters
  let filteredData = [...data.data];
  if (activeFilters.status) {
    // This is just for demo - in a real app, you'd filter based on actual data
    filteredData = filteredData.filter((_, index) => {
      const statuses = ["active", "inactive", "training"];
      const randomStatus = statuses[index % statuses.length];
      return randomStatus === activeFilters.status;
    });
  }

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", page.toString());
      return newParams;
    });
  };

  const handleSearchChange = (value: string) => {
    setDebouncedSearch(value);
  };

  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  const handleFilterChange = (filters: any) => {
    setActiveFilters(filters);
    setShowFilters(
      Object.keys(filters).some(
        (key) =>
          filters[key] !== null &&
          (typeof filters[key] !== "object" ||
            (Array.isArray(filters[key]) &&
              (filters[key][0] > 0 || filters[key][1] < 100)))
      )
    );
  };

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase();
  };



  // Function to get random metrics for demo
  const getRandomMetric = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1) + min);
  };

  // Handle agent actions
  const handleViewAgent = async (agent: any) => {
    try {
      const agentDetails = await getAssistantById(agent.id);
      console.log("Agent details:", agentDetails);
      // You could open a modal or navigate to a details page here
    } catch (error) {
      console.error("Error fetching agent details:", error);
    }
  };

  const handleEditAgent = (agent: any) => {
    navigate(`/agents/edit/${agent.id}`);
  };

  const handleDeleteAgent = async (agent: any) => {
    if (confirm(`Are you sure you want to delete "${agent.name}"?`)) {
      try {
        await deleteAgentMutation.mutateAsync(agent.id);
      } catch (error) {
        console.error("Error deleting agent:", error);
      }
    }
  };

  // Common actions for both list and grid views
  const agentActions = [
    {
      label: "View Details",
      icon: <Eye className="h-4 w-4" />,
      onClick: handleViewAgent,
    },
    {
      label: "Edit Agent",
      icon: <Edit className="h-4 w-4" />,
      onClick: handleEditAgent,
    },
    {
      label: "Delete Agent",
      icon: <Trash className="h-4 w-4" />,
      onClick: handleDeleteAgent,
    },
  ];

  return (
    <Page>
      <Header
        title="Voice AI Agents"
        buttonText="Agent"
        action={() => navigate("/agents/add")}
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={false}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        showViewToggle={true}
        showFilters={false} // We'll use our custom filter component instead
      />

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="px-3 py-1">
            <Users className="h-3 w-3 mr-1" />
            <span>{filteredData.length || 0} Agents</span>
          </Badge>
        </div>

        <FilterPopover onFilterChange={handleFilterChange} />
      </div>

      {showFilters && (
        <div className="mb-4">
          {/* Active filters will be displayed by the FilterPopover component */}
        </div>
      )}

      {viewMode === "list" ? (
        <div className="w-full overflow-x-auto">
          
          <ModernTable
            data={filteredData}
            isLoading={isLoading}
            isSelectable={true}
            showColumnSelection={true}
            onRowClick={(item) => console.log("Row clicked", item)}
            columns={[
              {
                header: "Name",
                accessor: "name",
                className: "min-w-[200px]",
                cell: (value, item) => (
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-9 w-9 bg-gradient-to-br from-primary/20 to-primary/10">
                      <AvatarFallback className="text-primary font-medium">
                        {getInitials(String(value))}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">
                        {String(value)}
                      </div>
                      <div className="text-xs text-gray-500">
                        ID: {String(item.id).substring(0, 8)}
                      </div>
                    </div>
                  </div>
                ),
                sortable: true,
              },
              {
                header: "Price",
                accessor: "id", // Using id as a placeholder
                className: "min-w-[120px]",
                cell: () => {
                  // Generate a random price for demo
                  const price = `$${(Math.random() * 100).toFixed(2)}`;
                  return (
                    <div className="text-left font-medium text-gray-900">
                      {price}
                    </div>
                  );
                },
                sortable: true,
              },
              {
                header: "Status",
                accessor: "user_id",
                className: "min-w-[120px]",
                cell: (_, __, index) => {
                  // This is just for demo - in a real app you'd have a proper status field
                  const statuses = ["active", "inactive", "training"];
                  const randomStatus = statuses[(index || 0) % statuses.length];
  
                  const statusColor =
                    randomStatus === "active"
                      ? "text-green-500"
                      : randomStatus === "inactive"
                      ? "text-gray-400"
                      : "text-yellow-500";
  
                  return (
                    <div className="flex items-center">
                      <Circle
                        className={`h-2 w-2 mr-2 fill-current ${statusColor}`}
                      />
                      <span className="capitalize text-gray-700">
                        {randomStatus}
                      </span>
                    </div>
                  );
                },
              },
              {
                header: "Total Sales",
                accessor: "created_at", // Using created_at as a placeholder
                className: "min-w-[120px]",
                cell: () => {
                  // Generate a random number for demo
                  const sales = getRandomMetric(0, 10000);
                  return (
                    <div className="text-left font-medium text-gray-900">
                      {sales > 0 ? sales.toLocaleString() : "—"}
                    </div>
                  );
                },
                sortable: true,
              },
              {
                header: "Total Revenue",
                accessor: "first_message", // Using first_message as a placeholder
                className: "min-w-[150px]",
                cell: () => {
                  // Generate a random revenue for demo
                  const revenue = getRandomMetric(0, 2000000);
                  const trend = Math.random() > 0.5 ? "up" : "down";

                  return (
                    <div className="flex items-start justify-start space-x-2">
                      <span className="font-medium text-gray-900">
                        {revenue > 0 ? `$${revenue.toLocaleString()}` : "—"}
                      </span>
                      {revenue > 0 && (
                        <Activity
                          className={`h-4 w-4 ${
                            trend === "up" ? "text-green-500" : "text-red-500"
                          }`}
                        />
                      )}
                    </div>
                  );
                },
                sortable: true,
              },
            ]}
            actions={agentActions}
            emptyState={
              <div className="text-center p-8">
                <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-medium mb-1">No agents found</h3>
                <p className="text-gray-500 mb-4">
                  Get started by creating your first voice AI agent
                </p>
                <button
                  className="bg-primary px-4 py-2 rounded-md text-white flex items-center justify-center mx-auto"
                  onClick={() => console.log("Create agent")}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Agent
                </button>
              </div>
            }
          />
        </div>
      ) : (
        <AgentGrid
          data={filteredData}
          onItemClick={(item) => console.log("Grid item clicked", item)}
          actions={agentActions}
        />
      )}

      <div className="mt-8 border-t border-gray-200 pt-4">
        <CustomPagination
          currentPage={currentPage}
          totalPages={data.totalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </Page>
  );
};

export default Agents;
