// src/pages/Agents/AddAgent.tsx

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Page from "@/components/Page";
import {
  ArrowLeft,
  Bot,
  MessageSquare,
  Mic,
  Sparkles,
  Volume2,
  Brain,
  Zap
} from "lucide-react";

export function AddAgent() {
  const navigate = useNavigate();
  const [selectedVoice, setSelectedVoice] = useState<"female" | "male">("female");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    firstMessage: "",
    voice: "female"
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Creating agent with data:", formData);
    // Here you would typically call an API to create the agent
    navigate("/agents");
  };

  const handleCancel = () => {
    navigate("/agents");
  };

  return (
    <Page>
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCancel}
            className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Agents
          </Button>
        </div>

        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary/20 to-accent/20 rounded-2xl mb-4">
            <Bot className="h-8 w-8 text-primary" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-2">
            Create Voice AI Agent
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Configure your new voice agent's settings and behavior to create personalized AI interactions
          </p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8">
        {/* Basic Information Card */}
        <Card className="border-border/50 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Sparkles className="h-5 w-5 text-primary" />
              </div>
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium flex items-center gap-2">
                  <Bot className="h-4 w-4 text-primary" />
                  Agent Name
                </Label>
                <Input
                  id="name"
                  placeholder="e.g., Customer Support Assistant"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="h-11 border-border/50 bg-background focus:border-primary/50 focus:ring-primary/20"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium flex items-center gap-2">
                  <Brain className="h-4 w-4 text-primary" />
                  Agent Type
                </Label>
                <div className="flex gap-2">
                  <Badge variant="secondary" className="px-3 py-1">
                    <Zap className="h-3 w-3 mr-1" />
                    Voice Assistant
                  </Badge>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Description
              </Label>
              <Textarea
                id="description"
                placeholder="Describe what this agent does and how it should behave..."
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                className="min-h-[120px] border-border/50 bg-background focus:border-primary/50 focus:ring-primary/20 resize-none"
              />
            </div>
          </CardContent>
        </Card>

        {/* Voice Settings Card */}
        <Card className="border-border/50 shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-xl">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Volume2 className="h-5 w-5 text-primary" />
              </div>
              Voice Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="first_message" className="text-sm font-medium flex items-center gap-2">
                <MessageSquare className="h-4 w-4 text-primary" />
                First Message
              </Label>
              <Textarea
                id="first_message"
                placeholder="Hello! I'm your AI assistant. How can I help you today?"
                value={formData.firstMessage}
                onChange={(e) => handleInputChange("firstMessage", e.target.value)}
                className="min-h-[100px] border-border/50 bg-background focus:border-primary/50 focus:ring-primary/20 resize-none"
              />
            </div>

            <div className="space-y-4">
              <Label className="text-sm font-medium flex items-center gap-2">
                <Mic className="h-4 w-4 text-primary" />
                Voice Type
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  className={`border-2 rounded-xl p-6 cursor-pointer transition-all duration-200 ${
                    selectedVoice === "female"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-border/50 hover:border-primary/30 hover:bg-primary/2"
                  }`}
                  onClick={() => {
                    setSelectedVoice("female");
                    handleInputChange("voice", "female");
                  }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${selectedVoice === "female" ? "bg-primary/20" : "bg-muted"}`}>
                        <Mic className={`h-5 w-5 ${selectedVoice === "female" ? "text-primary" : "text-muted-foreground"}`} />
                      </div>
                      <span className="font-semibold text-lg">Female Voice</span>
                    </div>
                    <div className={`h-5 w-5 rounded-full border-2 flex items-center justify-center ${
                      selectedVoice === "female" ? "border-primary" : "border-border"
                    }`}>
                      {selectedVoice === "female" && (
                        <div className="h-2.5 w-2.5 rounded-full bg-primary"></div>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Natural sounding female voice with clear pronunciation and warm, professional tone
                  </p>
                </div>

                <div
                  className={`border-2 rounded-xl p-6 cursor-pointer transition-all duration-200 ${
                    selectedVoice === "male"
                      ? "border-primary bg-primary/5 shadow-md"
                      : "border-border/50 hover:border-primary/30 hover:bg-primary/2"
                  }`}
                  onClick={() => {
                    setSelectedVoice("male");
                    handleInputChange("voice", "male");
                  }}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${selectedVoice === "male" ? "bg-primary/20" : "bg-muted"}`}>
                        <Mic className={`h-5 w-5 ${selectedVoice === "male" ? "text-primary" : "text-muted-foreground"}`} />
                      </div>
                      <span className="font-semibold text-lg">Male Voice</span>
                    </div>
                    <div className={`h-5 w-5 rounded-full border-2 flex items-center justify-center ${
                      selectedVoice === "male" ? "border-primary" : "border-border"
                    }`}>
                      {selectedVoice === "male" && (
                        <div className="h-2.5 w-2.5 rounded-full bg-primary"></div>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    Deep male voice with professional tone, clarity, and authoritative presence
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-end pt-6 border-t border-border/50">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="sm:w-auto w-full h-11 border-border/50 hover:bg-muted/50"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            className="sm:w-auto w-full h-11 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-white shadow-lg"
          >
            <Bot className="h-4 w-4 mr-2" />
            Create Agent
          </Button>
        </div>
      </form>
    </Page>
  );
}
