import { knowledgeBaseData } from "@/components/dynamicdata";
import { ModernTable } from "@/components/ModernTable";
import Header from "@/components/Header";
import Page from "@/components/Page";
import {
  Edit,
  Eye,
  Trash,
  Plus,
  BookOpen,
  Calendar,
  Tag,
  BarChart2,
  FileText,
  BookMarked,
  Share2,
  Circle,
} from "lucide-react";
import { AddKnowledgeBase } from "./AddKnowledgeBase";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import KnowledgeBaseGrid from "@/components/KnowledgeBaseGrid";
import KnowledgeBaseFilterPopover from "@/components/KnowledgeBaseFilterPopover";
import CustomPagination from "@/components/CustomPagination";

const KnowledgeBases = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [debouncedSearch, setDebouncedSearch] = useState(
    searchParams.get("search") || ""
  );
  const [searchQuery, setSearchQuery] = useState(debouncedSearch);
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    searchParams.get("view") === "grid" ? "grid" : "list"
  );
  const [activeFilters, setActiveFilters] = useState<any>({});
  const [showFilters, setShowFilters] = useState(false);

  // Extract current page from searchParams
  const currentPage = parseInt(searchParams.get("page") || "1");
  const itemsPerPage = 10;

  // Calculate total pages based on filtered data
  const totalPages = Math.ceil(knowledgeBaseData.length / itemsPerPage);

  // Debouncing effect for search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(debouncedSearch);
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set("search", debouncedSearch);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    }, 500); // Reduced debounce time for better UX

    return () => clearTimeout(handler);
  }, [debouncedSearch, setSearchParams]);

  // Update URL when view mode changes
  useEffect(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (viewMode === "grid") {
        newParams.set("view", "grid");
      } else {
        newParams.delete("view");
      }
      return newParams;
    });
  }, [viewMode, setSearchParams]);

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  // Function to get category badge variant
  const getCategoryBadgeVariant = (category: string) => {
    switch (category.toLowerCase()) {
      case "onboarding":
        return "success";
      case "features":
        return "purple";
      case "support":
        return "info";
      default:
        return "secondary";
    }
  };

  // Filter data based on search query and active filters
  const filteredData = knowledgeBaseData.filter((item) => {
    // Search filter
    const matchesSearch = searchQuery
      ? item.title.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    // Category filter
    const matchesCategory = activeFilters.category
      ? item.category.toLowerCase() === activeFilters.category.toLowerCase()
      : true;

    // Views filter
    const matchesViews = activeFilters.viewsRange
      ? item.views >= activeFilters.viewsRange[0] &&
        item.views <= activeFilters.viewsRange[1]
      : true;

    return matchesSearch && matchesCategory && matchesViews;
  });

  // Pagination
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", page.toString());
      return newParams;
    });
  };

  const handleSearchChange = (value: string) => {
    setDebouncedSearch(value);
  };

  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  const handleFilterChange = (filters: any) => {
    setActiveFilters(filters);
    setShowFilters(
      Object.keys(filters).some(
        (key) =>
          filters[key] !== null &&
          (typeof filters[key] !== "object" ||
            (Array.isArray(filters[key]) &&
              (filters[key][0] > 0 || filters[key][1] < 2000)))
      )
    );
  };

  // Common actions for both list and grid views
  const knowledgeBaseActions = [
    {
      label: "View Details",
      icon: <Eye className="h-4 w-4" />,
      onClick: (item: any) => console.log("Viewing", item),
    },
    {
      label: "Edit Knowledge Base",
      icon: <Edit className="h-4 w-4" />,
      onClick: (item: any) => console.log("Editing", item),
    },
    {
      label: "Delete Knowledge Base",
      icon: <Trash className="h-4 w-4" />,
      onClick: (item: any) => console.log("Deleting", item),
    },
  ];

  return (
    <Page>
      <Header
        title="Knowledge Library"
        buttonText="Knowledge Base"
        action={() => console.log("Adding...")}
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={true}
        sheetContent={<AddKnowledgeBase />}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        showViewToggle={true}
        showFilters={false} // We'll use our custom filter component instead
      />

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="px-3 py-1">
            <BookMarked className="h-3 w-3 mr-1" />
            <span>{filteredData.length} Knowledge Bases</span>
          </Badge>
        </div>

        <KnowledgeBaseFilterPopover onFilterChange={handleFilterChange} />
      </div>

      {showFilters && (
        <div className="mb-4">
          {/* Active filters will be displayed by the FilterPopover component */}
        </div>
      )}

      {viewMode === "list" ? (
        <ModernTable
          data={paginatedData}
          isSelectable={true}
          showColumnSelection={true}
          onRowClick={(item) => console.log("Row clicked", item)}
          columns={[
            {
              header: "Knowledge Base",
              accessor: "title",
              className: "min-w-[250px]",
              cell: (value, item) => (
                <div className="flex items-center space-x-3">
                  <div className="h-9 w-9 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                    <FileText className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">
                      {String(value)}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: KB-{String(item.id).padStart(4, "0")}
                    </div>
                  </div>
                </div>
              ),
              sortable: true,
            },
            {
              header: "Category",
              accessor: "category",
              className: "min-w-[150px]",
              cell: (value) => (
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={getCategoryBadgeVariant(String(value))}
                    className="capitalize"
                  >
                    <Tag className="h-3 w-3 mr-1" />
                    {String(value)}
                  </Badge>
                </div>
              ),
              sortable: true,
            },
            {
              header: "Views",
              accessor: "views",
              className: "min-w-[120px]",
              cell: (value) => (
                <div className="flex items-center space-x-2 justify-start">
                  <BarChart2 className="h-4 w-4 text-gray-400" />
                  <span className="font-medium text-gray-900">
                    {Number(value).toLocaleString()}
                  </span>
                </div>
              ),
              sortable: true,
            },
            {
              header: "Created",
              accessor: "created_at",
              className: "min-w-[150px]",
              cell: (value) => (
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-700">
                    {formatDate(String(value))}
                  </span>
                </div>
              ),
              sortable: true,
            },
          ]}
          actions={knowledgeBaseActions}
          emptyState={
            <div className="text-center p-8">
              <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <BookOpen className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-medium mb-1">
                No knowledge bases found
              </h3>
              <p className="text-gray-500 mb-4">
                Get started by creating your first knowledge base
              </p>
              <button
                className="bg-primary px-4 py-2 rounded-md text-white flex items-center justify-center mx-auto"
                onClick={() => console.log("Create knowledge base")}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Knowledge Base
              </button>
            </div>
          }
        />
      ) : (
        <KnowledgeBaseGrid
          data={paginatedData}
          onItemClick={(item) => console.log("Grid item clicked", item)}
          actions={knowledgeBaseActions}
          getCategoryBadgeVariant={getCategoryBadgeVariant}
          formatDate={formatDate}
        />
      )}

      <div className="mt-8 border-t border-gray-200 pt-4">
        <CustomPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </Page>
  );
};

export default KnowledgeBases;
