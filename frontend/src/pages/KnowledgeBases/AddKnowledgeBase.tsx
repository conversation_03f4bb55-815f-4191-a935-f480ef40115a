// src/pages/KnowledgeBases/AddKnowledgeBase.tsx

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  SheetClose,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { BookOpen, Tag, Upload, FileText, BookMarked } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export function AddKnowledgeBase() {
  return (
    <>
      <SheetTitle className="text-xl font-semibold jakarta bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-1">
        Create Knowledge Base
      </SheetTitle>
      <SheetDescription className="text-muted-foreground mb-6">
        Add a new knowledge base to your library for your voice AI agents.
      </SheetDescription>

      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <BookMarked className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Basic Information</h3>
          </div>

          <div>
            <Label
              htmlFor="title"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Title
            </Label>
            <Input
              id="title"
              placeholder="Enter knowledge base title"
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
          </div>

          <div>
            <Label
              htmlFor="category"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Category
            </Label>
            <Select>
              <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="onboarding">Onboarding</SelectItem>
                <SelectItem value="features">Features</SelectItem>
                <SelectItem value="support">Support</SelectItem>
                <SelectItem value="faq">FAQ</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label
              htmlFor="description"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Provide a brief description of this knowledge base..."
              className="w-full min-h-[80px] border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <FileText className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Content</h3>
          </div>

          <div>
            <Label
              htmlFor="content"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Knowledge Base Content
            </Label>
            <Textarea
              id="content"
              placeholder="Enter the content for your knowledge base..."
              className="w-full min-h-[200px] border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
          </div>

          <div className="border-2 border-dashed border-border/50 rounded-lg p-6 text-center">
            <div className="flex flex-col items-center justify-center">
              <Upload className="h-8 w-8 text-muted-foreground mb-2" />
              <h3 className="text-sm font-medium mb-1">Upload Files</h3>
              <p className="text-xs text-muted-foreground mb-3">
                Drag and drop files here or click to browse
              </p>
              <Button variant="outline" className="text-xs border-border/50">
                Browse Files
              </Button>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Tag className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Tags</h3>
          </div>

          <div>
            <Label
              htmlFor="tags"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Add Tags
            </Label>
            <Input
              id="tags"
              placeholder="Enter tags separated by commas"
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Tags help voice agents find relevant information quickly
            </p>
          </div>
        </div>
      </div>

      <div className="mt-8 flex justify-end space-x-3">
        <SheetClose asChild>
          <Button
            type="button"
            variant="outline"
            className="border-border/50 text-foreground hover:bg-muted/20"
          >
            Cancel
          </Button>
        </SheetClose>
        <SheetClose asChild>
          <Button type="submit" className="btn-gradient text-white">
            Create Knowledge Base
          </Button>
        </SheetClose>
      </div>
    </>
  );
}
