// src/pages/Campaigns/AddCampaign.tsx

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  SheetClose,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  Calendar as CalendarIcon,
  Megaphone,
  ListFilter,
  Users,
  Settings,
  BarChart,
  PlayCircle,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useState } from "react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

export function AddCampaign() {
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();

  return (
    <>
      <SheetTitle className="text-xl font-semibold jakarta bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-1">
        Create Voice Campaign
      </SheetTitle>
      <SheetDescription className="text-muted-foreground mb-6">
        Set up a new voice AI campaign to engage with your contacts.
      </SheetDescription>
      
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Megaphone className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Campaign Information</h3>
          </div>
          
          <div>
            <Label htmlFor="name" className="text-sm font-medium text-foreground mb-1.5 block">
              Campaign Name
            </Label>
            <Input 
              id="name" 
              placeholder="Enter campaign name" 
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20" 
            />
          </div>
          
          <div>
            <Label htmlFor="description" className="text-sm font-medium text-foreground mb-1.5 block">
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Describe the purpose of this campaign..."
              className="w-full min-h-[80px] border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startDate" className="text-sm font-medium text-foreground mb-1.5 block">
                Start Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : <span>Select date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            
            <div>
              <Label htmlFor="endDate" className="text-sm font-medium text-foreground mb-1.5 block">
                End Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : <span>Select date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <ListFilter className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Target Audience</h3>
          </div>
          
          <div>
            <Label htmlFor="list" className="text-sm font-medium text-foreground mb-1.5 block">
              Contact List
            </Label>
            <Select>
              <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                <SelectValue placeholder="Select a contact list" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newsletter">Newsletter Subscribers</SelectItem>
                <SelectItem value="event">Event Attendees</SelectItem>
                <SelectItem value="sales">Sales Leads</SelectItem>
                <SelectItem value="vip">VIP Customers</SelectItem>
                <SelectItem value="marketing">Marketing Campaign</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground mt-1">
              Select the contact list this campaign will target
            </p>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Filter by Activity</Label>
              <p className="text-xs text-muted-foreground">
                Only target contacts who have been active in the last 30 days
              </p>
            </div>
            <Switch />
          </div>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Settings className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Campaign Settings</h3>
          </div>
          
          <div>
            <Label htmlFor="type" className="text-sm font-medium text-foreground mb-1.5 block">
              Campaign Type
            </Label>
            <Select>
              <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                <SelectValue placeholder="Select campaign type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="outreach">Outreach</SelectItem>
                <SelectItem value="followup">Follow-up</SelectItem>
                <SelectItem value="promotional">Promotional</SelectItem>
                <SelectItem value="informational">Informational</SelectItem>
                <SelectItem value="survey">Survey</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="frequency" className="text-sm font-medium text-foreground mb-1.5 block">
                Call Frequency
              </Label>
              <Select>
                <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="once">Once</SelectItem>
                  <SelectItem value="daily">Daily</SelectItem>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="biweekly">Bi-weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="priority" className="text-sm font-medium text-foreground mb-1.5 block">
                Priority
              </Label>
              <Select>
                <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Enable Analytics</Label>
                <p className="text-xs text-muted-foreground">
                  Track detailed metrics for this campaign
                </p>
              </div>
              <Switch defaultChecked />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Auto-pause on Low Engagement</Label>
                <p className="text-xs text-muted-foreground">
                  Automatically pause campaign if engagement drops below threshold
                </p>
              </div>
              <Switch />
            </div>
          </div>
        </div>
      </div>
      
      <div className="mt-8 flex justify-end space-x-3">
        <Button 
          type="button" 
          variant="outline" 
          className="border-border/50 text-foreground hover:bg-muted/20 flex items-center"
          onClick={() => console.log("Testing campaign")}
        >
          <PlayCircle className="h-4 w-4 mr-2" />
          Test Campaign
        </Button>
        
        <SheetClose asChild>
          <Button type="button" variant="outline" className="border-border/50 text-foreground hover:bg-muted/20">
            Cancel
          </Button>
        </SheetClose>
        
        <SheetClose asChild>
          <Button type="submit" className="btn-gradient text-white">
            Create Campaign
          </Button>
        </SheetClose>
      </div>
    </>
  );
}
