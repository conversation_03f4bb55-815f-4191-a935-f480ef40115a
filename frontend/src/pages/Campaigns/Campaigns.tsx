import { ModernTable } from "@/components/ModernTable";
import Header from "@/components/Header";
import Page from "@/components/Page";
import {
  Edit,
  Trash,
  Plus,
  Megaphone,
  PauseCircle,
  PlayCircle,
  CheckCircle,
  Clock,
  Calendar,
  BarChart2,
  Users,
  SlidersHorizontal,
} from "lucide-react";
import { AddCampaign } from "./AddCampaign";
import { Badge } from "@/components/ui/badge";
import { campaignsData } from "@/data/campaignsData";
import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import CampaignGrid from "@/components/CampaignGrid";
import CampaignFilterPopover from "@/components/CampaignFilterPopover";
import CustomPagination from "@/components/CustomPagination";

const Campaigns = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [debouncedSearch, setDebouncedSearch] = useState(
    searchParams.get("search") || ""
  );
  const [searchQuery, setSearchQuery] = useState(debouncedSearch);
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    searchParams.get("view") === "grid" ? "grid" : "list"
  );
  const [activeFilters, setActiveFilters] = useState<any>({});
  const [showFilters, setShowFilters] = useState(false);
  
  // Extract current page from searchParams
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Debouncing effect for search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(debouncedSearch);
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set("search", debouncedSearch);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    }, 500); // Reduced debounce time for better UX

    return () => clearTimeout(handler);
  }, [debouncedSearch, setSearchParams]);

  // Update URL when view mode changes
  useEffect(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (viewMode === "grid") {
        newParams.set("view", "grid");
      } else {
        newParams.delete("view");
      }
      return newParams;
    });
  }, [viewMode, setSearchParams]);

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", page.toString());
      return newParams;
    });
  };

  const handleSearchChange = (value: string) => {
    setDebouncedSearch(value);
  };

  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  const handleFilterChange = (filters: any) => {
    setActiveFilters(filters);
    setShowFilters(
      Object.keys(filters).some(
        (key) =>
          filters[key] !== null &&
          (typeof filters[key] !== "object" ||
            (Array.isArray(filters[key]) &&
              (filters[key][0] > 0 || filters[key][1] < 100)))
      )
    );
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  // Add some more mock data for a better demo
  const enhancedCampaignsData = [
    ...campaignsData,
    {
      id: 3,
      campaignName: "Holiday Special",
      status: "Scheduled",
      startDate: "2024-12-01",
      endDate: "2024-12-25",
    },
    {
      id: 4,
      campaignName: "Customer Feedback",
      status: "Paused",
      startDate: "2024-09-15",
      endDate: "2024-10-15",
    },
    {
      id: 5,
      campaignName: "New Feature Announcement",
      status: "Draft",
      startDate: "2024-11-10",
      endDate: "2024-11-20",
    },
  ];

  // Function to get campaign type based on name
  const getCampaignType = (campaignName: string) => {
    const lowerName = campaignName.toLowerCase();
    if (
      lowerName.includes("sale") ||
      lowerName.includes("special") ||
      lowerName.includes("holiday")
    )
      return "Promotional";
    if (lowerName.includes("launch") || lowerName.includes("announcement"))
      return "Announcement";
    if (lowerName.includes("feedback")) return "Survey";
    return "General";
  };

  // Function to get badge variant based on status
  const getStatusBadgeVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "success";
      case "completed":
        return "secondary";
      case "scheduled":
        return "info";
      case "paused":
        return "warning";
      case "draft":
        return "outline";
      default:
        return "secondary";
    }
  };

  // Function to get status icon
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return <PlayCircle className="h-3 w-3 mr-1" />;
      case "completed":
        return <CheckCircle className="h-3 w-3 mr-1" />;
      case "scheduled":
        return <Clock className="h-3 w-3 mr-1" />;
      case "paused":
        return <PauseCircle className="h-3 w-3 mr-1" />;
      case "draft":
        return <Edit className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  // Filter data based on search query
  let filteredData = [...enhancedCampaignsData];
  if (searchQuery) {
    filteredData = filteredData.filter(item => 
      item.campaignName.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }
  
  // Filter data based on activeFilters
  if (activeFilters.status) {
    filteredData = filteredData.filter(item => 
      item.status.toLowerCase() === activeFilters.status.toLowerCase()
    );
  }

  // Common actions for both list and grid views
  const campaignActions = [
    {
      label: "View Analytics",
      icon: <BarChart2 className="h-4 w-4" />,
      onClick: (item: any) => console.log("Viewing analytics", item),
    },
    {
      label: "View Contacts",
      icon: <Users className="h-4 w-4" />,
      onClick: (item: any) => console.log("Viewing contacts", item),
    },
    {
      label: "Edit Campaign",
      icon: <Edit className="h-4 w-4" />,
      onClick: (item: any) => console.log("Editing", item),
    },
    {
      label: "Delete Campaign",
      icon: <Trash className="h-4 w-4" />,
      onClick: (item: any) => console.log("Deleting", item),
    },
  ];

  return (
    <Page>
      <Header
        title="Voice Campaigns"
        buttonText="Campaign"
        action={() => console.log("Adding...")}
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={true}
        sheetContent={<AddCampaign />}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        showViewToggle={true}
        showFilters={false} // We'll use our custom filter component instead
      />

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="px-3 py-1">
            <Megaphone className="h-3 w-3 mr-1" />
            <span>{filteredData.length} Campaigns</span>
          </Badge>
        </div>
        
        <CampaignFilterPopover onFilterChange={handleFilterChange} />
      </div>
      
      {showFilters && (
        <div className="mb-4">
          {/* Active filters will be displayed by the FilterPopover component */}
        </div>
      )}

      {viewMode === "list" ? (
        <ModernTable
          data={filteredData}
          isSelectable={true}
          showColumnSelection={true}
          onRowClick={(item) => console.log("Row clicked", item)}
          columns={[
          {
            header: "Campaign",
            accessor: "campaignName",
            className: "min-w-[250px]",
            cell: (value, item) => {
              const campaignType = getCampaignType(String(value));

              return (
                <div className="flex items-center space-x-3">
                  <div className="h-9 w-9 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                    <Megaphone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-foreground">
                      {String(value)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Type: {campaignType}
                    </div>
                  </div>
                </div>
              );
            },
            sortable: true,
          },
          {
            header: "Status",
            accessor: "status",
            className: "min-w-[120px]",
            cell: (value) => (
              <Badge
                variant={getStatusBadgeVariant(String(value))}
                className="capitalize"
              >
                {getStatusIcon(String(value))}
                {String(value)}
              </Badge>
            ),
            sortable: true,
          },
          {
            header: "Start Date",
            accessor: "startDate",
            className: "min-w-[150px]",
            cell: (value) => (
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{formatDate(String(value))}</span>
              </div>
            ),
            sortable: true,
          },
          {
            header: "End Date",
            accessor: "endDate",
            className: "min-w-[150px]",
            cell: (value) => (
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{formatDate(String(value))}</span>
              </div>
            ),
            sortable: true,
          },
          {
            header: "Progress",
            accessor: "id",
            className: "min-w-[150px]",
            cell: (value, item) => {
              // This is just for demo - in a real app you'd calculate actual progress
              const status = String(item.status).toLowerCase();
              let progress = 0;

              if (status === "completed") progress = 100;
              else if (status === "active") {
                // Calculate progress based on dates
                const start = new Date(String(item.startDate));
                const end = new Date(String(item.endDate));
                const now = new Date();

                if (now < start) progress = 0;
                else if (now > end) progress = 100;
                else {
                  const total = end.getTime() - start.getTime();
                  const current = now.getTime() - start.getTime();
                  progress = Math.round((current / total) * 100);
                }
              } else if (status === "paused")
                progress = Math.round(Number(value) * 17) % 100;
              else if (status === "scheduled") progress = 0;
              else if (status === "draft") progress = 0;

              return (
                <div className="w-full">
                  <div className="flex justify-between text-xs mb-1">
                    <span>{progress}% Complete</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                </div>
              );
            },
          },
        ]}
        actions={campaignActions}
        emptyState={
          <div className="text-center p-8">
            <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Megaphone className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-lg font-medium mb-1">No campaigns found</h3>
            <p className="text-muted-foreground mb-4">
              Get started by creating your first voice campaign
            </p>
            <button
              className="btn-gradient px-4 py-2 rounded-md text-white flex items-center justify-center mx-auto"
              onClick={() => console.log("Create campaign")}
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Campaign
            </button>
          </div>
        }
      />
      ) : (
        <CampaignGrid
          data={filteredData}
          onItemClick={(item) => console.log("Grid item clicked", item)}
          actions={campaignActions}
          formatDate={formatDate}
          getStatusBadgeVariant={getStatusBadgeVariant}
          getStatusIcon={getStatusIcon}
          getCampaignType={getCampaignType}
        />
      )}

      <div className="mt-8 border-t border-gray-200 pt-4">
        <CustomPagination
          currentPage={currentPage}
          totalPages={5} // This would come from your API in a real app
          onPageChange={handlePageChange}
        />
      </div>
    </Page>
  );
};

export default Campaigns;
