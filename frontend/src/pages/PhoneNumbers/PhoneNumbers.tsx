import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2, Phone, Plus, Search, ShoppingCart, Edit, Trash2 } from "lucide-react";
import { toast } from "sonner";

import {
  getAllPhoneNumbers,
  searchAvailablePhoneNumbers,
  buyPhoneNumber,
  updatePhoneNumber,
  deletePhoneNumber
} from "@/api/services/phoneNumbers/phoneNumberService";
import { fetchAllAgents } from "@/api/services/agents/agentService";
import type { PhoneNumber, AvailablePhoneNumber, PhoneNumberSearchParams } from "@/api/services/phoneNumbers/phoneNumberService";

const PhoneNumbers = () => {
  const [searchParams, setSearchParams] = useState<PhoneNumberSearchParams>({
    country: "US",
    limit: 10,
  });
  const [selectedNumber, setSelectedNumber] = useState<PhoneNumber | null>(null);
  const [isSearchDialogOpen, setIsSearchDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editForm, setEditForm] = useState({
    friendlyName: "",
    assistantId: "",
  });

  const queryClient = useQueryClient();

  // Fetch owned phone numbers
  const { data: phoneNumbersData, isLoading: numbersLoading, error: numbersError } = useQuery({
    queryKey: ["phoneNumbers"],
    queryFn: () => getAllPhoneNumbers(),
    retry: 1,
    onSuccess: (data) => {
      console.log("Phone numbers data received:", data);
      if (data?.data?.length > 0) {
        console.log("First phone number structure:", data.data[0]);
      }
    },
    onError: (error) => {
      console.error("Phone numbers fetch error:", error);
    }
  });

  // Fetch available phone numbers for purchase
  const { data: availableNumbers, isLoading: searchLoading, refetch: searchNumbers } = useQuery({
    queryKey: ["phoneNumbers", "available", searchParams],
    queryFn: () => searchAvailablePhoneNumbers(searchParams),
    enabled: false,
  });

  // Fetch assistants for assignment
  const { data: assistantsData } = useQuery({
    queryKey: ["assistants"],
    queryFn: () => fetchAllAgents(1, ""),
    retry: 1,
  });

  // Buy phone number mutation
  const buyNumberMutation = useMutation({
    mutationFn: buyPhoneNumber,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["phoneNumbers"] });
      toast.success("Phone number purchased successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to purchase phone number");
    },
  });

  // Update phone number mutation
  const updateNumberMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updatePhoneNumber(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["phoneNumbers"] });
      setIsEditDialogOpen(false);
      toast.success("Phone number updated successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to update phone number");
    },
  });

  // Delete phone number mutation
  const deleteNumberMutation = useMutation({
    mutationFn: deletePhoneNumber,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["phoneNumbers"] });
      toast.success("Phone number deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to delete phone number");
    },
  });

  const handleSearch = () => {
    searchNumbers();
  };

  const handleBuyNumber = (number: AvailablePhoneNumber) => {
    buyNumberMutation.mutate({
      number: number.number,
      friendlyName: number.friendlyName,
    });
  };

  const handleEditNumber = (phoneNumber: PhoneNumber) => {
    setSelectedNumber(phoneNumber);
    setEditForm({
      friendlyName: phoneNumber.friendlyName || "",
      assistantId: phoneNumber.assistantId || "",
    });
    setIsEditDialogOpen(true);
  };

  const handleUpdateNumber = () => {
    if (!selectedNumber) return;

    updateNumberMutation.mutate({
      id: selectedNumber.id,
      data: editForm,
    });
  };

  const handleDeleteNumber = (id: string) => {
    if (confirm("Are you sure you want to delete this phone number?")) {
      deleteNumberMutation.mutate(id);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      active: "default",
      inactive: "secondary",
      pending: "outline",
    };
    return <Badge variant={variants[status] || "outline"}>{status}</Badge>;
  };

  const assistants = assistantsData?.data?.assistants || [];

  if (numbersError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Phone Numbers</h1>
          <p className="text-muted-foreground">
            Manage your phone numbers and purchase new ones
          </p>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                Unable to connect to the backend API. Please ensure the backend server is running.
              </p>
              <Button onClick={() => window.location.reload()}>
                Retry Connection
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Phone Numbers</h1>
          <p className="text-muted-foreground">
            Manage your phone numbers and purchase new ones
          </p>
        </div>
        <Dialog open={isSearchDialogOpen} onOpenChange={setIsSearchDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Buy Number
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>Search & Buy Phone Numbers</DialogTitle>
              <DialogDescription>
                Search for available phone numbers to purchase
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Select
                    value={searchParams.country}
                    onValueChange={(value) => setSearchParams(prev => ({ ...prev, country: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="US">United States</SelectItem>
                      <SelectItem value="CA">Canada</SelectItem>
                      <SelectItem value="GB">United Kingdom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="areaCode">Area Code</Label>
                  <Input
                    id="areaCode"
                    value={searchParams.areaCode || ""}
                    onChange={(e) => setSearchParams(prev => ({ ...prev, areaCode: e.target.value }))}
                    placeholder="e.g., 415"
                  />
                </div>
                <div>
                  <Label htmlFor="contains">Contains</Label>
                  <Input
                    id="contains"
                    value={searchParams.contains || ""}
                    onChange={(e) => setSearchParams(prev => ({ ...prev, contains: e.target.value }))}
                    placeholder="e.g., 1234"
                  />
                </div>
                <div className="flex items-end">
                  <Button onClick={handleSearch} disabled={searchLoading} className="w-full">
                    {searchLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Search className="h-4 w-4 mr-2" />}
                    Search
                  </Button>
                </div>
              </div>

              {availableNumbers && (
                <div className="border rounded-lg">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Number</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Capabilities</TableHead>
                        <TableHead>Price/Month</TableHead>
                        <TableHead>Action</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {availableNumbers.data?.map((number: AvailablePhoneNumber) => (
                        <TableRow key={number.number}>
                          <TableCell className="font-mono">{number.number}</TableCell>
                          <TableCell>
                            {number.locality && number.region
                              ? `${number.locality}, ${number.region}`
                              : number.locality || number.region || "Unknown Location"
                            }
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-1">
                              {number.capabilities?.voice && <Badge variant="outline">Voice</Badge>}
                              {number.capabilities?.sms && <Badge variant="outline">SMS</Badge>}
                              {number.capabilities?.mms && <Badge variant="outline">MMS</Badge>}
                              {!number.capabilities && <Badge variant="outline">Unknown</Badge>}
                            </div>
                          </TableCell>
                          <TableCell>
                            {number.monthlyPrice !== undefined
                              ? `$${number.monthlyPrice}`
                              : "Contact for pricing"
                            }
                          </TableCell>
                          <TableCell>
                            <Button
                              size="sm"
                              onClick={() => handleBuyNumber(number)}
                              disabled={buyNumberMutation.isPending}
                            >
                              {buyNumberMutation.isPending ? (
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              ) : (
                                <ShoppingCart className="h-3 w-3 mr-1" />
                              )}
                              Buy
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Phone Numbers List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="h-5 w-5" />
            Your Phone Numbers
          </CardTitle>
          <CardDescription>
            Manage your purchased phone numbers and their assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {numbersLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Number</TableHead>
                  <TableHead>Friendly Name</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Capabilities</TableHead>
                  <TableHead>Assigned Assistant</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {phoneNumbersData?.data?.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center text-muted-foreground">
                      No phone numbers found. Purchase your first number to get started.
                    </TableCell>
                  </TableRow>
                ) : (
                  phoneNumbersData?.data?.map((phoneNumber: PhoneNumber) => (
                    <TableRow key={phoneNumber.id}>
                      <TableCell className="font-mono">{phoneNumber.number || "Unknown"}</TableCell>
                      <TableCell>{phoneNumber.friendlyName || "-"}</TableCell>
                      <TableCell>{getStatusBadge(phoneNumber.status || "inactive")}</TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {phoneNumber.capabilities?.voice && <Badge variant="outline">Voice</Badge>}
                          {phoneNumber.capabilities?.sms && <Badge variant="outline">SMS</Badge>}
                          {phoneNumber.capabilities?.mms && <Badge variant="outline">MMS</Badge>}
                          {!phoneNumber.capabilities && <Badge variant="outline">Unknown</Badge>}
                        </div>
                      </TableCell>
                      <TableCell>
                        {phoneNumber.assistantId ? (
                          <Badge variant="secondary">Assigned</Badge>
                        ) : (
                          <Badge variant="outline">Unassigned</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditNumber(phoneNumber)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeleteNumber(phoneNumber.id)}
                            disabled={deleteNumberMutation.isPending}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Phone Number</DialogTitle>
            <DialogDescription>
              Update the phone number settings
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="friendlyName">Friendly Name</Label>
              <Input
                id="friendlyName"
                value={editForm.friendlyName}
                onChange={(e) => setEditForm(prev => ({ ...prev, friendlyName: e.target.value }))}
                placeholder="Enter a friendly name..."
              />
            </div>
            <div>
              <Label htmlFor="assistantId">Assign to Assistant</Label>
              <Select
                value={editForm.assistantId}
                onValueChange={(value) => setEditForm(prev => ({ ...prev, assistantId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an assistant" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Unassigned</SelectItem>
                  {assistants.map((assistant: any) => (
                    <SelectItem key={assistant.id} value={assistant.id}>
                      {assistant.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button
              onClick={handleUpdateNumber}
              disabled={updateNumberMutation.isPending}
              className="w-full"
            >
              {updateNumberMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Update Phone Number
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PhoneNumbers;
