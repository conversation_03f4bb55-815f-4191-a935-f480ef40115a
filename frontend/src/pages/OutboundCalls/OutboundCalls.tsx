import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Loader2, Phone, Plus, Play, Square, Download, BarChart3, PhoneCall, FileAudio, Pause, Share2, Eye, Trash } from "lucide-react";
import { toast } from "sonner";
import AudioPlayer from "@/components/AudioPlayer";

import {
  createOutboundCall,
  getAllOutboundCalls,
  getOutboundCallById,
  endOutboundCall,
  getCallTranscript,
  getCallRecording,
  getCallStats,
  createBulkOutboundCalls
} from "@/api/services/outbound/outboundService";
import { fetchAllAgentsComplete, fetchAllAgentsFromVapi } from "@/api/services/agents/agentService";
import { getAllPhoneNumbers } from "@/api/services/phoneNumbers/phoneNumberService";
import {
  getAllRecordings,
  deleteRecording,
  downloadRecording,
  shareRecording,
  type Recording
} from "@/api/services/recordings/recordingService";
import type { OutboundCall, OutboundCallConfig } from "@/api/services/outbound/outboundService";

const OutboundCalls = () => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isBulkDialogOpen, setIsBulkDialogOpen] = useState(false);
  const [selectedCall, setSelectedCall] = useState<OutboundCall | null>(null);
  const [callForm, setCallForm] = useState<OutboundCallConfig>({
    assistantId: "",
    phoneNumber: "",
    firstMessage: "",
    voicemailMessage: "",
    maxDurationSeconds: 300,
  });
  const [bulkPhoneNumbers, setBulkPhoneNumbers] = useState("");
  const [useVapiAssistants, setUseVapiAssistants] = useState(false);
  const [playingRecordingId, setPlayingRecordingId] = useState<string | null>(null);

  const queryClient = useQueryClient();

  // Fetch outbound calls
  const { data: callsData, isLoading: callsLoading, error: callsError } = useQuery({
    queryKey: ["outboundCalls"],
    queryFn: () => getAllOutboundCalls(),
    retry: 1,
  });

  // Fetch assistants for selection (Database)
  const { data: assistantsData, isLoading: assistantsLoading } = useQuery({
    queryKey: ["assistants", "database"],
    queryFn: () => fetchAllAgentsComplete(""),
    retry: 1,
    enabled: !useVapiAssistants,
  });

  // Fetch assistants from VAPI
  const { data: vapiAssistantsData, isLoading: vapiAssistantsLoading } = useQuery({
    queryKey: ["assistants", "vapi"],
    queryFn: () => fetchAllAgentsFromVapi(),
    retry: 1,
    enabled: useVapiAssistants,
  });

  // Fetch phone numbers
  const { data: phoneNumbersData } = useQuery({
    queryKey: ["phoneNumbers"],
    queryFn: () => getAllPhoneNumbers(),
    retry: 1,
  });

  // Fetch call statistics
  const { data: statsData } = useQuery({
    queryKey: ["callStats"],
    queryFn: () => getCallStats(),
    retry: 1,
  });

  // Fetch all recordings
  const { data: recordingsData, isLoading: recordingsLoading } = useQuery({
    queryKey: ["recordings", "outbound"],
    queryFn: () => getAllRecordings(1, 50), // Get first 50 recordings
    retry: 1,
  });

  // Create outbound call mutation
  const createCallMutation = useMutation({
    mutationFn: createOutboundCall,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["outboundCalls"] });
      setIsCreateDialogOpen(false);
      setCallForm({
        assistantId: "",
        phoneNumber: "",
        firstMessage: "",
        voicemailMessage: "",
        maxDurationSeconds: 300,
      });
      toast.success("Outbound call initiated successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create outbound call");
    },
  });

  // Create bulk calls mutation
  const createBulkCallsMutation = useMutation({
    mutationFn: createBulkOutboundCalls,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["outboundCalls"] });
      setIsBulkDialogOpen(false);
      setBulkPhoneNumbers("");
      toast.success("Bulk outbound calls initiated successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create bulk outbound calls");
    },
  });

  // End call mutation
  const endCallMutation = useMutation({
    mutationFn: endOutboundCall,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["outboundCalls"] });
      toast.success("Call ended successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to end call");
    },
  });

  const handleCreateCall = () => {
    if (!callForm.assistantId || !callForm.phoneNumber) {
      toast.error("Please select an assistant and enter a phone number");
      return;
    }
    createCallMutation.mutate(callForm);
  };

  const handleBulkCalls = () => {
    if (!callForm.assistantId || !bulkPhoneNumbers.trim()) {
      toast.error("Please select an assistant and enter phone numbers");
      return;
    }

    const phoneNumbers = bulkPhoneNumbers
      .split('\n')
      .map(num => num.trim())
      .filter(num => num.length > 0);

    if (phoneNumbers.length === 0) {
      toast.error("Please enter at least one phone number");
      return;
    }

    createBulkCallsMutation.mutate({
      assistantId: callForm.assistantId,
      phoneNumbers,
      firstMessage: callForm.firstMessage,
      voicemailMessage: callForm.voicemailMessage,
      maxDurationSeconds: callForm.maxDurationSeconds,
    });
  };

  const handleEndCall = (callId: string) => {
    if (confirm("Are you sure you want to end this call?")) {
      endCallMutation.mutate(callId);
    }
  };

  const handleDownloadTranscript = async (callId: string) => {
    try {
      await getCallTranscript(callId);
    } catch (error) {
      console.error("Error downloading transcript:", error);
    }
  };

  const handleDownloadRecording = async (callId: string) => {
    try {
      const { downloadCallRecording } = await import("@/api/services/outbound/outboundService");
      await downloadCallRecording(callId);
    } catch (error) {
      console.error("Error downloading recording:", error);
    }
  };

  // Recording action handlers
  const handlePlayRecording = (recordingId: string) => {
    if (playingRecordingId === recordingId) {
      setPlayingRecordingId(null);
    } else {
      setPlayingRecordingId(recordingId);
    }
  };

  const handleDownloadRecordingFile = (recording: Recording) => {
    downloadRecording(recording.id, `${recording.recordingName}.${recording.format}`);
  };

  const handleShareRecording = (recording: Recording) => {
    shareRecording(recording.id, { expiresIn: "7d" });
  };

  const handleDeleteRecording = (recording: Recording) => {
    if (confirm(`Are you sure you want to delete "${recording.recordingName}"?`)) {
      deleteRecording(recording.id).then(() => {
        queryClient.invalidateQueries({ queryKey: ["recordings"] });
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const variants: Record<string, "default" | "secondary" | "destructive" | "outline"> = {
      queued: "outline",
      ringing: "secondary",
      "in-progress": "default",
      forwarding: "secondary",
      ended: "destructive",
    };
    return <Badge variant={variants[status] || "outline"}>{status}</Badge>;
  };

  // Get assistants from the appropriate source
  const assistants = useVapiAssistants
    ? (vapiAssistantsData?.data || [])
    : (assistantsData?.data || []);

  const phoneNumbers = phoneNumbersData?.data || [];
  const calls = callsData?.data || [];
  const recordings = recordingsData?.recordings || [];
  const isAssistantsLoading = useVapiAssistants ? vapiAssistantsLoading : assistantsLoading;

  if (callsError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Outbound Calls</h1>
          <p className="text-muted-foreground">
            Make and manage outbound calls with your AI assistants
          </p>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                Unable to connect to the backend API. Please ensure the backend server is running.
              </p>
              <Button onClick={() => window.location.reload()}>
                Retry Connection
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Outbound Calls</h1>
          <p className="text-muted-foreground">
            Make and manage outbound calls with your AI assistants
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isBulkDialogOpen} onOpenChange={setIsBulkDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <PhoneCall className="h-4 w-4 mr-2" />
                Bulk Calls
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Bulk Outbound Calls</DialogTitle>
                <DialogDescription>
                  Create multiple outbound calls at once
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="bulk-assistant-source">Assistant Source</Label>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="bulk-assistant-source" className="text-sm">Database</Label>
                    <Switch
                      id="bulk-assistant-source"
                      checked={useVapiAssistants}
                      onCheckedChange={(checked) => {
                        setUseVapiAssistants(checked);
                        setCallForm(prev => ({ ...prev, assistantId: "" })); // Reset selection
                      }}
                    />
                    <Label htmlFor="bulk-assistant-source" className="text-sm">VAPI</Label>
                  </div>
                </div>
                <div>
                  <Label htmlFor="bulk-assistant">
                    Assistant {isAssistantsLoading && <Loader2 className="h-3 w-3 animate-spin inline ml-1" />}
                  </Label>
                  <Select
                    value={callForm.assistantId}
                    onValueChange={(value) => setCallForm(prev => ({ ...prev, assistantId: value }))}
                    disabled={isAssistantsLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        isAssistantsLoading
                          ? "Loading assistants..."
                          : assistants.length === 0
                            ? "No assistants found"
                            : "Select an assistant"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {assistants.map((assistant: any) => (
                        <SelectItem key={assistant.id} value={assistant.id}>
                          {assistant.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {assistants.length === 0 && !isAssistantsLoading && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {useVapiAssistants
                        ? "No assistants found in VAPI. Try switching to Database source."
                        : "No assistants found in database. Try creating an assistant first."}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="bulk-numbers">Phone Numbers (one per line)</Label>
                  <Textarea
                    id="bulk-numbers"
                    value={bulkPhoneNumbers}
                    onChange={(e) => setBulkPhoneNumbers(e.target.value)}
                    placeholder="+1234567890&#10;+1987654321&#10;+1555123456"
                    rows={5}
                  />
                </div>
                <div>
                  <Label htmlFor="bulk-first-message">First Message (Optional)</Label>
                  <Textarea
                    id="bulk-first-message"
                    value={callForm.firstMessage || ""}
                    onChange={(e) => setCallForm(prev => ({ ...prev, firstMessage: e.target.value }))}
                    placeholder="Custom greeting message..."
                  />
                </div>
                <Button
                  onClick={handleBulkCalls}
                  disabled={createBulkCallsMutation.isPending}
                  className="w-full"
                >
                  {createBulkCallsMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Create Bulk Calls
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Make Call
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Outbound Call</DialogTitle>
                <DialogDescription>
                  Start a new outbound call with an AI assistant
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="assistant-source">Assistant Source</Label>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="assistant-source" className="text-sm">Database</Label>
                    <Switch
                      id="assistant-source"
                      checked={useVapiAssistants}
                      onCheckedChange={(checked) => {
                        setUseVapiAssistants(checked);
                        setCallForm(prev => ({ ...prev, assistantId: "" })); // Reset selection
                      }}
                    />
                    <Label htmlFor="assistant-source" className="text-sm">VAPI</Label>
                  </div>
                </div>
                <div>
                  <Label htmlFor="assistant">
                    Assistant {isAssistantsLoading && <Loader2 className="h-3 w-3 animate-spin inline ml-1" />}
                  </Label>
                  <Select
                    value={callForm.assistantId}
                    onValueChange={(value) => setCallForm(prev => ({ ...prev, assistantId: value }))}
                    disabled={isAssistantsLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        isAssistantsLoading
                          ? "Loading assistants..."
                          : assistants.length === 0
                            ? "No assistants found"
                            : "Select an assistant"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {assistants.map((assistant: any) => (
                        <SelectItem key={assistant.id} value={assistant.id}>
                          {assistant.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {assistants.length === 0 && !isAssistantsLoading && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {useVapiAssistants
                        ? "No assistants found in VAPI. Try switching to Database source."
                        : "No assistants found in database. Try creating an assistant first."}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    value={callForm.phoneNumber}
                    onChange={(e) => setCallForm(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    placeholder="+1234567890"
                  />
                </div>
                <div>
                  <Label htmlFor="firstMessage">First Message (Optional)</Label>
                  <Textarea
                    id="firstMessage"
                    value={callForm.firstMessage || ""}
                    onChange={(e) => setCallForm(prev => ({ ...prev, firstMessage: e.target.value }))}
                    placeholder="Custom greeting message..."
                  />
                </div>
                <div>
                  <Label htmlFor="voicemailMessage">Voicemail Message (Optional)</Label>
                  <Textarea
                    id="voicemailMessage"
                    value={callForm.voicemailMessage || ""}
                    onChange={(e) => setCallForm(prev => ({ ...prev, voicemailMessage: e.target.value }))}
                    placeholder="Message to leave if voicemail is detected..."
                  />
                </div>
                <div>
                  <Label htmlFor="maxDuration">Max Duration (seconds)</Label>
                  <Input
                    id="maxDuration"
                    type="number"
                    value={callForm.maxDurationSeconds}
                    onChange={(e) => setCallForm(prev => ({ ...prev, maxDurationSeconds: parseInt(e.target.value) }))}
                    min="30"
                    max="3600"
                  />
                </div>
                <Button
                  onClick={handleCreateCall}
                  disabled={createCallMutation.isPending}
                  className="w-full"
                >
                  {createCallMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Start Call
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="calls" className="space-y-4">
        <TabsList>
          <TabsTrigger value="calls">Active Calls</TabsTrigger>
          <TabsTrigger value="recordings">All Recordings</TabsTrigger>
          <TabsTrigger value="history">Call History</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="calls" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Outbound Calls
              </CardTitle>
              <CardDescription>
                Manage your outbound calls and view their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {callsLoading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Phone Number</TableHead>
                      <TableHead>Assistant</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Recording</TableHead>
                      <TableHead>Started</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {calls.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center text-muted-foreground">
                          No outbound calls found. Create your first call to get started.
                        </TableCell>
                      </TableRow>
                    ) : (
                      calls.map((call: OutboundCall) => (
                        <TableRow key={call.id}>
                          <TableCell className="font-mono">{call.phoneNumber}</TableCell>
                          <TableCell>{call.assistantId}</TableCell>
                          <TableCell>{getStatusBadge(call.status)}</TableCell>
                          <TableCell>
                            {call.duration ? `${Math.floor(call.duration / 60)}:${(call.duration % 60).toString().padStart(2, '0')}` : "-"}
                          </TableCell>
                          <TableCell>
                            {call.status === "ended" || call.recordingUrl ? (
                              <AudioPlayer
                                src={call.recordingUrl || `/api/outboundcall/recording/${call.id}`}
                                title={`Call Recording - ${call.phoneNumber}`}
                                duration={call.duration ? `${Math.floor(call.duration / 60)}:${(call.duration % 60).toString().padStart(2, '0')}` : "0:00"}
                                compact={true}
                                onPlay={() => setPlayingRecordingId(call.id)}
                                onPause={() => setPlayingRecordingId(null)}
                              />
                            ) : call.status === "in-progress" ? (
                              <span className="text-muted-foreground text-sm">Recording...</span>
                            ) : (
                              <span className="text-muted-foreground text-sm">No recording</span>
                            )}
                          </TableCell>
                          <TableCell>
                            {call.startedAt ? new Date(call.startedAt).toLocaleString() : "-"}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              {call.status === "in-progress" && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleEndCall(call.id)}
                                  disabled={endCallMutation.isPending}
                                >
                                  <Square className="h-3 w-3" />
                                </Button>
                              )}
                              {call.transcript && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDownloadTranscript(call.id)}
                                >
                                  <Download className="h-3 w-3" />
                                </Button>
                              )}
                              {(call.status === "ended" || call.recordingUrl) && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDownloadRecording(call.id)}
                                  title="Download Recording"
                                >
                                  <Download className="h-3 w-3" />
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recordings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileAudio className="h-5 w-5" />
                All Call Recordings
              </CardTitle>
              <CardDescription>
                View and manage all call recordings with inline playback
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recordingsLoading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Recording Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Duration</TableHead>
                      <TableHead>Audio Player</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {recordings.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center text-muted-foreground">
                          No recordings found. Recordings will appear here after calls are completed.
                        </TableCell>
                      </TableRow>
                    ) : (
                      recordings.map((recording: Recording) => (
                        <TableRow key={recording.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{recording.recordingName}</div>
                              <div className="text-xs text-muted-foreground">
                                ID: {recording.id}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{recording.type}</Badge>
                          </TableCell>
                          <TableCell>{recording.duration}</TableCell>
                          <TableCell>
                            <AudioPlayer
                              src={`/api/recordings/${recording.id}/stream`}
                              title={recording.recordingName}
                              duration={recording.duration}
                              compact={true}
                              onPlay={() => setPlayingRecordingId(recording.id)}
                              onPause={() => setPlayingRecordingId(null)}
                            />
                          </TableCell>
                          <TableCell>
                            {new Date(recording.date).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownloadRecordingFile(recording)}
                                title="Download Recording"
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleShareRecording(recording)}
                                title="Share Recording"
                              >
                                <Share2 className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(`/recordings/${recording.id}`, '_blank')}
                                title="View Details"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteRecording(recording)}
                                title="Delete Recording"
                              >
                                <Trash className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Call History</CardTitle>
              <CardDescription>View completed and failed calls</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">Call history will be displayed here.</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Calls</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statsData?.data?.totalCalls?.toLocaleString() || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Successful Calls</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statsData?.data?.successfulCalls?.toLocaleString() || 0}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {statsData?.data?.averageDuration ? `${Math.floor(statsData.data.averageDuration / 60)}:${(statsData.data.averageDuration % 60).toString().padStart(2, '0')}` : "0:00"}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Cost</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  ${statsData?.data?.totalCost?.toFixed(2) || "0.00"}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Recordings</CardTitle>
                <FileAudio className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {recordingsData?.pagination?.totalRecordings?.toLocaleString() || recordings.length}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OutboundCalls;
