import { useQuery } from "@tanstack/react-query";
import Page from "@/components/Page";
import Header from "@/components/Header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  PhoneCall,
  Clock,
  Users,
  Activity,
  CheckCircle,
  XCircle,
  AlertCircle,
  Mic,
  Loader2,
} from "lucide-react";

// Import API services
import { fetchAllAgents } from "@/api/services/agents/agentService";
import { getAllOutboundCalls, getCallStats } from "@/api/services/outbound/outboundService";
import { getModelStats } from "@/api/services/models/modelService";
import { getVoiceStats } from "@/api/services/voices/voiceService";
import { getAllPhoneNumbers } from "@/api/services/phoneNumbers/phoneNumberService";

// Modern chart components
import ModernBarChart from "@/components/dashboard/ModernBarChart";
import ModernLineChart from "@/components/dashboard/ModernLineChart";
import ModernPieChart from "@/components/dashboard/ModernPieChart";
import ModernProgressBar from "@/components/dashboard/ModernProgressBar";
import ModernStatCard from "@/components/dashboard/ModernStatCard";
import ModernInsightCard from "@/components/dashboard/ModernInsightCard";

// Mock data for dashboard
const callVolumeData = [
  { name: "Mon", calls: 120 },
  { name: "Tue", calls: 145 },
  { name: "Wed", calls: 160 },
  { name: "Thu", calls: 170 },
  { name: "Fri", calls: 190 },
  { name: "Sat", calls: 80 },
  { name: "Sun", calls: 60 },
];

const callDurationData = [
  { name: "Mon", avgDuration: 3.2 },
  { name: "Tue", avgDuration: 3.5 },
  { name: "Wed", avgDuration: 3.8 },
  { name: "Thu", avgDuration: 3.6 },
  { name: "Fri", avgDuration: 3.9 },
  { name: "Sat", avgDuration: 2.8 },
  { name: "Sun", avgDuration: 2.5 },
];

const callOutcomeData = [
  { name: "Successful", value: 65 },
  { name: "Failed", value: 15 },
  { name: "Abandoned", value: 20 },
];

const agentPerformanceData = [
  { name: "Sales Agent", successRate: 78, avgCallTime: 3.5, callsHandled: 245 },
  {
    name: "Support Agent",
    successRate: 82,
    avgCallTime: 4.2,
    callsHandled: 320,
  },
  {
    name: "Appointment Agent",
    successRate: 75,
    avgCallTime: 2.8,
    callsHandled: 180,
  },
  {
    name: "Survey Agent",
    successRate: 88,
    avgCallTime: 3.1,
    callsHandled: 210,
  },
];

const recentCalls = [
  {
    id: 1,
    agent: "Sales Agent",
    duration: "3:45",
    status: "Completed",
    outcome: "Successful",
    time: "10:30 AM",
  },
  {
    id: 2,
    agent: "Support Agent",
    duration: "5:12",
    status: "Completed",
    outcome: "Successful",
    time: "11:15 AM",
  },
  {
    id: 3,
    agent: "Appointment Agent",
    duration: "2:30",
    status: "Completed",
    outcome: "Failed",
    time: "12:05 PM",
  },
  {
    id: 4,
    agent: "Survey Agent",
    duration: "3:18",
    status: "Completed",
    outcome: "Successful",
    time: "1:30 PM",
  },
  {
    id: 5,
    agent: "Sales Agent",
    duration: "4:05",
    status: "Completed",
    outcome: "Abandoned",
    time: "2:45 PM",
  },
];

const Dashboard = () => {
  // Fetch real-time data from APIs
  const { data: agentsData, isLoading: agentsLoading } = useQuery({
    queryKey: ["dashboard", "agents"],
    queryFn: () => fetchAllAgents(1, ""),
    retry: 1,
  });

  const { data: callsData, isLoading: callsLoading } = useQuery({
    queryKey: ["dashboard", "calls"],
    queryFn: () => getAllOutboundCalls(),
    retry: 1,
  });

  const { data: callStatsData, isLoading: callStatsLoading } = useQuery({
    queryKey: ["dashboard", "callStats"],
    queryFn: () => getCallStats(),
    retry: 1,
  });

  const { data: modelStatsData } = useQuery({
    queryKey: ["dashboard", "modelStats"],
    queryFn: () => getModelStats(),
    retry: 1,
  });

  const { data: voiceStatsData } = useQuery({
    queryKey: ["dashboard", "voiceStats"],
    queryFn: () => getVoiceStats(),
    retry: 1,
  });

  const { data: phoneNumbersData } = useQuery({
    queryKey: ["dashboard", "phoneNumbers"],
    queryFn: () => getAllPhoneNumbers(),
    retry: 1,
  });

  // Calculate real metrics
  const totalAgents = agentsData?.data?.assistants?.length || 0;
  const totalCalls = callStatsData?.data?.totalCalls || callsData?.data?.length || 0;
  const avgDuration = callStatsData?.data?.averageDuration || 0;
  const successRate = callStatsData?.data?.successfulCalls && callStatsData?.data?.totalCalls
    ? ((callStatsData.data.successfulCalls / callStatsData.data.totalCalls) * 100).toFixed(1)
    : "0";

  const isLoading = agentsLoading || callsLoading || callStatsLoading;

  if (isLoading) {
    return (
      <Page>
        <Header
          title="Voice AI Dashboard"
          buttonText="New Campaign"
          action={() => console.log("Adding new campaign...")}
          filterByName={true}
        />
        <div className="p-6 flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Page>
    );
  }

  return (
    <Page>
      <Header
        title="Voice AI Dashboard"
        buttonText="New Campaign"
        action={() => console.log("Adding new campaign...")}
        filterByName={true}
      />

      <div className="p-6 space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <ModernStatCard
            title="Total Calls"
            value={totalCalls.toLocaleString()}
            icon={<PhoneCall className="h-5 w-5" />}
            trend={{
              value: "12%",
              direction: "up",
              label: "from last week"
            }}
            animationDelay={100}
          />

          <ModernStatCard
            title="Avg. Call Duration"
            value={avgDuration > 0 ? `${Math.floor(avgDuration / 60)}:${(avgDuration % 60).toString().padStart(2, '0')}` : "0:00"}
            icon={<Clock className="h-5 w-5" />}
            trend={{
              value: "5%",
              direction: "up",
              label: "from last week"
            }}
            iconColor="text-chart-2"
            iconBgColor="bg-gradient-to-br from-chart-2/20 to-chart-2/10"
            animationDelay={200}
          />

          <ModernStatCard
            title="Active Agents"
            value={totalAgents.toString()}
            icon={<Users className="h-5 w-5" />}
            trend={{
              value: "2 new",
              direction: "up",
              label: "this week"
            }}
            iconColor="text-chart-3"
            iconBgColor="bg-gradient-to-br from-chart-3/20 to-chart-3/10"
            animationDelay={300}
          />

          <ModernStatCard
            title="Success Rate"
            value={`${successRate}%`}
            icon={<Activity className="h-5 w-5" />}
            trend={{
              value: "3%",
              direction: "down",
              label: "from last week"
            }}
            iconColor="text-chart-4"
            iconBgColor="bg-gradient-to-br from-chart-4/20 to-chart-4/10"
            animationDelay={400}
          />
        </div>

        {/* Charts Row 1 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="card overflow-hidden">
            <CardHeader className="border-b border-border/50">
              <CardTitle className="text-lg font-semibold jakarta">
                Call Volume
              </CardTitle>
              <CardDescription>Number of calls per day</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="h-[300px]">
                <ModernBarChart
                  data={callVolumeData}
                  dataKeys={[
                    {
                      key: "calls",
                      name: "Number of Calls",
                      color: "hsl(var(--primary))"
                    }
                  ]}
                  xAxisDataKey="name"
                  height={300}
                  barRadius={[4, 4, 0, 0]}
                  tooltipFormatter={(value) => `${value} calls`}
                  tooltipLabelFormatter={(label) => `Day: ${label}`}
                  animationDuration={800}
                />
              </div>
            </CardContent>
          </Card>

          <Card className="card overflow-hidden">
            <CardHeader className="border-b border-border/50">
              <CardTitle className="text-lg font-semibold jakarta">
                Average Call Duration
              </CardTitle>
              <CardDescription>Average call time in minutes</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="h-[300px]">
                <ModernLineChart
                  data={callDurationData}
                  dataKeys={[
                    {
                      key: "avgDuration",
                      name: "Average Duration",
                      color: "hsl(var(--chart-2))",
                      strokeWidth: 2,
                      dotRadius: 4,
                      activeDotRadius: 6
                    }
                  ]}
                  xAxisDataKey="name"
                  height={300}
                  tooltipFormatter={(value) => `${value} min`}
                  tooltipLabelFormatter={(label) => `Day: ${label}`}
                  animationDuration={1200}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Row 2 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="card overflow-hidden">
            <CardHeader className="border-b border-border/50">
              <CardTitle className="text-lg font-semibold jakarta">
                Call Outcomes
              </CardTitle>
              <CardDescription>Distribution of call results</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="h-[300px] flex items-center justify-center">
                <ModernPieChart
                  data={callOutcomeData}
                  dataKey="value"
                  nameKey="name"
                  colors={["#4ade80", "#f87171", "#fbbf24"]}
                  height={300}
                  outerRadius={90}
                  innerRadius={0}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  tooltipFormatter={(value) => `${value} calls`}
                  animationDuration={1000}
                />
              </div>
            </CardContent>
          </Card>

          <Card className="col-span-1 lg:col-span-2 card overflow-hidden">
            <CardHeader className="border-b border-border/50">
              <CardTitle className="text-lg font-semibold jakarta">
                Agent Performance
              </CardTitle>
              <CardDescription>Success rates by agent type</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="h-[300px]">
                <ModernBarChart
                  data={agentPerformanceData}
                  dataKeys={[
                    {
                      key: "successRate",
                      name: "Success Rate (%)",
                      color: "hsl(var(--chart-1))"
                    },
                    {
                      key: "avgCallTime",
                      name: "Avg Call Time (min)",
                      color: "hsl(var(--chart-3))"
                    }
                  ]}
                  xAxisDataKey="name"
                  height={300}
                  barRadius={[4, 4, 0, 0]}
                  tooltipFormatter={(value, name) =>
                    name === "Success Rate (%)" ? `${value}%` : `${value} min`
                  }
                  tooltipLabelFormatter={(label) => `Agent: ${label}`}
                  animationDuration={1000}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Voice Agent Health Monitoring */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="card overflow-hidden">
            <CardHeader className="border-b border-border/50">
              <CardTitle className="text-lg font-semibold jakarta">
                Voice Agent Health
              </CardTitle>
              <CardDescription>Real-time performance metrics</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-5">
                <ModernProgressBar
                  label="Response Latency"
                  value={15}
                  maxValue={100}
                  status="success"
                  statusText="Excellent (450ms)"
                  height={8}
                  animationDuration={800}
                />

                <ModernProgressBar
                  label="Speech Recognition Accuracy"
                  value={92}
                  maxValue={100}
                  status="success"
                  statusText="Good (92%)"
                  height={8}
                  animationDuration={1000}
                />

                <ModernProgressBar
                  label="Voice Naturalness"
                  value={95}
                  maxValue={100}
                  status="success"
                  statusText="Excellent (95%)"
                  height={8}
                  animationDuration={1200}
                />

                <ModernProgressBar
                  label="API Reliability"
                  value={98.5}
                  maxValue={100}
                  status="warning"
                  statusText="Good (98.5%)"
                  height={8}
                  animationDuration={1400}
                />

                <ModernProgressBar
                  label="Call Completion Rate"
                  value={89}
                  maxValue={100}
                  status="success"
                  statusText="Good (89%)"
                  height={8}
                  animationDuration={1600}
                />
              </div>
            </CardContent>
          </Card>

          <Card className="card overflow-hidden">
            <CardHeader className="border-b border-border/50">
              <CardTitle className="text-lg font-semibold jakarta">
                Voice Agent Insights
              </CardTitle>
              <CardDescription>Key performance indicators</CardDescription>
            </CardHeader>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <ModernInsightCard
                  title="Average Words Per Minute"
                  value="145 WPM"
                  icon={<Mic className="h-6 w-6" />}
                  iconColor="text-primary"
                  iconBgColor="bg-gradient-to-br from-primary/20 to-primary/10"
                  animationDelay={100}
                />

                <ModernInsightCard
                  title="Interruption Rate"
                  value="3.2%"
                  icon={<Activity className="h-6 w-6" />}
                  iconColor="text-chart-3"
                  iconBgColor="bg-gradient-to-br from-chart-3/20 to-chart-3/10"
                  animationDelay={300}
                />

                <ModernInsightCard
                  title="Sentiment Score"
                  value="+0.68"
                  icon={<CheckCircle className="h-6 w-6" />}
                  iconColor="text-chart-4"
                  iconBgColor="bg-gradient-to-br from-chart-4/20 to-chart-4/10"
                  animationDelay={500}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Calls Table */}
        <Card className="card overflow-hidden">
          <CardHeader className="border-b border-border/50">
            <CardTitle className="text-lg font-semibold jakarta">
              Recent Calls
            </CardTitle>
            <CardDescription>Latest voice agent interactions</CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-border/50">
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">
                      ID
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">
                      Agent
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">
                      Duration
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">
                      Status
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">
                      Outcome
                    </th>
                    <th className="text-left py-3 px-4 font-medium text-muted-foreground">
                      Time
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {recentCalls.map((call) => (
                    <tr
                      key={call.id}
                      className="border-b border-border/30 hover:bg-secondary/40 transition-colors"
                    >
                      <td className="py-3 px-4 jakarta">#{call.id}</td>
                      <td className="py-3 px-4 flex items-center">
                        <div className="h-7 w-7 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center mr-2">
                          <Mic className="h-3.5 w-3.5 text-primary" />
                        </div>
                        <span className="jakarta">{call.agent}</span>
                      </td>
                      <td className="py-3 px-4 jakarta">{call.duration}</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100/80 text-green-800 jakarta">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          {call.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        {call.outcome === "Successful" && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100/80 text-green-800 jakarta">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            {call.outcome}
                          </span>
                        )}
                        {call.outcome === "Failed" && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100/80 text-red-800 jakarta">
                            <XCircle className="h-3 w-3 mr-1" />
                            {call.outcome}
                          </span>
                        )}
                        {call.outcome === "Abandoned" && (
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100/80 text-yellow-800 jakarta">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {call.outcome}
                          </span>
                        )}
                      </td>
                      <td className="py-3 px-4 jakarta">{call.time}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </Page>
  );
};

export default Dashboard;
