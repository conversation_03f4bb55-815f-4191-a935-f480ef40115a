import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Play, Settings, BarChart3, Zap } from "lucide-react";
import { toast } from "sonner";

import { getAvailableModels, testModelConfig, getModelStats, createModelConfig } from "@/api/services/models/modelService";
import type { ModelProvider, ModelConfig, ModelStats } from "@/api/services/models/modelService";

const Models = () => {
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState<string>("");
  const [testMessage, setTestMessage] = useState("Hello, how are you?");
  const [temperature, setTemperature] = useState(0.7);
  const [isTestDialogOpen, setIsTestDialogOpen] = useState(false);
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);

  const queryClient = useQueryClient();

  // Fetch available models
  const { data: modelsData, isLoading: modelsLoading, error: modelsError } = useQuery({
    queryKey: ["models", "available"],
    queryFn: getAvailableModels,
    retry: 1,
  });

  // Fetch model statistics
  const { data: statsData, isLoading: statsLoading } = useQuery({
    queryKey: ["models", "stats"],
    queryFn: getModelStats,
    retry: 1,
  });

  // Test model mutation
  const testModelMutation = useMutation({
    mutationFn: testModelConfig,
    onSuccess: (data) => {
      toast.success("Model test completed successfully");
      console.log("Test result:", data);
    },
    onError: (error: any) => {
      console.error("Model test failed:", error);
      toast.error(error.response?.data?.message || "Model test failed");
    },
  });

  // Create model config mutation
  const createConfigMutation = useMutation({
    mutationFn: createModelConfig,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["models"] });
      setIsConfigDialogOpen(false);
      toast.success("Model configuration created successfully");
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create model configuration");
    },
  });

  const handleTestModel = () => {
    if (!selectedProvider || !selectedModel) {
      toast.error("Please select a provider and model");
      return;
    }

    testModelMutation.mutate({
      provider: selectedProvider,
      model: selectedModel,
      message: testMessage,
      temperature,
    });
  };

  const handleCreateConfig = (data: ModelConfig) => {
    createConfigMutation.mutate(data);
  };

  const providers = modelsData?.data || {};

  if (modelsError) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Models</h1>
          <p className="text-muted-foreground">
            Manage and configure AI models from different providers
          </p>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                Unable to connect to the backend API. Please ensure the backend server is running.
              </p>
              <Button onClick={() => window.location.reload()}>
                Retry Connection
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI Models</h1>
          <p className="text-muted-foreground">
            Manage and configure AI models from different providers
          </p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isTestDialogOpen} onOpenChange={setIsTestDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Play className="h-4 w-4 mr-2" />
                Test Model
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Test AI Model</DialogTitle>
                <DialogDescription>
                  Test a model configuration with a sample message
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="provider">Provider</Label>
                    <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select provider" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.keys(providers).map((provider) => (
                          <SelectItem key={provider} value={provider}>
                            {provider}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="model">Model</Label>
                    <Select value={selectedModel} onValueChange={setSelectedModel}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select model" />
                      </SelectTrigger>
                      <SelectContent>
                        {selectedProvider && providers[selectedProvider]?.map((model: any) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div>
                  <Label htmlFor="message">Test Message</Label>
                  <Textarea
                    id="message"
                    value={testMessage}
                    onChange={(e) => setTestMessage(e.target.value)}
                    placeholder="Enter a test message..."
                  />
                </div>
                <div>
                  <Label htmlFor="temperature">Temperature: {temperature}</Label>
                  <Input
                    id="temperature"
                    type="range"
                    min="0"
                    max="2"
                    step="0.1"
                    value={temperature}
                    onChange={(e) => setTemperature(parseFloat(e.target.value))}
                  />
                </div>
                <Button
                  onClick={handleTestModel}
                  disabled={testModelMutation.isPending}
                  className="w-full"
                >
                  {testModelMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Test Model
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Settings className="h-4 w-4 mr-2" />
                Create Config
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Model Configuration</DialogTitle>
                <DialogDescription>
                  Create a new model configuration for your assistants
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Model configuration form will be implemented here.
                </p>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="providers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="providers">Available Models</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          {modelsLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid gap-6">
              {Object.keys(providers).length === 0 ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <p className="text-muted-foreground">
                        No models available. Please check your backend configuration.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                Object.entries(providers).map(([providerName, models]: [string, any]) => (
                  <Card key={providerName}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5" />
                        {providerName}
                      </CardTitle>
                      <CardDescription>
                        {Array.isArray(models) ? models.length : 0} models available
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {Array.isArray(models) && models.map((model: any) => (
                          <Card key={model.id || model.name} className="p-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <h4 className="font-semibold">{model.name}</h4>
                                <Badge variant="secondary">{model.provider || providerName}</Badge>
                              </div>
                              {model.description && (
                                <p className="text-sm text-muted-foreground">
                                  {model.description}
                                </p>
                              )}
                              {model.contextLength && (
                                <div className="text-xs text-muted-foreground">
                                  Context: {model.contextLength.toLocaleString()} tokens
                                </div>
                              )}
                              {model.pricing && (
                                <div className="text-xs text-muted-foreground">
                                  Input: ${model.pricing.input}/1K • Output: ${model.pricing.output}/1K
                                </div>
                              )}
                            </div>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="stats" className="space-y-4">
          {statsLoading ? (
            <div className="flex items-center justify-center h-64">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statsData?.data?.totalRequests?.toLocaleString() || 0}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Tokens</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statsData?.data?.totalTokens?.toLocaleString() || 0}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Latency</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {statsData?.data?.averageLatency || 0}ms
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                  <BarChart3 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {((statsData?.data?.errorRate || 0) * 100).toFixed(1)}%
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Models;
