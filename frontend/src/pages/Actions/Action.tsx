import { actionsData } from "@/components/dynamicdata";
import { ModernTable } from "@/components/ModernTable";
import Header from "@/components/Header";
import Page from "@/components/Page";
import {
  Edit,
  Eye,
  Trash,
  Plus,
  PlayCircle,
  Zap,
  Mail,
  Calendar,
  PhoneCall,
  Clock,
  AlertCircle,
  Share2,
  Circle,
} from "lucide-react";
import { AddAction } from "./AddAction";
import { Badge } from "@/components/ui/badge";
import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import ActionGrid from "@/components/ActionGrid";
import ActionFilterPopover from "@/components/ActionFilterPopover";
import CustomPagination from "@/components/CustomPagination";

const Action = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [debouncedSearch, setDebouncedSearch] = useState(
    searchParams.get("search") || ""
  );
  const [searchQuery, setSearchQuery] = useState(debouncedSearch);
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    searchParams.get("view") === "grid" ? "grid" : "list"
  );
  const [activeFilters, setActiveFilters] = useState<any>({});
  const [showFilters, setShowFilters] = useState(false);

  // Extract current page from searchParams
  const currentPage = parseInt(searchParams.get("page") || "1");
  const itemsPerPage = 10;

  // Debouncing effect for search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(debouncedSearch);
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set("search", debouncedSearch);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    }, 500); // Reduced debounce time for better UX

    return () => clearTimeout(handler);
  }, [debouncedSearch, setSearchParams]);

  // Update URL when view mode changes
  useEffect(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (viewMode === "grid") {
        newParams.set("view", "grid");
      } else {
        newParams.delete("view");
      }
      return newParams;
    });
  }, [viewMode, setSearchParams]);

  // Function to get action type and icon
  const getActionTypeInfo = (actionName: string) => {
    const actionTypes = {
      "Send Email": {
        icon: <Mail className="h-5 w-5 text-primary" />,
        badge: "purple",
      },
      "Schedule Meeting": {
        icon: <Calendar className="h-5 w-5 text-chart-2" />,
        badge: "info",
      },
      "Make Call": {
        icon: <PhoneCall className="h-5 w-5 text-chart-3" />,
        badge: "success",
      },
      "Set Reminder": {
        icon: <Clock className="h-5 w-5 text-chart-4" />,
        badge: "warning",
      },
      "Send Alert": {
        icon: <AlertCircle className="h-5 w-5 text-chart-5" />,
        badge: "destructive",
      },
    };

    // Default fallback
    return (
      actionTypes[actionName as keyof typeof actionTypes] || {
        icon: <Zap className="h-5 w-5 text-primary" />,
        badge: "secondary",
      }
    );
  };

  // Add some more mock data for a better demo
  const enhancedActionsData = [
    ...actionsData,
    {
      id: 3,
      actionName: "Make Call",
      description: "Initiate a call to the contact.",
    },
    {
      id: 4,
      actionName: "Set Reminder",
      description: "Set a reminder for follow-up tasks.",
    },
    {
      id: 5,
      actionName: "Send Alert",
      description: "Send an urgent alert notification.",
    },
  ];

  // Filter data based on search query and active filters
  const filteredData = enhancedActionsData.filter((item) => {
    // Search filter
    const matchesSearch = searchQuery
      ? item.actionName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    // Type filter
    const matchesType = activeFilters.type
      ? item.actionName
          .toLowerCase()
          .startsWith(activeFilters.type.toLowerCase())
      : true;

    // Status filter
    const matchesStatus = activeFilters.status
      ? (Number(item.id) % 3 === 0 && activeFilters.status === "Draft") ||
        (Number(item.id) % 3 === 1 && activeFilters.status === "Active") ||
        (Number(item.id) % 3 === 2 && activeFilters.status === "Inactive")
      : true;

    return matchesSearch && matchesType && matchesStatus;
  });

  // Calculate total pages based on filtered data
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  // Pagination
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", page.toString());
      return newParams;
    });
  };

  const handleSearchChange = (value: string) => {
    setDebouncedSearch(value);
  };

  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  const handleFilterChange = (filters: any) => {
    setActiveFilters(filters);
    setShowFilters(Object.keys(filters).some((key) => filters[key] !== null));
  };

  // Common actions for both list and grid views
  const actionActions = [
    {
      label: "Run Action",
      icon: <PlayCircle className="h-4 w-4" />,
      onClick: (item: any) => console.log("Running", item),
    },
    {
      label: "Edit Action",
      icon: <Edit className="h-4 w-4" />,
      onClick: (item: any) => console.log("Editing", item),
    },
    {
      label: "Delete Action",
      icon: <Trash className="h-4 w-4" />,
      onClick: (item: any) => console.log("Deleting", item),
    },
  ];

  return (
    <Page>
      <Header
        title="Voice AI Actions"
        buttonText="Action"
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={true}
        sheetContent={<AddAction />}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        showViewToggle={true}
        showFilters={false} // We'll use our custom filter component instead
      />

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="px-3 py-1">
            <Zap className="h-3 w-3 mr-1" />
            <span>{filteredData.length} Actions</span>
          </Badge>
        </div>

        <ActionFilterPopover onFilterChange={handleFilterChange} />
      </div>

      {showFilters && (
        <div className="mb-4">
          {/* Active filters will be displayed by the FilterPopover component */}
        </div>
      )}

      {viewMode === "list" ? (
        <ModernTable
          data={paginatedData}
          isSelectable={true}
          showColumnSelection={true}
          onRowClick={(item) => console.log("Row clicked", item)}
          columns={[
            {
              header: "Action",
              accessor: "actionName",
              className: "min-w-[250px]",
              cell: (value, item) => {
                const { icon } = getActionTypeInfo(String(value));

                return (
                  <div className="flex items-center space-x-3">
                    <div className="h-9 w-9 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                      {icon}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {String(value)}
                      </div>
                      <div className="text-xs text-gray-500">
                        ID: ACT-{String(item.id).padStart(4, "0")}
                      </div>
                    </div>
                  </div>
                );
              },
              sortable: true,
            },
            {
              header: "Description",
              accessor: "description",
              className: "min-w-[350px]",
              cell: (value) => (
                <div className="text-sm text-gray-600">{String(value)}</div>
              ),
            },
            {
              header: "Type",
              accessor: "actionName",
              className: "min-w-[150px]",
              cell: (value) => {
                const { badge } = getActionTypeInfo(String(value));
                const type = String(value).split(" ")[0]; // Get first word as type

                return (
                  <Badge variant={badge as any} className="capitalize">
                    {type}
                  </Badge>
                );
              },
              sortable: true,
            },
            {
              header: "Status",
              accessor: "id",
              className: "min-w-[120px]",
              cell: (value) => {
                // This is just for demo - in a real app you'd have a proper status field
                const statuses = ["Active", "Inactive", "Draft"];
                const statusVariants = ["success", "secondary", "warning"];
                const index = Number(value) % 3;

                return (
                  <div className="flex items-center justify-start">
                    <Badge
                      variant={statusVariants[index] as any}
                      className="capitalize"
                    >
                      {statuses[index]}
                    </Badge>
                  </div>
                  
                );
              },
            },
          ]}
          actions={actionActions}
          emptyState={
            <div className="text-center p-8">
              <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-medium mb-1">No actions found</h3>
              <p className="text-gray-500 mb-4">
                Get started by creating your first voice AI action
              </p>
              <button
                className="bg-primary px-4 py-2 rounded-md text-white flex items-center justify-center mx-auto"
                onClick={() => console.log("Create action")}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Action
              </button>
            </div>
          }
        />
      ) : (
        <ActionGrid
          data={paginatedData}
          onItemClick={(item) => console.log("Grid item clicked", item)}
          actions={actionActions}
          getActionTypeInfo={getActionTypeInfo}
        />
      )}

      <div className="mt-8 border-t border-gray-200 pt-4">
        <CustomPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </Page>
  );
};

export default Action;
