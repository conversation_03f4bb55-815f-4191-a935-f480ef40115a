// src/pages/Actions/AddAction.tsx

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  SheetClose,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  Zap,
  Settings,
  Code,
  PlayCircle,
  Mail,
  Calendar,
  PhoneCall,
  Clock,
  AlertCircle,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

export function AddAction() {
  return (
    <>
      <SheetTitle className="text-xl font-semibold jakarta bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-1">
        Create Voice AI Action
      </SheetTitle>
      <SheetDescription className="text-muted-foreground mb-6">
        Configure a new action for your voice AI agents to perform.
      </SheetDescription>

      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Zap className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Basic Information</h3>
          </div>

          <div>
            <Label
              htmlFor="name"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Action Name
            </Label>
            <Input
              id="name"
              placeholder="Enter action name"
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
          </div>

          <div>
            <Label
              htmlFor="type"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Action Type
            </Label>
            <Select>
              <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                <SelectValue placeholder="Select action type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="email">
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 mr-2 text-primary" />
                    <span>Send Email</span>
                  </div>
                </SelectItem>
                <SelectItem value="meeting">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-chart-2" />
                    <span>Schedule Meeting</span>
                  </div>
                </SelectItem>
                <SelectItem value="call">
                  <div className="flex items-center">
                    <PhoneCall className="h-4 w-4 mr-2 text-chart-3" />
                    <span>Make Call</span>
                  </div>
                </SelectItem>
                <SelectItem value="reminder">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-chart-4" />
                    <span>Set Reminder</span>
                  </div>
                </SelectItem>
                <SelectItem value="alert">
                  <div className="flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2 text-chart-5" />
                    <span>Send Alert</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label
              htmlFor="description"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Describe what this action does..."
              className="w-full min-h-[100px] border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Settings className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Action Configuration</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label
                htmlFor="trigger"
                className="text-sm font-medium text-foreground mb-1.5 block"
              >
                Trigger Phrase
              </Label>
              <Input
                id="trigger"
                placeholder="e.g., 'Send an email to...'"
                className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Voice command that triggers this action
              </p>
            </div>

            <div>
              <Label
                htmlFor="priority"
                className="text-sm font-medium text-foreground mb-1.5 block"
              >
                Priority
              </Label>
              <Select>
                <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">
                  Require Confirmation
                </Label>
                <p className="text-xs text-muted-foreground">
                  Ask for confirmation before executing
                </p>
              </div>
              <Switch />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">Active</Label>
                <p className="text-xs text-muted-foreground">
                  Enable this action for voice agents
                </p>
              </div>
              <Switch defaultChecked />
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Code className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Advanced Settings</h3>
          </div>

          <div className="border border-border/50 rounded-lg p-4 bg-muted/20">
            <Label
              htmlFor="code"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Custom Code (Optional)
            </Label>
            <Textarea
              id="code"
              placeholder="// Add custom code for this action"
              className="w-full min-h-[120px] font-mono text-xs border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
            />
            <p className="text-xs text-muted-foreground mt-2">
              Advanced users can add custom code to extend this action's
              functionality
            </p>
          </div>
        </div>
      </div>

      <div className="mt-8 flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          className="border-border/50 text-foreground hover:bg-muted/20 flex items-center"
          onClick={() => console.log("Testing action")}
        >
          <PlayCircle className="h-4 w-4 mr-2" />
          Test Action
        </Button>

        <SheetClose asChild>
          <Button
            type="button"
            variant="outline"
            className="border-border/50 text-foreground hover:bg-muted/20"
          >
            Cancel
          </Button>
        </SheetClose>

        <SheetClose asChild>
          <Button type="submit" className="btn-gradient text-white">
            Create Action
          </Button>
        </SheetClose>
      </div>
    </>
  );
}
