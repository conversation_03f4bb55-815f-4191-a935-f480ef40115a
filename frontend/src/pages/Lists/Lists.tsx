import { useState, useEffect } from "react";
import { contactListsData } from "@/components/dynamicdata";
import { ModernTable } from "@/components/ModernTable";
import Header from "@/components/Header";
import Page from "@/components/Page";
import {
  Edit,
  Eye,
  Trash,
  Plus,
  Users,
  Calendar,
  ListFilter,
  Mail,
  UserPlus,
  ListChecks,
  Share2,
  MoreHorizontal,
} from "lucide-react";
import { AddContactList } from "./AddContactList";
import { Badge } from "@/components/ui/badge";
import { useSearchParams } from "react-router-dom";
import ListGrid from "@/components/ListGrid";
import ListFilterPopover from "@/components/ListFilterPopover";
import CustomPagination from "@/components/CustomPagination";

const Lists = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [debouncedSearch, setDebouncedSearch] = useState(
    searchParams.get("search") || ""
  );
  const [searchQuery, setSearchQuery] = useState(debouncedSearch);
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    searchParams.get("view") === "grid" ? "grid" : "list"
  );
  const [activeFilters, setActiveFilters] = useState<any>({});
  const [showFilters, setShowFilters] = useState(false);

  // Extract current page from searchParams
  const currentPage = parseInt(searchParams.get("page") || "1");
  const itemsPerPage = 10;

  // Debouncing effect for search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(debouncedSearch);
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set("search", debouncedSearch);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    }, 500); // Reduced debounce time for better UX

    return () => clearTimeout(handler);
  }, [debouncedSearch, setSearchParams]);

  // Update URL when view mode changes
  useEffect(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (viewMode === "grid") {
        newParams.set("view", "grid");
      } else {
        newParams.delete("view");
      }
      return newParams;
    });
  }, [viewMode, setSearchParams]);

  // Function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(date);
  };

  // Add some more mock data for a better demo
  const enhancedContactListsData = [
    ...contactListsData,
    {
      id: 3,
      listName: "Sales Leads",
      contactsCount: 175,
      created_at: "2024-08-05",
    },
    {
      id: 4,
      listName: "VIP Customers",
      contactsCount: 50,
      created_at: "2024-09-10",
    },
    {
      id: 5,
      listName: "Marketing Campaign",
      contactsCount: 320,
      created_at: "2024-10-01",
    },
  ];

  const handleSearchChange = (value: string) => {
    setDebouncedSearch(value);
  };

  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  const handleFilterChange = (filters: any) => {
    setActiveFilters(filters);
    setShowFilters(
      Object.keys(filters).some(
        (key) =>
          filters[key] !== null &&
          (typeof filters[key] !== "object" ||
            (Array.isArray(filters[key]) &&
              (filters[key][0] > 0 || filters[key][1] < 500)))
      )
    );
  };

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", page.toString());
      return newParams;
    });
  };

  // Filter data based on search query and active filters
  const filteredData = enhancedContactListsData.filter((item) => {
    // Search filter
    const matchesSearch = searchQuery
      ? item.listName.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    // Type filter
    const matchesType = activeFilters.type
      ? getListType(item.listName) === activeFilters.type
      : true;

    // Contacts range filter
    const matchesContactsRange = activeFilters.contactsRange
      ? item.contactsCount >= activeFilters.contactsRange[0] &&
        item.contactsCount <= activeFilters.contactsRange[1]
      : true;

    return matchesSearch && matchesType && matchesContactsRange;
  });

  // Calculate total pages based on filtered data
  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  // Pagination
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Function to get list type based on name
  const getListType = (listName: string) => {
    const lowerName = listName.toLowerCase();
    if (lowerName.includes("newsletter") || lowerName.includes("campaign"))
      return "Marketing";
    if (lowerName.includes("event") || lowerName.includes("attendee"))
      return "Event";
    if (lowerName.includes("sales") || lowerName.includes("lead"))
      return "Sales";
    if (lowerName.includes("vip") || lowerName.includes("customer"))
      return "Customer";
    return "General";
  };

  // Function to get badge variant based on list type
  const getListBadgeVariant = (listType: string) => {
    switch (listType) {
      case "Marketing":
        return "purple";
      case "Event":
        return "info";
      case "Sales":
        return "success";
      case "Customer":
        return "warning";
      default:
        return "secondary";
    }
  };

  // Common actions for both list and grid views
  const listActions = [
    {
      label: "View Contacts",
      icon: <Users className="h-4 w-4" />,
      onClick: (item: any) => console.log("Viewing", item),
    },
    {
      label: "Send Campaign",
      icon: <Mail className="h-4 w-4" />,
      onClick: (item: any) => console.log("Sending campaign to", item),
    },
    {
      label: "Edit List",
      icon: <Edit className="h-4 w-4" />,
      onClick: (item: any) => console.log("Editing", item),
    },
    {
      label: "Delete List",
      icon: <Trash className="h-4 w-4" />,
      onClick: (item: any) => console.log("Deleting", item),
    },
  ];

  return (
    <Page>
      <Header
        title="Contact Lists"
        buttonText="List"
        action={() => console.log("Adding...")}
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={true}
        sheetContent={<AddContactList />}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        showViewToggle={true}
        showFilters={false} // We'll use our custom filter component instead
      />

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="px-3 py-1">
            <ListChecks className="h-3 w-3 mr-1" />
            <span>{filteredData.length} Lists</span>
          </Badge>
        </div>

        <ListFilterPopover onFilterChange={handleFilterChange} />
      </div>

      {showFilters && (
        <div className="mb-4">
          {/* Active filters will be displayed by the FilterPopover component */}
        </div>
      )}

      {viewMode === "list" ? (
        <ModernTable
          data={paginatedData}
          isSelectable={true}
          showColumnSelection={true}
          onRowClick={(item) => console.log("Row clicked", item)}
          columns={[
            {
              header: "List Name",
              accessor: "listName",
              className: "min-w-[250px]",
              cell: (value, item) => {
                const listType = getListType(String(value));

                return (
                  <div className="flex items-center space-x-3">
                    <div className="h-9 w-9 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                      <ListFilter className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {String(value)}
                      </div>
                      <div className="text-xs text-gray-500">
                        ID: LIST-{String(item.id).padStart(4, "0")}
                      </div>
                    </div>
                  </div>
                );
              },
              sortable: true,
            },
            {
              header: "Type",
              accessor: "listName",
              className: "min-w-[150px]",
              cell: (value) => {
                const listType = getListType(String(value));

                return (
                  <Badge
                    variant={getListBadgeVariant(listType)}
                    className="capitalize"
                  >
                    {listType}
                  </Badge>
                );
              },
              sortable: true,
            },
            {
              header: "Contacts",
              accessor: "contactsCount",
              className: "min-w-[150px]",
              cell: (value) => (
                <div className="flex items-center space-x-2 justify-end">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="font-medium text-gray-900">
                    {Number(value).toLocaleString()}
                  </span>
                </div>
              ),
              sortable: true,
            },
            {
              header: "Created",
              accessor: "created_at",
              className: "min-w-[150px]",
              cell: (value) => (
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-700">
                    {formatDate(String(value))}
                  </span>
                </div>
              ),
              sortable: true,
            },
          ]}
          actions={listActions}
          emptyState={
            <div className="text-center p-8">
              <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <UserPlus className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-medium mb-1">
                No contact lists found
              </h3>
              <p className="text-gray-500 mb-4">
                Get started by creating your first contact list
              </p>
              <button
                className="bg-primary px-4 py-2 rounded-md text-white flex items-center justify-center mx-auto"
                onClick={() => console.log("Create list")}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Contact List
              </button>
            </div>
          }
        />
      ) : (
        <ListGrid
          data={paginatedData}
          onItemClick={(item) => console.log("Grid item clicked", item)}
          actions={listActions}
          getListType={getListType}
          getListBadgeVariant={getListBadgeVariant}
          formatDate={formatDate}
        />
      )}

      <div className="mt-8 border-t border-gray-200 pt-4">
        <CustomPagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </Page>
  );
};

export default Lists;
