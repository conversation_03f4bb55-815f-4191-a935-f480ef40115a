// src/pages/Lists/AddContactList.tsx

import { useRef } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  SheetClose,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  UploadIcon,
  Trash2,
  PlusIcon,
  ListFilter,
  Users,
  Mail,
  Phone,
  FileText,
  Info,
} from "lucide-react";
import Papa from "papaparse";
import * as XLSX from "xlsx";
import { jwtDecode } from "jwt-decode";
import { createContactService } from "@/api/services/contact/contactService";
import secureLocalStorage from "react-secure-storage";
import { toast } from "sonner";
import { Textarea } from "@/components/ui/textarea";

// Define the shape of a single contact
interface Contact {
  email: string;
  contactNumber: string; // Changed from number to string
}

// Define the shape of form data
interface FormData {
  listName: string;
  listDescription: string;
  contacts: Contact[];
}

// Define the decoded token structure (adjust based on your actual token)
interface DecodedToken {
  user: {
    user_id?: number;
    id?: number;
  };
}

const validationSchema = Yup.object().shape({
  listName: Yup.string()
    .required("List Name is required")
    .min(2, "List Name must be at least 2 characters"),
  listDescription: Yup.string().required("List Description is required"),
  contacts: Yup.array()
    .of(
      Yup.object().shape({
        email: Yup.string()
          .required("Email is required")
          .email("Email is invalid"),
        contactNumber: Yup.string()
          .required("Contact Number is required")
          .matches(
            /^\+?[1-9]\d{1,14}$/,
            "Contact Number must include country code and be valid"
          ),
      })
    )
    .required("Contacts are required") // Ensure contacts is required
    .min(1, "At least one contact is required"),
});

export function AddContactList() {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize React Hook Form
  const {
    register,
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      listName: "",
      listDescription: "",
      contacts: [{ email: "", contactNumber: "" }], // Initialize contactNumber as string
    },
  });

  // Initialize useFieldArray for dynamic contacts
  const { fields, append, remove } = useFieldArray({
    control,
    name: "contacts",
  });

  // Retrieve and decode the token
  const getUserId = (): number | null => {
    const token = secureLocalStorage.getItem("token"); // Adjust the key as per your implementation
    if (!token) {
      return null;
    }

    try {
      const decoded: DecodedToken = jwtDecode(token as string);

      // Check for user_id or id
      if (decoded.user.user_id) {
        return decoded.user.user_id;
      } else if (decoded.user.id) {
        return decoded.user.id;
      } else {
        return null;
      }
    } catch (error) {
      console.error("Error decoding token:", error);
      return null;
    }
  };

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    const userId = getUserId();
    if (!userId) {
      toast.error("User ID not found. Please log in again.");
      return;
    }

    // Prepare the contacts in the expected format
    const formattedContacts = data.contacts.map((contact) => ({
      email: contact.email,
      contact_number: contact.contactNumber, // Convert to API-compatible format
    }));

    // Prepare the payload
    const payload = {
      user_id: userId,
      list_name: data.listName,
      list_description: data.listDescription,
      contacts: formattedContacts,
    };

    try {
      console.log("Submitting Payload:", payload); // Debug the payload
      await createContactService(payload);
      reset({
        listName: "",
        listDescription: "",
        contacts: [{ email: "", contactNumber: "" }],
      });
      toast.success("Contact List added successfully!");
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to add contact list. Please try again.");
    }
  };

  // Handle file import click
  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  // Handle file selection and parsing
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();

      reader.onload = (evt) => {
        const data = evt.target?.result;
        if (data) {
          const fileExtension = file.name.split(".").pop()?.toLowerCase();

          if (fileExtension === "csv") {
            // Existing CSV parsing code
          } else if (fileExtension === "xlsx") {
            const workbook = XLSX.read(data, { type: "binary" });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json<Contact>(worksheet, {
              defval: "",
            });

            // Log the raw JSON data
            console.log("Excel JSON Data:", jsonData);

            const validContacts = jsonData
              .map((contact: any) => ({
                email:
                  contact.email ||
                  contact["Email"] ||
                  contact["Email Address"] ||
                  "",
                contactNumber:
                  contact.contactNumber ||
                  contact["Contact Number"] ||
                  contact["Phone Number"] ||
                  "",
              }))
              .filter((contact) => contact.email && contact.contactNumber);

            validContacts.forEach((contact) => {
              append({
                email: contact.email,
                contactNumber: contact.contactNumber,
              });
            });

            toast.success("Contacts imported successfully!");
            console.log("Parsed Contacts:", validContacts);
          } else {
            toast.error(
              "Unsupported file format. Please upload a CSV or Excel file."
            );
          }
        }
      };

      if (
        file.type ===
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
        file.type === "application/vnd.ms-excel" ||
        file.type === "text/csv"
      ) {
        reader.readAsBinaryString(file);
      } else {
        toast.error("Please upload a valid CSV or Excel file.");
      }

      // Reset the input value to allow re-uploading the same file if needed
      e.target.value = "";
    }
  };

  return (
    <>
      <SheetTitle className="text-xl font-semibold jakarta bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-1">
        Create Contact List
      </SheetTitle>
      <SheetDescription className="text-muted-foreground mb-6">
        Create a new contact list for your voice AI agents to use for campaigns.
      </SheetDescription>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <ListFilter className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">List Information</h3>
          </div>

          {/* List Name Field */}
          <div>
            <Label
              htmlFor="listName"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              List Name<span className="text-red-500">*</span>
            </Label>
            <Input
              id="listName"
              placeholder="Enter contact list name"
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
              {...register("listName")}
            />
            {errors.listName && (
              <p className="mt-1 text-xs text-red-600">
                {errors.listName.message}
              </p>
            )}
          </div>

          {/* List Description Field */}
          <div>
            <Label
              htmlFor="listDescription"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              List Description<span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="listDescription"
              placeholder="Enter contact list description"
              className="w-full min-h-[80px] border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
              {...register("listDescription")}
            />
            {errors.listDescription && (
              <p className="mt-1 text-xs text-red-600">
                {errors.listDescription.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Contacts</h3>
            <span className="text-xs text-red-500">*</span>
          </div>

          {/* Contacts Section */}
          <div className="space-y-4">
            {fields.map((field, index) => (
              <div
                key={field.id}
                className="border border-border/50 rounded-lg p-4 mb-4 relative bg-card/50"
              >
                {/* Remove Contact Button */}
                {fields.length > 1 && (
                  <button
                    type="button"
                    onClick={() => remove(index)}
                    className="absolute top-3 right-3 text-muted-foreground hover:text-red-500 transition-colors"
                    title="Remove Contact"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                )}

                <div className="flex items-center space-x-2 mb-3">
                  <div className="h-6 w-6 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-primary">
                      {index + 1}
                    </span>
                  </div>
                  <h4 className="text-sm font-medium">Contact {index + 1}</h4>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Email Field */}
                  <div>
                    <Label
                      htmlFor={`contacts.${index}.email`}
                      className="text-sm font-medium text-foreground mb-1.5 block"
                    >
                      Email<span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id={`contacts.${index}.email`}
                        type="email"
                        placeholder="Enter contact email"
                        className="w-full pl-10 border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
                        {...register(`contacts.${index}.email` as const)}
                      />
                    </div>
                    {errors.contacts?.[index]?.email && (
                      <p className="mt-1 text-xs text-red-600">
                        {errors.contacts[index].email?.message}
                      </p>
                    )}
                  </div>

                  {/* Contact Number Field */}
                  <div>
                    <Label
                      htmlFor={`contacts.${index}.contactNumber`}
                      className="text-sm font-medium text-foreground mb-1.5 block"
                    >
                      Contact Number<span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        id={`contacts.${index}.contactNumber`}
                        type="tel"
                        placeholder="Enter contact number with country code"
                        className="w-full pl-10 border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
                        {...register(
                          `contacts.${index}.contactNumber` as const
                        )}
                      />
                    </div>
                    {errors.contacts?.[index]?.contactNumber && (
                      <p className="mt-1 text-xs text-red-600">
                        {errors.contacts[index].contactNumber?.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}

            {/* Add Contact Button */}
            <Button
              type="button"
              variant="outline"
              className="w-full border-dashed border-border/50 hover:bg-muted/20 flex items-center justify-center gap-2 py-6"
              onClick={() => append({ email: "", contactNumber: "" })}
            >
              <PlusIcon className="w-4 h-4" />
              Add Another Contact
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <UploadIcon className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Bulk Import</h3>
          </div>

          {/* Import Section */}
          <div className="border-2 border-dashed border-border/50 rounded-lg p-6 text-center">
            <div className="flex flex-col items-center justify-center">
              <UploadIcon className="h-8 w-8 text-muted-foreground mb-2" />
              <h3 className="text-sm font-medium mb-1">Import Contacts</h3>
              <p className="text-xs text-muted-foreground mb-3">
                Drag and drop a CSV or Excel file here or click to browse
              </p>
              <Button
                type="button"
                variant="outline"
                className="text-xs border-border/50 hover:bg-muted/20"
                onClick={handleImportClick}
              >
                Browse Files
              </Button>
              <input
                type="file"
                id="import"
                accept=".xlsx, .csv"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
              />
            </div>
          </div>

          <div className="flex items-center p-3 bg-muted/20 rounded-lg border border-border/50">
            <Info className="h-4 w-4 text-muted-foreground mr-2 flex-shrink-0" />
            <p className="text-xs text-muted-foreground">
              Your CSV or Excel file should have columns for 'Email' and
              'Contact Number' (with country code)
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex justify-end space-x-3">
          <SheetClose asChild>
            <Button
              type="button"
              variant="outline"
              className="border-border/50 text-foreground hover:bg-muted/20"
            >
              Cancel
            </Button>
          </SheetClose>

          <Button
            type="submit"
            className="btn-gradient text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Creating..." : "Create List"}
          </Button>
        </div>
      </form>
    </>
  );
}
