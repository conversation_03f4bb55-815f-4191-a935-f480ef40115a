import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import AuthCard from "@/components/auth/AuthCard";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Link } from "react-router-dom";

// Define the schema with 'email' and 'password'
const FormSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z
    .string()
    .min(4, { message: "Password must be at least 6 characters" })
    .max(100),
});

// Define the type inferred from the schema
type FormData = z.infer<typeof FormSchema>;

const Register = () => {
  const form = useForm<FormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Handle form submission
  const onSubmit = (values: FormData) => {
    console.log("Form Submitted:", values);

    // You can perform further actions here, such as sending data to an API
  };

  return (
    <AuthCard>
      <Form {...form}>
        <h1 className="text-3xl text-foreground font-bold tracking-wide mb-2">
          Create Account
        </h1>
        <p className="text-muted-foreground mb-6">Sign up to get started with our platform</p>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Field */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter your email"
                    {...field}
                    className="bg-white border-[#E4E8F0]"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Field */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">Password</FormLabel>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="Create a password"
                    {...field}
                    className="bg-white border-[#E4E8F0]"
                  />
                </FormControl>
                <p className="text-xs text-muted-foreground mt-1">
                  Password must be at least 6 characters
                </p>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full mt-4"
            style={{
              background: 'linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.9))'
            }}
          >
            Create Account
          </Button>

          {/* Extra Links */}
          <div className="w-full flex items-center justify-center pt-4">
            <span className="text-sm text-muted-foreground">
              Already have an account?{" "}
              <Link to="/login" className="text-primary font-medium hover:underline">
                Sign in
              </Link>
            </span>
          </div>
        </form>
      </Form>
    </AuthCard>
  );
};

export default Register;
