// src/pages/auth/Login.tsx

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import AuthCard from "@/components/auth/AuthCard";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Link, useNavigate } from "react-router-dom";

import { loginService, demoLoginService } from "@/api/services/auth/authService";
import { useState } from "react";

import { useAuth } from "@/context/AuthContext";

// Define the schema with 'email' and 'password'
const FormSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z
    .string()
    .min(4, { message: "Password must be at least 6 characters" }) // Adjusted min from 4 to 6
    .max(100),
});

// Define the type inferred from the schema
type FormData = z.infer<typeof FormSchema>;

const Login = () => {
  const [loading, setLoading] = useState<boolean>(false);

  const navigate = useNavigate();
  const { login } = useAuth();

  const form = useForm<FormData>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Handle form submission
  const onSubmit = async (values: FormData) => {
    setLoading(true);
    try {
      const token = await loginService(values);
      console.log("Token:", token);
      if (token) {
        login(token); // Store token in AuthContext
        navigate("/"); // Navigate to protected route
      }
    } catch (error) {
      console.error("Login failed:", error);
      // Error toast is already handled in loginService
    } finally {
      setLoading(false);
    }
  };

  // Handle demo login
  const handleDemoLogin = async () => {
    setLoading(true);
    try {
      const token = await demoLoginService();
      console.log("Demo Token:", token);
      if (token) {
        login(token); // Store token in AuthContext
        navigate("/"); // Navigate to protected route
      }
    } catch (error) {
      console.error("Demo login failed:", error);
      // Error toast is already handled in demoLoginService
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthCard>
      <Form {...form}>
        <h1 className="text-3xl text-foreground font-bold tracking-wide mb-2">
          Welcome Back
        </h1>
        <p className="text-muted-foreground mb-6">Sign in to your account to continue</p>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Email Field */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium">Email</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter your email"
                    {...field}
                    className="bg-white border-[#E4E8F0]"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Field */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between">
                  <FormLabel className="text-sm font-medium">Password</FormLabel>
                  <Link to="/forgot-password" className="text-xs text-primary hover:underline">
                    Forgot password?
                  </Link>
                </div>
                <FormControl>
                  <Input
                    type="password"
                    placeholder="Enter your password"
                    {...field}
                    className="bg-white border-[#E4E8F0]"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Submit Button */}
          <Button
            type="submit"
            className="w-full mt-4"
            disabled={loading}
            style={{
              background: 'linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.9))'
            }}
          >
            {loading ? "Signing in..." : "Sign In"}
          </Button>

          {/* Demo Login Button */}
          <Button
            type="button"
            variant="outline"
            className="w-full mt-2"
            disabled={loading}
            onClick={handleDemoLogin}
          >
            {loading ? "Signing in..." : "Demo Login"}
          </Button>

          {/* Extra Links */}
          <div className="w-full flex items-center justify-center pt-4">
            <span className="text-sm text-muted-foreground">
              Don't have an account?{" "}
              <Link to="/register" className="text-primary font-medium hover:underline">
                Create account
              </Link>
            </span>
          </div>
        </form>
      </Form>
    </AuthCard>
  );
};

export default Login;
