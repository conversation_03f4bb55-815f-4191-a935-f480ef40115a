import { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useDebounce } from "@/hooks/useDebounce";
import Page from "@/components/Page";
import Header from "@/components/Header";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import {
  MoreHorizontal,
  Plus,
  Search,
  Play,
  Edit,
  Trash2,
  Download,
  Upload,
  Copy,
  GitBranch as WorkflowIcon,
} from "lucide-react";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import {
  getAllWorkflows,
  deleteWorkflow,
  testWorkflow,
  exportWorkflow,
  importWorkflow,
} from "@/api/services/workflows/workflowService";
import { Workflow } from "@/types/api";
import CreateWorkflow from "./CreateWorkflow";

const Workflows = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [search, setSearch] = useState("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null);
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");
  const debouncedSearch = useDebounce(search, 300);

  // Fetch workflows
  const {
    data: workflowsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["workflows", debouncedSearch],
    queryFn: () => getAllWorkflows(1, debouncedSearch),
  });

  // Delete workflow mutation
  const deleteMutation = useMutation({
    mutationFn: deleteWorkflow,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["workflows"] });
      setDeleteDialogOpen(false);
      setSelectedWorkflow(null);
    },
  });

  // Test workflow mutation
  const testMutation = useMutation({
    mutationFn: ({ id }: { id: string }) => testWorkflow(id),
    onSuccess: () => {
      toast.success("Workflow test initiated successfully");
    },
  });

  const handleSearchChange = (value: string) => {
    setSearch(value);
  };

  const handleDeleteClick = (workflow: Workflow) => {
    setSelectedWorkflow(workflow);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (selectedWorkflow?.id) {
      deleteMutation.mutate(selectedWorkflow.id);
    }
  };

  const handleTestWorkflow = (workflow: Workflow) => {
    if (workflow.id) {
      testMutation.mutate({ id: workflow.id });
    }
  };

  const handleEditWorkflow = (workflow: Workflow) => {
    navigate(`/dashboard/workflows/${workflow.id}/edit`);
  };

  const handleViewWorkflow = (workflow: Workflow) => {
    navigate(`/dashboard/workflows/${workflow.id}`);
  };

  const handleExportWorkflow = async (workflow: Workflow) => {
    if (workflow.id) {
      try {
        await exportWorkflow(workflow.id);
      } catch (error) {
        console.error("Export failed:", error);
      }
    }
  };

  const handleImportWorkflow = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      try {
        await importWorkflow(file);
        queryClient.invalidateQueries({ queryKey: ["workflows"] });
      } catch (error) {
        console.error("Import failed:", error);
      }
    }
    // Reset input
    event.target.value = "";
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      active: "bg-green-100 text-green-800",
      inactive: "bg-gray-100 text-gray-800",
      draft: "bg-yellow-100 text-yellow-800",
      archived: "bg-red-100 text-red-800",
    };

    return (
      <Badge className={statusColors[status as keyof typeof statusColors] || statusColors.inactive}>
        {status}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (error) {
    return (
      <Page>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-2">Failed to load workflows</p>
            <Button onClick={() => queryClient.invalidateQueries({ queryKey: ["workflows"] })}>
              Try Again
            </Button>
          </div>
        </div>
      </Page>
    );
  }

  return (
    <Page>
      <Header
        title="Workflows"
        buttonText="Create Workflow"
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={true}
        sheetContent={<CreateWorkflow />}
        sheetSize="xl"
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        showViewToggle={true}
      />

      <div className="mb-4 flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search workflows..."
            value={search}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex items-center gap-2">
          <input
            type="file"
            accept=".json"
            onChange={handleImportWorkflow}
            className="hidden"
            id="import-workflow"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => document.getElementById("import-workflow")?.click()}
          >
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
        </div>
      </div>

      <Card className="border-0 shadow-none bg-transparent">
        <CardHeader className="px-0 pb-4">
          <CardTitle className="flex items-center gap-2 text-xl font-semibold">
            <WorkflowIcon className="h-5 w-5" />
            Workflows
          </CardTitle>
          <CardDescription className="text-gray-500">
            Manage your voice AI workflows and conversation flows
          </CardDescription>
        </CardHeader>
        <CardContent className="px-0">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-[250px]" />
                    <Skeleton className="h-4 w-[200px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-lg border bg-white">
              <Table>
                <TableHeader>
                  <TableRow className="border-b bg-gray-50/50">
                    <TableHead className="font-medium text-gray-900">Name</TableHead>
                    <TableHead className="font-medium text-gray-900">Description</TableHead>
                    <TableHead className="font-medium text-gray-900">Nodes</TableHead>
                    <TableHead className="font-medium text-gray-900">Status</TableHead>
                    <TableHead className="font-medium text-gray-900">Last Modified</TableHead>
                    <TableHead className="text-right font-medium text-gray-900">Actions</TableHead>
                  </TableRow>
                </TableHeader>
              <TableBody>
                {workflowsData?.data?.workflows?.map((workflow: Workflow) => (
                  <TableRow
                    key={workflow.id}
                    className="cursor-pointer hover:bg-gray-50/50"
                    onClick={() => handleViewWorkflow(workflow)}
                  >
                    <TableCell className="font-medium py-4">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <WorkflowIcon className="h-4 w-4 text-blue-600" />
                        </div>
                        <span className="text-gray-900">{workflow.name}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-gray-600 py-4">
                      {workflow.description || "No description"}
                    </TableCell>
                    <TableCell className="py-4">
                      <Badge variant="secondary" className="bg-gray-100 text-gray-700 hover:bg-gray-100">
                        {workflow.nodes?.length || 0} nodes
                      </Badge>
                    </TableCell>
                    <TableCell className="py-4">
                      {getStatusBadge(workflow.localData?.status || "active")}
                    </TableCell>
                    <TableCell className="text-gray-600 py-4">
                      {workflow.updatedAt ? formatDate(workflow.updatedAt) : "N/A"}
                    </TableCell>
                    <TableCell className="text-right py-4">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                          <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleTestWorkflow(workflow);
                          }}>
                            <Play className="mr-2 h-4 w-4" />
                            Test Workflow
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleEditWorkflow(workflow);
                          }}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={(e) => {
                            e.stopPropagation();
                            handleExportWorkflow(workflow);
                          }}>
                            <Download className="mr-2 h-4 w-4" />
                            Export
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteClick(workflow);
                            }}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
                {workflowsData?.data?.workflows?.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <WorkflowIcon className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-500">No workflows found</p>
                        <p className="text-sm text-gray-400">
                          Create your first workflow to get started
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Workflow</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{selectedWorkflow?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Page>
  );
};

export default Workflows;
