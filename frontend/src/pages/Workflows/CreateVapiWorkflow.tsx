import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { ArrowLeft, Zap, MessageCircle, Phone, PhoneOff, Wrench } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { vapiWorkflowService, CreateVapiWorkflowRequest, VapiWorkflowNode } from "@/services/vapiWorkflowService";

// VAPI Workflow Templates
const VAPI_WORKFLOW_TEMPLATES = [
  {
    id: "blank",
    name: "Blank Workflow",
    description: "Start with an empty workflow",
    icon: MessageCircle,
    color: "bg-gray-500",
    nodes: [
      {
        id: "start-node",
        type: "conversation" as const,
        name: "Start Conversation",
        position: { x: 250, y: 100 },
        firstMessage: "Hey there!",
        prompt: "You are a helpful assistant. Be friendly and conversational.",
        isStart: true,
      }
    ],
    edges: [],
  },
  {
    id: "information-gathering",
    name: "Information Gathering",
    description: "Collect user information with variable extraction",
    icon: MessageCircle,
    color: "bg-blue-500",
    nodes: [
      {
        id: "greeting-node",
        type: "conversation" as const,
        name: "Greeting",
        position: { x: 250, y: 100 },
        firstMessage: "Hey there!",
        prompt: "Ask users what kind of voice agent they want to build. Be friendly and conversational.",
        isStart: true,
      },
      {
        id: "collect-info-node",
        type: "conversation" as const,
        name: "Collect Information",
        position: { x: 250, y: 300 },
        prompt: "Ask for the user's first name and what city they're in.",
        extractVariables: [
          { name: "first_name", type: "string", description: "the user's first name" },
          { name: "city", type: "string", description: "the user's city" }
        ],
      },
      {
        id: "response-node",
        type: "conversation" as const,
        name: "Dynamic Response",
        position: { x: 250, y: 500 },
        prompt: "Say 'Thanks {{first_name}}, {{city}} is great!' Then ask if there's anything you can help with.",
      },
      {
        id: "end-node",
        type: "endCall" as const,
        name: "End Call",
        position: { x: 250, y: 700 },
        firstMessage: "Alright, have a nice day!",
      }
    ],
    edges: [
      { id: "edge-1", source: "greeting-node", target: "collect-info-node", condition: "User describes their voice agent" },
      { id: "edge-2", source: "collect-info-node", target: "response-node", condition: "" },
      { id: "edge-3", source: "response-node", target: "end-node", condition: "User does not need any help" },
    ],
  },
  {
    id: "appointment-scheduling",
    name: "Appointment Scheduling",
    description: "Schedule, reschedule, and cancel appointments",
    icon: Phone,
    color: "bg-green-500",
    nodes: [
      {
        id: "greeting-node",
        type: "conversation" as const,
        name: "Greeting",
        position: { x: 250, y: 100 },
        firstMessage: "Hello! I'm here to help you with appointments.",
        prompt: "Ask how you can help with their appointment needs.",
        isStart: true,
      },
      {
        id: "api-check-node",
        type: "apiRequest" as const,
        name: "Check Availability",
        position: { x: 250, y: 300 },
        url: "https://api.example.com/appointments/availability",
        method: "GET",
      },
      {
        id: "schedule-node",
        type: "conversation" as const,
        name: "Schedule Appointment",
        position: { x: 100, y: 500 },
        prompt: "Help schedule the appointment based on availability.",
      },
      {
        id: "transfer-node",
        type: "transferCall" as const,
        name: "Transfer to Human",
        position: { x: 400, y: 500 },
        destination: "+1-555-SUPPORT",
        transferPlan: {
          message: "Transferring you to our scheduling team",
          summary: "Customer needs complex scheduling assistance"
        },
      }
    ],
    edges: [
      { id: "edge-1", source: "greeting-node", target: "api-check-node", condition: "User wants to schedule appointment" },
      { id: "edge-2", source: "api-check-node", target: "schedule-node", condition: "Availability found" },
      { id: "edge-3", source: "api-check-node", target: "transfer-node", condition: "No availability or complex request" },
    ],
  },
  {
    id: "customer-support",
    name: "Customer Support",
    description: "Handle customer inquiries with escalation",
    icon: Wrench,
    color: "bg-purple-500",
    nodes: [
      {
        id: "greeting-node",
        type: "conversation" as const,
        name: "Support Greeting",
        position: { x: 250, y: 100 },
        firstMessage: "Hi! I'm here to help with your support request.",
        prompt: "Ask about their issue and try to help resolve it.",
        isStart: true,
      },
      {
        id: "escalation-node",
        type: "conversation" as const,
        name: "Human Escalation",
        position: { x: 400, y: 300 },
        prompt: "Confirm that the user wants to speak to a human and ask what they'd like to discuss.",
        isGlobal: true,
        globalCondition: "User wants to speak to a human",
      },
      {
        id: "transfer-node",
        type: "transferCall" as const,
        name: "Transfer to Agent",
        position: { x: 400, y: 500 },
        destination: "+1-555-SUPPORT",
      }
    ],
    edges: [
      { id: "edge-1", source: "escalation-node", target: "transfer-node", condition: "User confirmed human transfer" },
    ],
  }
];

const CreateVapiWorkflow = () => {
  const navigate = useNavigate();
  const [selectedTemplate, setSelectedTemplate] = useState<string>("blank");
  const [workflowName, setWorkflowName] = useState("");
  const [workflowDescription, setWorkflowDescription] = useState("");

  const createWorkflowMutation = useMutation({
    mutationFn: (data: CreateVapiWorkflowRequest) => vapiWorkflowService.createWorkflow(data),
    onSuccess: (workflow) => {
      toast.success("VAPI Workflow created successfully!");
      navigate(`/workflows/vapi/${workflow.id}`);
    },
    onError: (error) => {
      console.error("Failed to create workflow:", error);
      toast.error("Failed to create workflow");
    },
  });

  const handleCreateWorkflow = () => {
    if (!workflowName.trim()) {
      toast.error("Please enter a workflow name");
      return;
    }

    const template = VAPI_WORKFLOW_TEMPLATES.find(t => t.id === selectedTemplate);
    if (!template) {
      toast.error("Please select a template");
      return;
    }

    const workflowData: CreateVapiWorkflowRequest = {
      name: workflowName,
      description: workflowDescription,
      nodes: template.nodes as VapiWorkflowNode[],
      edges: template.edges,
    };

    createWorkflowMutation.mutate(workflowData);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate("/workflows")}
            className="text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Workflows
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create VAPI Workflow</h1>
            <p className="text-gray-600 mt-1">Build voice AI workflows with visual flow builder</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Workflow Details */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Workflow Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Workflow Name *
                  </label>
                  <Input
                    value={workflowName}
                    onChange={(e) => setWorkflowName(e.target.value)}
                    placeholder="e.g., Customer Support Bot"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <Textarea
                    value={workflowDescription}
                    onChange={(e) => setWorkflowDescription(e.target.value)}
                    placeholder="Describe what this workflow does..."
                    rows={3}
                  />
                </div>
                <Button
                  onClick={handleCreateWorkflow}
                  disabled={createWorkflowMutation.isPending || !workflowName.trim()}
                  className="w-full"
                >
                  {createWorkflowMutation.isPending ? "Creating..." : "Create Workflow"}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Template Selection */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Choose a Template</CardTitle>
                <p className="text-sm text-gray-600">
                  Start with a pre-built template or create from scratch
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {VAPI_WORKFLOW_TEMPLATES.map((template) => {
                    const IconComponent = template.icon;
                    return (
                      <div
                        key={template.id}
                        className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                          selectedTemplate === template.id
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                        onClick={() => setSelectedTemplate(template.id)}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-10 h-10 ${template.color} rounded-lg flex items-center justify-center`}>
                            <IconComponent className="h-5 w-5 text-white" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-gray-900">{template.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="secondary" className="text-xs">
                                {template.nodes.length} nodes
                              </Badge>
                              <Badge variant="secondary" className="text-xs">
                                {template.edges.length} connections
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateVapiWorkflow;
