import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Wrench,
  Settings,
  Trash2,
  Tool,
} from "lucide-react";

interface ToolNodeData {
  name: string;
  toolId?: string;
  toolName?: string;
  toolType?: string;
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ToolNode = ({ data, selected }: NodeProps<ToolNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  // Mock tools data - in real implementation, this would come from an API
  const availableTools = [
    { id: "tool-1", name: "Calendar Booking", type: "API Request" },
    { id: "tool-2", name: "CRM Lookup", type: "Database" },
    { id: "tool-3", name: "Email Sender", type: "Communication" },
    { id: "tool-4", name: "Weather API", type: "External API" },
    { id: "tool-5", name: "Calculator", type: "Utility" },
  ];

  const handleUpdate = useCallback((updates: Partial<ToolNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleToolSelect = (toolId: string) => {
    const selectedTool = availableTools.find(tool => tool.id === toolId);
    if (selectedTool) {
      handleUpdate({
        toolId,
        toolName: selectedTool.name,
        toolType: selectedTool.type,
      });
    }
  };

  const getToolTypeColor = (type: string) => {
    switch (type) {
      case "API Request":
        return "bg-blue-100 text-blue-800";
      case "Database":
        return "bg-green-100 text-green-800";
      case "Communication":
        return "bg-purple-100 text-purple-800";
      case "External API":
        return "bg-orange-100 text-orange-800";
      case "Utility":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <>
      {/* VAPI-style Top Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white !rounded-full"
        style={{ top: -6 }}
      />

      {/* Exact VAPI Tool Node Design */}
      <div
        className={`w-72 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 relative ${
          selected ? "ring-2 ring-purple-500" : ""
        }`}
      >
        {/* VAPI-style colored left border */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-purple-500 rounded-l-lg"></div>

        {/* Node Header */}
        <div className="flex items-center justify-between p-3 pb-2">
          <div className="flex items-center gap-2">
            <Wrench className="h-4 w-4 text-purple-500" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {localData.name || "Tool"}
              </h3>
            </div>
          </div>
          <div className="flex items-center gap-1">
            {localData.toolType && (
              <Badge className={`text-xs px-2 py-0.5 ${getToolTypeColor(localData.toolType)}`}>
                {localData.toolType}
              </Badge>
            )}
            <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900">
                  <Settings className="h-3 w-3" />
                </Button>
              </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure Tool Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="tool-select">Select Tool</Label>
                      <Select
                        value={localData.toolId || ""}
                        onValueChange={handleToolSelect}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a tool" />
                        </SelectTrigger>
                        <SelectContent>
                          {availableTools.map((tool) => (
                            <SelectItem key={tool.id} value={tool.id}>
                              <div className="flex items-center gap-2">
                                <Tool className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{tool.name}</div>
                                  <div className="text-xs text-gray-500">{tool.type}</div>
                                </div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {localData.toolId && (
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-2">Selected Tool</h4>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <strong>Name:</strong> {localData.toolName}
                          </div>
                          <div className="flex items-center gap-2">
                            <strong>Type:</strong> 
                            <Badge className={getToolTypeColor(localData.toolType || "")}>
                              {localData.toolType}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            <strong>ID:</strong> 
                            <code className="text-xs bg-gray-200 px-2 py-1 rounded">
                              {localData.toolId}
                            </code>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={data.onDelete}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* VAPI-style Node Content */}
        <div className="px-3 pb-3">
          <div className="space-y-2">
            {/* Selected Tool */}
            {localData.toolName ? (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Selected tool:</span>
                <div className="text-gray-800 mt-0.5">
                  {localData.toolName}
                </div>
              </div>
            ) : (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Tool:</span>
                <div className="text-gray-500 mt-0.5 italic">
                  No tool selected
                </div>
              </div>
            )}

            {/* Tool ID */}
            {localData.toolId && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Tool ID:</span>
                <div className="text-blue-600 mt-0.5">
                  {localData.toolId}
                </div>
              </div>
            )}

            {/* Configuration Status */}
            <div className="flex items-center justify-between pt-1 mt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                {localData.toolName ? "Configured" : "Not configured"}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900"
                onClick={() => setIsConfigOpen(true)}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* VAPI-style Bottom Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white !rounded-full"
        style={{ bottom: -6 }}
      />
    </>
  );
};

export default ToolNode;
