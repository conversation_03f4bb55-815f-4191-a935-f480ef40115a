import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  PhoneForwarded,
  Settings,
  Trash2,
  Phone,
} from "lucide-react";

interface TransferCallNodeData {
  name: string;
  destination: string;
  transferPlan?: {
    message?: string;
    summary?: string;
  };
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const TransferCallNode = ({ data, selected }: NodeProps<TransferCallNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = useCallback((updates: Partial<TransferCallNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleDestinationChange = (value: string) => {
    handleUpdate({ destination: value });
  };

  const handleMessageChange = (value: string) => {
    handleUpdate({
      transferPlan: {
        ...localData.transferPlan,
        message: value,
      },
    });
  };

  const handleSummaryChange = (value: string) => {
    handleUpdate({
      transferPlan: {
        ...localData.transferPlan,
        summary: value,
      },
    });
  };

  const formatPhoneNumber = (phone: string) => {
    if (!phone) return "No destination";
    if (phone.startsWith("+1")) {
      const digits = phone.slice(2);
      if (digits.length === 10) {
        return `+1 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
      }
    }
    return phone;
  };

  return (
    <>
      {/* VAPI-style Top Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white !rounded-full"
        style={{ top: -6 }}
      />

      {/* Exact VAPI Transfer Call Node Design */}
      <div
        className={`w-72 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 relative ${
          selected ? "ring-2 ring-yellow-500" : ""
        }`}
      >
        {/* VAPI-style colored left border */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-yellow-500 rounded-l-lg"></div>

        {/* Node Header */}
        <div className="flex items-center justify-between p-3 pb-2">
          <div className="flex items-center gap-2">
            <Phone className="h-4 w-4 text-yellow-500" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {localData.name || "Transfer Call"}
              </h3>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900">
                  <Settings className="h-3 w-3" />
                </Button>
              </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure Transfer Call Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="destination">Destination Phone Number</Label>
                      <Input
                        id="destination"
                        value={localData.destination || ""}
                        onChange={(e) => handleDestinationChange(e.target.value)}
                        placeholder="******-123-4567"
                      />
                    </div>

                    <div>
                      <Label htmlFor="transfer-message">Transfer Message (Optional)</Label>
                      <Textarea
                        id="transfer-message"
                        value={localData.transferPlan?.message || ""}
                        onChange={(e) => handleMessageChange(e.target.value)}
                        placeholder="Message to play before transferring the call"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="transfer-summary">Call Summary (Optional)</Label>
                      <Textarea
                        id="transfer-summary"
                        value={localData.transferPlan?.summary || ""}
                        onChange={(e) => handleSummaryChange(e.target.value)}
                        placeholder="Summary to provide to the person receiving the transfer"
                        rows={3}
                      />
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={data.onDelete}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* VAPI-style Node Content */}
        <div className="px-3 pb-3">
          <div className="space-y-2">
            {/* Destination */}
            <div className="text-xs text-gray-600">
              <span className="font-medium">Destination:</span>
              <div className="text-gray-800 mt-0.5">
                {formatPhoneNumber(localData.destination)}
              </div>
            </div>

            {/* Transfer Message */}
            {localData.transferPlan?.message && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Transfer message:</span>
                <div className="text-gray-800 mt-0.5 leading-relaxed">
                  "{localData.transferPlan.message.substring(0, 60)}{localData.transferPlan.message.length > 60 ? "..." : ""}"
                </div>
              </div>
            )}

            {/* Summary */}
            {localData.transferPlan?.summary && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Summary:</span>
                <div className="text-gray-800 mt-0.5 leading-relaxed">
                  {localData.transferPlan.summary.substring(0, 60)}{localData.transferPlan.summary.length > 60 ? "..." : ""}
                </div>
              </div>
            )}

            {/* Configuration Status */}
            <div className="flex items-center justify-between pt-1 mt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                {localData.destination ? "Configured" : "Not configured"}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900"
                onClick={() => setIsConfigOpen(true)}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* VAPI-style Bottom Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white !rounded-full"
        style={{ bottom: -6 }}
      />

      {/* Transfer calls typically don't have outgoing connections */}
    </>
  );
};

export default TransferCallNode;
