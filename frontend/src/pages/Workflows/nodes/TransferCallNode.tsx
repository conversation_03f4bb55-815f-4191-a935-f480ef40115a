import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  PhoneForwarded,
  Settings,
  Trash2,
  Phone,
} from "lucide-react";

interface TransferCallNodeData {
  name: string;
  destination: string;
  transferPlan?: {
    message?: string;
    summary?: string;
  };
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const TransferCallNode = ({ data, selected }: NodeProps<TransferCallNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = useCallback((updates: Partial<TransferCallNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleDestinationChange = (value: string) => {
    handleUpdate({ destination: value });
  };

  const handleMessageChange = (value: string) => {
    handleUpdate({
      transferPlan: {
        ...localData.transferPlan,
        message: value,
      },
    });
  };

  const handleSummaryChange = (value: string) => {
    handleUpdate({
      transferPlan: {
        ...localData.transferPlan,
        summary: value,
      },
    });
  };

  const formatPhoneNumber = (phone: string) => {
    if (!phone) return "No destination";
    if (phone.startsWith("+1")) {
      const digits = phone.slice(2);
      if (digits.length === 10) {
        return `+1 (${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6)}`;
      }
    }
    return phone;
  };

  return (
    <>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-orange-500"
      />
      
      <Card
        className={`min-w-[280px] max-w-[320px] transition-all duration-200 ${
          selected
            ? "ring-2 ring-orange-500 shadow-lg"
            : "shadow-md hover:shadow-lg"
        }`}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <PhoneForwarded className="h-4 w-4 text-orange-600" />
              <CardTitle className="text-sm font-medium">
                {localData.name || "Transfer Call"}
              </CardTitle>
            </div>
            <div className="flex items-center gap-1">
              <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Settings className="h-3 w-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure Transfer Call Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="destination">Destination Phone Number</Label>
                      <Input
                        id="destination"
                        value={localData.destination || ""}
                        onChange={(e) => handleDestinationChange(e.target.value)}
                        placeholder="******-123-4567"
                      />
                    </div>

                    <div>
                      <Label htmlFor="transfer-message">Transfer Message (Optional)</Label>
                      <Textarea
                        id="transfer-message"
                        value={localData.transferPlan?.message || ""}
                        onChange={(e) => handleMessageChange(e.target.value)}
                        placeholder="Message to play before transferring the call"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="transfer-summary">Call Summary (Optional)</Label>
                      <Textarea
                        id="transfer-summary"
                        value={localData.transferPlan?.summary || ""}
                        onChange={(e) => handleSummaryChange(e.target.value)}
                        placeholder="Summary to provide to the person receiving the transfer"
                        rows={3}
                      />
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                onClick={data.onDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex items-center gap-2 text-xs">
              <Phone className="h-3 w-3 text-gray-500" />
              <span className="text-gray-700">
                {formatPhoneNumber(localData.destination)}
              </span>
            </div>
            
            {localData.transferPlan?.message && (
              <div className="text-xs text-gray-600 bg-orange-50 p-2 rounded">
                <strong>Message:</strong> {localData.transferPlan.message.substring(0, 50)}
                {localData.transferPlan.message.length > 50 && "..."}
              </div>
            )}

            {localData.transferPlan?.summary && (
              <div className="text-xs text-gray-600">
                <strong>Summary:</strong> {localData.transferPlan.summary.substring(0, 50)}
                {localData.transferPlan.summary.length > 50 && "..."}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Transfer calls typically don't have outgoing connections */}
    </>
  );
};

export default TransferCallNode;
