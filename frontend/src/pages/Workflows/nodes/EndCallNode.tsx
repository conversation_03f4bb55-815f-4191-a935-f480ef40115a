import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  PhoneOff,
  Settings,
  Trash2,
} from "lucide-react";

interface EndCallNodeData {
  name: string;
  firstMessage?: string;
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const EndCallNode = ({ data, selected }: NodeProps<EndCallNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = useCallback((updates: Partial<EndCallNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleFirstMessageChange = (value: string) => {
    handleUpdate({ firstMessage: value });
  };

  return (
    <>
      {/* VAPI-style Top Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white !rounded-full"
        style={{ top: -6 }}
      />

      {/* Exact VAPI End Call Node Design */}
      <div
        className={`w-72 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 relative ${
          selected ? "ring-2 ring-red-500" : ""
        }`}
      >
        {/* VAPI-style colored left border */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-red-500 rounded-l-lg"></div>

        {/* Node Header */}
        <div className="flex items-center justify-between p-3 pb-2">
          <div className="flex items-center gap-2">
            <PhoneOff className="h-4 w-4 text-red-500" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {localData.name || "End Call"}
              </h3>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900">
                  <Settings className="h-3 w-3" />
                </Button>
              </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure End Call Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="first-message">Closing Message (Optional)</Label>
                      <Textarea
                        id="first-message"
                        value={localData.firstMessage || ""}
                        onChange={(e) => handleFirstMessageChange(e.target.value)}
                        placeholder="Final message to say before ending the call"
                        rows={3}
                      />
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={data.onDelete}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* VAPI-style Node Content */}
        <div className="px-3 pb-3">
          <div className="space-y-2">
            {/* Action Description */}
            <div className="text-xs text-gray-600">
              <span className="font-medium">Action:</span>
              <div className="text-gray-800 mt-0.5">
                Terminates the call
              </div>
            </div>

            {/* Closing Message */}
            {localData.firstMessage && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Closing message:</span>
                <div className="text-gray-800 mt-0.5 leading-relaxed">
                  "{localData.firstMessage.substring(0, 80)}{localData.firstMessage.length > 80 ? "..." : ""}"
                </div>
              </div>
            )}

            {/* Configuration Status */}
            <div className="flex items-center justify-between pt-1 mt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                Terminal node
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900"
                onClick={() => setIsConfigOpen(true)}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* End call nodes don't have outgoing connections */}
    </>
  );
};

export default EndCallNode;
