import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  MessageCircle,
  Settings,
  Trash2,
  Star,
  Play,
} from "lucide-react";

interface ConversationNodeData {
  name: string;
  prompt: string;
  firstMessage?: string;
  isStart?: boolean;
  variableExtractionPlan?: {
    schema: any;
    aliases?: Array<{ key: string; value: string }>;
  };
  globalNodePlan?: {
    enabled: boolean;
    enterCondition: string;
  };
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ConversationNode = ({ data, selected }: NodeProps<ConversationNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = useCallback((updates: Partial<ConversationNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handlePromptChange = (value: string) => {
    handleUpdate({ prompt: value });
  };

  const handleFirstMessageChange = (value: string) => {
    handleUpdate({ firstMessage: value });
  };

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleStartToggle = (checked: boolean) => {
    handleUpdate({ isStart: checked });
  };

  const handleGlobalToggle = (checked: boolean) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enabled: checked,
      },
    });
  };

  const handleGlobalConditionChange = (value: string) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enterCondition: value,
      },
    });
  };

  return (
    <>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-blue-500"
      />
      
      <Card
        className={`min-w-[280px] max-w-[320px] transition-all duration-200 ${
          selected
            ? "ring-2 ring-blue-500 shadow-lg"
            : "shadow-md hover:shadow-lg"
        } ${localData.isStart ? "ring-2 ring-green-500" : ""}`}
      >
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageCircle className="h-4 w-4 text-blue-600" />
              <CardTitle className="text-sm font-medium">
                {localData.name || "Conversation"}
              </CardTitle>
            </div>
            <div className="flex items-center gap-1">
              {localData.isStart && (
                <Badge variant="secondary" className="text-xs">
                  <Star className="h-3 w-3 mr-1" />
                  Start
                </Badge>
              )}
              {localData.globalNodePlan?.enabled && (
                <Badge variant="outline" className="text-xs">
                  Global
                </Badge>
              )}
              <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <Settings className="h-3 w-3" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure Conversation Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is-start"
                        checked={localData.isStart || false}
                        onCheckedChange={handleStartToggle}
                      />
                      <Label htmlFor="is-start">Start Node</Label>
                    </div>

                    <div>
                      <Label htmlFor="first-message">First Message (Optional)</Label>
                      <Textarea
                        id="first-message"
                        value={localData.firstMessage || ""}
                        onChange={(e) => handleFirstMessageChange(e.target.value)}
                        placeholder="Message to speak when entering this node"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="prompt">Conversation Prompt</Label>
                      <Textarea
                        id="prompt"
                        value={localData.prompt || ""}
                        onChange={(e) => handlePromptChange(e.target.value)}
                        placeholder="Enter the conversation prompt that guides the AI's behavior"
                        rows={4}
                      />
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <Switch
                          id="is-global"
                          checked={localData.globalNodePlan?.enabled || false}
                          onCheckedChange={handleGlobalToggle}
                        />
                        <Label htmlFor="is-global">Global Node</Label>
                      </div>
                      
                      {localData.globalNodePlan?.enabled && (
                        <div>
                          <Label htmlFor="global-condition">Enter Condition</Label>
                          <Input
                            id="global-condition"
                            value={localData.globalNodePlan?.enterCondition || ""}
                            onChange={(e) => handleGlobalConditionChange(e.target.value)}
                            placeholder="e.g., User wants to speak to a human"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                onClick={data.onDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="space-y-2">
            {localData.firstMessage && (
              <div className="text-xs text-gray-600 bg-blue-50 p-2 rounded">
                <strong>First:</strong> {localData.firstMessage.substring(0, 50)}
                {localData.firstMessage.length > 50 && "..."}
              </div>
            )}
            
            <div className="text-xs text-gray-700">
              <strong>Prompt:</strong> {localData.prompt?.substring(0, 80) || "No prompt set"}
              {(localData.prompt?.length || 0) > 80 && "..."}
            </div>

            {localData.globalNodePlan?.enabled && (
              <div className="text-xs text-orange-600 bg-orange-50 p-2 rounded">
                <strong>Global:</strong> {localData.globalNodePlan.enterCondition || "No condition"}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-blue-500"
      />
    </>
  );
};

export default ConversationNode;
