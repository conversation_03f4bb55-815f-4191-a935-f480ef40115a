import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  MessageCircle,
  Settings,
  Trash2,
  Star,
  Play,
} from "lucide-react";

interface ConversationNodeData {
  name: string;
  prompt: string;
  firstMessage?: string;
  isStart?: boolean;
  variableExtractionPlan?: {
    schema: any;
    aliases?: Array<{ key: string; value: string }>;
  };
  globalNodePlan?: {
    enabled: boolean;
    enterCondition: string;
  };
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ConversationNode = ({ data, selected }: NodeProps<ConversationNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = useCallback((updates: Partial<ConversationNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handlePromptChange = (value: string) => {
    handleUpdate({ prompt: value });
  };

  const handleFirstMessageChange = (value: string) => {
    handleUpdate({ firstMessage: value });
  };

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleStartToggle = (checked: boolean) => {
    handleUpdate({ isStart: checked });
  };

  const handleGlobalToggle = (checked: boolean) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enabled: checked,
      },
    });
  };

  const handleGlobalConditionChange = (value: string) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enterCondition: value,
      },
    });
  };

  return (
    <>
      {/* VAPI-style Handles */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-4 h-4 !bg-blue-500 !border-2 !border-white shadow-md"
      />

      {/* VAPI-style Node */}
      <div
        className={`w-80 bg-white border rounded-xl shadow-sm hover:shadow-md transition-all duration-200 group ${
          selected
            ? "ring-2 ring-blue-500 ring-offset-2 shadow-lg"
            : ""
        } ${localData.isStart ? "ring-2 ring-green-500 ring-offset-2" : ""}`}
      >
        {/* Node Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform">
              <MessageCircle className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-900">
                {localData.name || "Conversation"}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                {localData.isStart && (
                  <Badge variant="secondary" className="text-xs px-2 py-0.5 bg-green-100 text-green-700">
                    <Star className="h-3 w-3 mr-1" />
                    Start Node
                  </Badge>
                )}
                {localData.globalNodePlan?.enabled && (
                  <Badge variant="outline" className="text-xs px-2 py-0.5">
                    Global
                  </Badge>
                )}
                <span className="text-xs text-gray-500">AI Response</span>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-100">
                  <Settings className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-blue-500" />
                    Configure Conversation Node
                  </DialogTitle>
                </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is-start"
                        checked={localData.isStart || false}
                        onCheckedChange={handleStartToggle}
                      />
                      <Label htmlFor="is-start">Start Node</Label>
                    </div>

                    <div>
                      <Label htmlFor="first-message">First Message (Optional)</Label>
                      <Textarea
                        id="first-message"
                        value={localData.firstMessage || ""}
                        onChange={(e) => handleFirstMessageChange(e.target.value)}
                        placeholder="Message to speak when entering this node"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="prompt">Conversation Prompt</Label>
                      <Textarea
                        id="prompt"
                        value={localData.prompt || ""}
                        onChange={(e) => handlePromptChange(e.target.value)}
                        placeholder="Enter the conversation prompt that guides the AI's behavior"
                        rows={4}
                      />
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <Switch
                          id="is-global"
                          checked={localData.globalNodePlan?.enabled || false}
                          onCheckedChange={handleGlobalToggle}
                        />
                        <Label htmlFor="is-global">Global Node</Label>
                      </div>
                      
                      {localData.globalNodePlan?.enabled && (
                        <div>
                          <Label htmlFor="global-condition">Enter Condition</Label>
                          <Input
                            id="global-condition"
                            value={localData.globalNodePlan?.enterCondition || ""}
                            onChange={(e) => handleGlobalConditionChange(e.target.value)}
                            placeholder="e.g., User wants to speak to a human"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={data.onDelete}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Node Content */}
        <div className="p-4 pt-0">
          <div className="space-y-3">
            {/* Prompt Preview */}
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-xs font-medium text-gray-600 mb-1">System Prompt</div>
              <div className="text-sm text-gray-800 leading-relaxed">
                {localData.prompt?.substring(0, 120) || "No prompt configured"}
                {(localData.prompt?.length || 0) > 120 && "..."}
              </div>
            </div>

            {/* First Message Preview */}
            {localData.firstMessage && (
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-xs font-medium text-blue-600 mb-1">First Message</div>
                <div className="text-sm text-blue-800">
                  {localData.firstMessage.substring(0, 80)}
                  {localData.firstMessage.length > 80 && "..."}
                </div>
              </div>
            )}

            {/* Global Node Indicator */}
            {localData.globalNodePlan?.enabled && (
              <div className="bg-orange-50 rounded-lg p-3">
                <div className="text-xs font-medium text-orange-600 mb-1">Global Condition</div>
                <div className="text-sm text-orange-800">
                  {localData.globalNodePlan.enterCondition || "Always active"}
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="flex items-center justify-between pt-2 border-t border-gray-100">
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 px-2 text-xs"
                  onClick={() => setIsConfigOpen(true)}
                >
                  <Settings className="h-3 w-3 mr-1" />
                  Configure
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 px-2 text-xs"
                >
                  <Play className="h-3 w-3 mr-1" />
                  Test
                </Button>
              </div>
              <div className="text-xs text-gray-400">
                {localData.variableExtractionPlan ? "Variables: Yes" : "Variables: No"}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* VAPI-style Bottom Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-4 h-4 !bg-blue-500 !border-2 !border-white shadow-md"
      />
    </>
  );
};

export default ConversationNode;
