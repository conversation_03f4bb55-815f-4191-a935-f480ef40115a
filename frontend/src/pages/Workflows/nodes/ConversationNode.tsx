import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  MessageCircle,
  Settings,
  Trash2,
  Star,
  Play,
} from "lucide-react";

interface ConversationNodeData {
  name: string;
  prompt: string;
  firstMessage?: string;
  isStart?: boolean;
  variableExtractionPlan?: {
    schema: any;
    aliases?: Array<{ key: string; value: string }>;
  };
  globalNodePlan?: {
    enabled: boolean;
    enterCondition: string;
  };
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ConversationNode = ({ data, selected }: NodeProps<ConversationNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);

  const handleUpdate = useCallback((updates: Partial<ConversationNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handlePromptChange = (value: string) => {
    handleUpdate({ prompt: value });
  };

  const handleFirstMessageChange = (value: string) => {
    handleUpdate({ firstMessage: value });
  };

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleStartToggle = (checked: boolean) => {
    handleUpdate({ isStart: checked });
  };

  const handleGlobalToggle = (checked: boolean) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enabled: checked,
      },
    });
  };

  const handleGlobalConditionChange = (value: string) => {
    handleUpdate({
      globalNodePlan: {
        ...localData.globalNodePlan,
        enterCondition: value,
      },
    });
  };

  return (
    <>
      {/* VAPI-style Top Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white !rounded-full"
        style={{ top: -6 }}
      />

      {/* Exact VAPI Node Design */}
      <div
        className={`w-72 bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 relative ${
          selected ? "ring-2 ring-blue-500" : ""
        } ${localData.isStart ? "ring-2 ring-green-500" : ""}`}
      >
        {/* VAPI-style colored left border */}
        <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-l-lg"></div>

        {/* Node Header */}
        <div className="flex items-center justify-between p-3 pb-2">
          <div className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4 text-blue-500" />
            <div>
              <h3 className="text-sm font-medium text-gray-900">
                {localData.name || "Conversation"}
              </h3>
              {localData.isStart && (
                <div className="flex items-center gap-1 mt-0.5">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-xs text-green-600 font-medium">Start Node</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-gray-100">
                  <Settings className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[85vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5 text-blue-500" />
                    Configure Conversation Node
                  </DialogTitle>
                </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="is-start"
                        checked={localData.isStart || false}
                        onCheckedChange={handleStartToggle}
                      />
                      <Label htmlFor="is-start">Start Node</Label>
                    </div>

                    <div>
                      <Label htmlFor="first-message">First Message (Optional)</Label>
                      <Textarea
                        id="first-message"
                        value={localData.firstMessage || ""}
                        onChange={(e) => handleFirstMessageChange(e.target.value)}
                        placeholder="Message to speak when entering this node"
                        rows={2}
                      />
                    </div>

                    <div>
                      <Label htmlFor="prompt">Conversation Prompt</Label>
                      <Textarea
                        id="prompt"
                        value={localData.prompt || ""}
                        onChange={(e) => handlePromptChange(e.target.value)}
                        placeholder="Enter the conversation prompt that guides the AI's behavior"
                        rows={4}
                      />
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <Switch
                          id="is-global"
                          checked={localData.globalNodePlan?.enabled || false}
                          onCheckedChange={handleGlobalToggle}
                        />
                        <Label htmlFor="is-global">Global Node</Label>
                      </div>
                      
                      {localData.globalNodePlan?.enabled && (
                        <div>
                          <Label htmlFor="global-condition">Enter Condition</Label>
                          <Input
                            id="global-condition"
                            value={localData.globalNodePlan?.enterCondition || ""}
                            onChange={(e) => handleGlobalConditionChange(e.target.value)}
                            placeholder="e.g., User wants to speak to a human"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50"
                onClick={data.onDelete}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* VAPI-style Node Content */}
        <div className="px-3 pb-3">
          <div className="space-y-2">
            {/* First Message (if configured) */}
            {localData.firstMessage && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">First message:</span>
                <div className="text-gray-800 mt-0.5 leading-relaxed">
                  "{localData.firstMessage.substring(0, 60)}{localData.firstMessage.length > 60 ? "..." : ""}"
                </div>
              </div>
            )}

            {/* Prompt Preview */}
            <div className="text-xs text-gray-600">
              <span className="font-medium">Prompt:</span>
              <div className="text-gray-800 mt-0.5 leading-relaxed">
                {localData.prompt?.substring(0, 80) || "No prompt configured"}
                {(localData.prompt?.length || 0) > 80 && "..."}
              </div>
            </div>

            {/* Variable Extraction */}
            {localData.variableExtractionPlan && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Extract variables:</span>
                <div className="text-blue-600 mt-0.5">
                  Enabled
                </div>
              </div>
            )}

            {/* Global Node */}
            {localData.globalNodePlan?.enabled && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Global node:</span>
                <div className="text-orange-600 mt-0.5">
                  {localData.globalNodePlan.enterCondition || "Always active"}
                </div>
              </div>
            )}

            {/* Configuration Status */}
            <div className="flex items-center justify-between pt-1 mt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                {localData.prompt ? "Configured" : "Not configured"}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900"
                onClick={() => setIsConfigOpen(true)}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* VAPI-style Bottom Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-gray-400 !border-2 !border-white !rounded-full"
        style={{ bottom: -6 }}
      />
    </>
  );
};

export default ConversationNode;
