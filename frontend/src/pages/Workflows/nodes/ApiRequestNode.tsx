import { useState, useCallback } from "react";
import { <PERSON><PERSON>, Position, NodeProps } from "@reactflow/core";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Link,
  Settings,
  Trash2,
  Globe,
} from "lucide-react";

interface ApiRequestNodeData {
  name: string;
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: any;
  timeoutSeconds?: number;
  onUpdate: (data: any) => void;
  onDelete: () => void;
}

const ApiRequestNode = ({ data, selected }: NodeProps<ApiRequestNodeData>) => {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [localData, setLocalData] = useState(data);
  const [headersText, setHeadersText] = useState(
    JSON.stringify(data.headers || {}, null, 2)
  );
  const [bodyText, setBodyText] = useState(
    JSON.stringify(data.body || {}, null, 2)
  );

  const handleUpdate = useCallback((updates: Partial<ApiRequestNodeData>) => {
    const newData = { ...localData, ...updates };
    setLocalData(newData);
    data.onUpdate(newData);
  }, [localData, data]);

  const handleNameChange = (value: string) => {
    handleUpdate({ name: value });
  };

  const handleUrlChange = (value: string) => {
    handleUpdate({ url: value });
  };

  const handleMethodChange = (value: string) => {
    handleUpdate({ method: value as ApiRequestNodeData["method"] });
  };

  const handleTimeoutChange = (value: string) => {
    const timeout = parseInt(value) || 20;
    handleUpdate({ timeoutSeconds: timeout });
  };

  const handleHeadersChange = (value: string) => {
    setHeadersText(value);
    try {
      const headers = JSON.parse(value);
      handleUpdate({ headers });
    } catch (error) {
      // Invalid JSON, don't update
    }
  };

  const handleBodyChange = (value: string) => {
    setBodyText(value);
    try {
      const body = JSON.parse(value);
      handleUpdate({ body });
    } catch (error) {
      // Invalid JSON, don't update
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case "GET":
        return "bg-green-100 text-green-800";
      case "POST":
        return "bg-blue-100 text-blue-800";
      case "PUT":
        return "bg-yellow-100 text-yellow-800";
      case "DELETE":
        return "bg-red-100 text-red-800";
      case "PATCH":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <>
      {/* VAPI-style Top Handle */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-teal-400 !border-2 !border-white !rounded-full"
        style={{ top: -6 }}
      />

      {/* VAPI Green API Request Node */}
      <div
        className={`w-72 bg-green-600 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 relative ${
          selected ? "ring-2 ring-teal-400" : ""
        }`}
      >
        {/* VAPI Node Header */}
        <div className="bg-green-700 rounded-t-lg px-3 py-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Zap className="h-4 w-4 text-white" />
            <div>
              <h3 className="text-sm font-medium text-white">
                {localData.name || "API Request"}
              </h3>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Badge className="bg-green-500 text-white text-xs px-2 py-0.5">
              {localData.method || "GET"}
            </Badge>
            <Dialog open={isConfigOpen} onOpenChange={setIsConfigOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-white hover:bg-green-600">
                  <Settings className="h-3 w-3" />
                </Button>
              </DialogTrigger>
                <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Configure API Request Node</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="node-name">Node Name</Label>
                      <Input
                        id="node-name"
                        value={localData.name || ""}
                        onChange={(e) => handleNameChange(e.target.value)}
                        placeholder="Enter node name"
                      />
                    </div>

                    <div className="grid grid-cols-4 gap-4">
                      <div className="col-span-1">
                        <Label htmlFor="method">Method</Label>
                        <Select
                          value={localData.method || "GET"}
                          onValueChange={handleMethodChange}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="GET">GET</SelectItem>
                            <SelectItem value="POST">POST</SelectItem>
                            <SelectItem value="PUT">PUT</SelectItem>
                            <SelectItem value="DELETE">DELETE</SelectItem>
                            <SelectItem value="PATCH">PATCH</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="col-span-3">
                        <Label htmlFor="url">URL</Label>
                        <Input
                          id="url"
                          value={localData.url || ""}
                          onChange={(e) => handleUrlChange(e.target.value)}
                          placeholder="https://api.example.com/endpoint"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="timeout">Timeout (seconds)</Label>
                      <Input
                        id="timeout"
                        type="number"
                        value={localData.timeoutSeconds || 20}
                        onChange={(e) => handleTimeoutChange(e.target.value)}
                        min="1"
                        max="300"
                      />
                    </div>

                    <div>
                      <Label htmlFor="headers">Headers (JSON)</Label>
                      <Textarea
                        id="headers"
                        value={headersText}
                        onChange={(e) => handleHeadersChange(e.target.value)}
                        placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'
                        rows={4}
                        className="font-mono text-sm"
                      />
                    </div>

                    {(localData.method === "POST" || 
                      localData.method === "PUT" || 
                      localData.method === "PATCH") && (
                      <div>
                        <Label htmlFor="body">Request Body (JSON)</Label>
                        <Textarea
                          id="body"
                          value={bodyText}
                          onChange={(e) => handleBodyChange(e.target.value)}
                          placeholder='{"key": "value", "data": "{{variable}}"}'
                          rows={6}
                          className="font-mono text-sm"
                        />
                      </div>
                    )}
                  </div>
                </DialogContent>
              </Dialog>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 text-white hover:bg-green-600"
                onClick={data.onDelete}
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>

        {/* VAPI Green Node Content */}
        <div className="px-3 pb-3 text-white">
          <div className="space-y-2">
            {/* URL */}
            <div>
              <div className="text-xs text-green-200 mb-1">URL:</div>
              <div className="text-sm bg-green-500 rounded p-2 min-h-[30px] break-all">
                {localData.url || "https://api.example.com"}
              </div>
            </div>

            {/* Keyboard Shortcut */}
            <div className="text-xs text-green-200 mt-2">
              ⌘ + Shift + A
            </div>

            {/* Timeout */}
            {localData.timeoutSeconds && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Timeout:</span>
                <div className="text-gray-800 mt-0.5">
                  {localData.timeoutSeconds} seconds
                </div>
              </div>
            )}

            {/* Headers */}
            {Object.keys(localData.headers || {}).length > 0 && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Headers:</span>
                <div className="text-blue-600 mt-0.5">
                  {Object.keys(localData.headers || {}).length} configured
                </div>
              </div>
            )}

            {/* Body */}
            {localData.body && Object.keys(localData.body).length > 0 && (
              <div className="text-xs text-gray-600">
                <span className="font-medium">Request body:</span>
                <div className="text-blue-600 mt-0.5">
                  {Object.keys(localData.body).length} fields
                </div>
              </div>
            )}

            {/* Configuration Status */}
            <div className="flex items-center justify-between pt-1 mt-2 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                {localData.url ? "Configured" : "Not configured"}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900"
                onClick={() => setIsConfigOpen(true)}
              >
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* VAPI-style Bottom Handle */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-teal-400 !border-2 !border-white !rounded-full"
        style={{ bottom: -6 }}
      />
    </>
  );
};

export default ApiRequestNode;
