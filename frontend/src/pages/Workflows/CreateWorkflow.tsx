import { useState } from "react";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Workflow as WorkflowIcon, Template } from "lucide-react";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import {
  createWorkflow,
  getWorkflowTemplates,
} from "@/api/services/workflows/workflowService";
import { WorkflowTemplate } from "@/types/api";

const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(80, "Name must be 80 characters or less"),
  description: z.string().optional(),
  template: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

const CreateWorkflow = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      template: "",
    },
  });

  // Fetch workflow templates
  const { data: templatesData, isLoading: templatesLoading } = useQuery({
    queryKey: ["workflow-templates"],
    queryFn: getWorkflowTemplates,
  });

  // Create workflow mutation
  const createMutation = useMutation({
    mutationFn: createWorkflow,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["workflows"] });
      toast.success("Workflow created successfully");
      // Navigate to workflow builder
      navigate(`/dashboard/workflows/${data.data.id}/edit`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || "Failed to create workflow");
    },
  });

  const onSubmit = (data: FormData) => {
    let workflowData: any = {
      name: data.name,
      description: data.description,
    };

    // If template is selected, use template data
    if (selectedTemplate) {
      workflowData = {
        ...workflowData,
        nodes: selectedTemplate.nodes,
        edges: selectedTemplate.edges,
      };
    } else {
      // Create basic workflow with start node
      workflowData = {
        ...workflowData,
        nodes: [
          {
            type: "conversation",
            name: "start",
            isStart: true,
            prompt: "You are a helpful AI assistant. Greet the user and ask how you can help them today.",
            firstMessage: "Hello! How can I assist you today?",
            metadata: {}
          }
        ],
        edges: [],
      };
    }

    createMutation.mutate(workflowData);
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = templatesData?.data?.find((t: WorkflowTemplate) => t.id === templateId);
    setSelectedTemplate(template || null);
    form.setValue("template", templateId);
    
    if (template) {
      // Auto-fill name if empty
      if (!form.getValues("name")) {
        form.setValue("name", template.name);
      }
      // Auto-fill description if empty
      if (!form.getValues("description")) {
        form.setValue("description", template.description);
      }
    }
  };

  const templates = templatesData?.data || [];

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2">
        <WorkflowIcon className="h-5 w-5" />
        <h2 className="text-lg font-semibold">Create New Workflow</h2>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Provide basic details for your workflow
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter workflow name" {...field} />
                    </FormControl>
                    <FormDescription>
                      A descriptive name for your workflow (max 80 characters)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe what this workflow does..."
                        className="resize-none"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Optional description to help you remember the workflow's purpose
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Template Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Template className="h-4 w-4" />
                Choose Template
              </CardTitle>
              <CardDescription>
                Start with a pre-built template or create from scratch
              </CardDescription>
            </CardHeader>
            <CardContent>
              {templatesLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading templates...</span>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Blank Template */}
                  <Card
                    className={`cursor-pointer transition-colors ${
                      !selectedTemplate ? "ring-2 ring-blue-500 bg-blue-50" : "hover:bg-gray-50"
                    }`}
                    onClick={() => {
                      setSelectedTemplate(null);
                      form.setValue("template", "");
                    }}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          <WorkflowIcon className="h-5 w-5 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">Blank Workflow</h3>
                          <p className="text-sm text-gray-600">Start from scratch</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Template Options */}
                  {templates.map((template: WorkflowTemplate) => (
                    <Card
                      key={template.id}
                      className={`cursor-pointer transition-colors ${
                        selectedTemplate?.id === template.id
                          ? "ring-2 ring-blue-500 bg-blue-50"
                          : "hover:bg-gray-50"
                      }`}
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Template className="h-5 w-5 text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <h3 className="font-medium">{template.name}</h3>
                              <Badge variant="outline" className="text-xs">
                                {template.category}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {template.description}
                            </p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="secondary" className="text-xs">
                                {template.nodes?.length || 0} nodes
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => form.reset()}
            >
              Reset
            </Button>
            <Button
              type="submit"
              disabled={createMutation.isPending}
              className="min-w-[120px]"
            >
              {createMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Workflow"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CreateWorkflow;
