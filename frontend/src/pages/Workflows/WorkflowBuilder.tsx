import { useState, useCallback, useRef, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Connection,
  ReactFlowProvider,
  ReactFlowInstance,
  Panel,
} from "@reactflow/core";
import { toast } from "sonner";
import {
  Play,
  Save,
  ArrowLeft,
  Plus,
  Settings,
  Download,
  Upload,
  Trash2,
  Copy,
  Undo,
  Redo,
  MessageCircle,
  Globe,
  Phone,
  PhoneOff,
  Wrench,
  Zap,
  MoreHorizontal,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  Too<PERSON><PERSON><PERSON>rovider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  getWorkflowById,
  updateWorkflow,
  testWorkflow,
  autoSaveWorkflow,
} from "@/api/services/workflows/workflowService";
import { Workflow, WorkflowNode as WorkflowNodeType, WorkflowEdge } from "@/types/api";

// Import custom node components
import ConversationNode from "./nodes/ConversationNode";
import ApiRequestNode from "./nodes/ApiRequestNode";
import TransferCallNode from "./nodes/TransferCallNode";
import EndCallNode from "./nodes/EndCallNode";
import ToolNode from "./nodes/ToolNode";

// VAPI-style node type definitions
const VAPI_NODE_TYPES = [
  {
    type: "conversation",
    label: "Conversation",
    icon: MessageCircle,
    color: "bg-blue-500",
    description: "Main conversation node with AI responses",
  },
  {
    type: "apiRequest",
    label: "API Request",
    icon: Zap,
    color: "bg-green-500",
    description: "Make HTTP requests to external APIs",
  },
  {
    type: "transferCall",
    label: "Transfer Call",
    icon: Phone,
    color: "bg-yellow-500",
    description: "Transfer call to another number",
  },
  {
    type: "endCall",
    label: "End Call",
    icon: PhoneOff,
    color: "bg-red-500",
    description: "End the conversation",
  },
  {
    type: "tool",
    label: "Tool",
    icon: Wrench,
    color: "bg-purple-500",
    description: "Execute custom tools and functions",
  },
];

// Node types mapping
const nodeTypes = {
  conversation: ConversationNode,
  apiRequest: ApiRequestNode,
  transferCall: TransferCallNode,
  endCall: EndCallNode,
  tool: ToolNode,
};

// Initial node position
let nodeId = 0;
const getNodeId = () => `node_${nodeId++}`;

const WorkflowBuilder = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Fetch workflow data
  const { data: workflowData, isLoading } = useQuery({
    queryKey: ["workflow", id],
    queryFn: () => getWorkflowById(id!),
    enabled: !!id,
  });

  // Auto-save mutation
  const autoSaveMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Workflow> }) =>
      autoSaveWorkflow(id, data),
    onSuccess: () => {
      setLastSaved(new Date());
      setIsAutoSaving(false);
    },
    onError: () => {
      setIsAutoSaving(false);
    },
  });

  // Test workflow mutation
  const testMutation = useMutation({
    mutationFn: testWorkflow,
    onSuccess: () => {
      toast.success("Workflow test initiated successfully");
    },
  });

  // Convert workflow data to React Flow format
  useEffect(() => {
    if (workflowData?.data) {
      const workflow = workflowData.data;
      
      // Convert nodes
      const flowNodes: Node[] = workflow.nodes?.map((node: WorkflowNodeType, index: number) => ({
        id: node.name || `node-${index}`,
        type: node.type,
        position: { x: index * 250, y: index * 100 },
        data: {
          ...node,
          onUpdate: (updatedData: any) => handleNodeUpdate(node.name || `node-${index}`, updatedData),
          onDelete: () => handleNodeDelete(node.name || `node-${index}`),
        },
      })) || [];

      // Convert edges
      const flowEdges: Edge[] = workflow.edges?.map((edge: WorkflowEdge, index: number) => ({
        id: `edge-${index}`,
        source: edge.from,
        target: edge.to,
        label: edge.condition?.prompt || edge.condition?.expression || "",
        type: "smoothstep",
        animated: true,
        style: { stroke: "#3b82f6", strokeWidth: 2 },
      })) || [];

      setNodes(flowNodes);
      setEdges(flowEdges);
      nodeId = flowNodes.length;
    }
  }, [workflowData, setNodes, setEdges]);

  // Auto-save when nodes or edges change
  useEffect(() => {
    if (nodes.length > 0 && id && !isAutoSaving) {
      const timeoutId = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [nodes, edges, id, isAutoSaving]);

  const handleAutoSave = useCallback(() => {
    if (!id || isAutoSaving) return;

    setIsAutoSaving(true);
    
    // Convert React Flow data back to workflow format
    const workflowNodes: WorkflowNodeType[] = nodes.map((node) => ({
      type: node.type as any,
      name: node.id,
      isStart: node.data.isStart || false,
      prompt: node.data.prompt || "",
      firstMessage: node.data.firstMessage || "",
      metadata: node.data.metadata || {},
      ...node.data,
    }));

    const workflowEdges: WorkflowEdge[] = edges.map((edge) => ({
      from: edge.source,
      to: edge.target,
      condition: edge.label ? {
        type: "ai" as const,
        prompt: edge.label as string,
      } : undefined,
    }));

    autoSaveMutation.mutate({
      id,
      data: {
        nodes: workflowNodes,
        edges: workflowEdges,
      },
    });
  }, [id, nodes, edges, isAutoSaving, autoSaveMutation]);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = {
        ...params,
        type: "smoothstep",
        animated: true,
        style: { stroke: "#3b82f6", strokeWidth: 2 },
      };
      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData("application/reactflow");

      if (typeof type === "undefined" || !type || !reactFlowInstance || !reactFlowBounds) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: Node = {
        id: getNodeId(),
        type,
        position,
        data: {
          type,
          name: `${type}-${nodeId}`,
          prompt: `Enter prompt for ${type} node`,
          onUpdate: (updatedData: any) => handleNodeUpdate(`${type}-${nodeId}`, updatedData),
          onDelete: () => handleNodeDelete(`${type}-${nodeId}`),
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  const handleNodeUpdate = useCallback((nodeId: string, updatedData: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...updatedData } }
          : node
      )
    );
  }, [setNodes]);

  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
  }, [setNodes, setEdges]);

  const handleTestWorkflow = () => {
    if (id) {
      testMutation.mutate(id);
    }
  };

  const handleSave = () => {
    handleAutoSave();
    toast.success("Workflow saved successfully");
  };

  const addNode = (type: string) => {
    const position = { x: Math.random() * 400, y: Math.random() * 400 };
    const newNode: Node = {
      id: getNodeId(),
      type,
      position,
      data: {
        type,
        name: `${type}-${nodeId}`,
        prompt: `Enter prompt for ${type} node`,
        onUpdate: (updatedData: any) => handleNodeUpdate(`${type}-${nodeId}`, updatedData),
        onDelete: () => handleNodeDelete(`${type}-${nodeId}`),
      },
    };
    setNodes((nds) => nds.concat(newNode));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading workflow...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* VAPI-style Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/workflows")}
              className="text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                {workflowData?.data?.name || "Untitled Workflow"}
              </h1>
              <div className="flex items-center gap-3 text-xs text-gray-500">
                <span>
                  {isAutoSaving ? "Auto-saving..." : lastSaved ? `Saved ${lastSaved.toLocaleTimeString()}` : "Unsaved changes"}
                </span>
                <Badge variant="secondary" className="text-xs px-2 py-0.5">
                  {nodes.length} nodes
                </Badge>
              </div>
            </div>
          </div>

          {/* VAPI-style Toolbar */}
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <Undo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Undo</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <Redo className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Redo</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <div className="h-6 w-px bg-gray-300 mx-2" />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSave}
                    disabled={isAutoSaving}
                    className="h-8 px-3 text-sm"
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Save Workflow</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <Button
              onClick={handleTestWorkflow}
              disabled={testMutation.isPending}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 h-8 px-3 text-sm"
            >
              <Play className="h-4 w-4 mr-1" />
              {testMutation.isPending ? "Testing..." : "Test"}
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* VAPI-style Node Palette */}
        <div className="w-72 bg-gray-50 border-r border-gray-200 p-4">
          <div className="mb-6">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Add Nodes</h3>
            <div className="space-y-2">
              {VAPI_NODE_TYPES.map((nodeType) => {
                const IconComponent = nodeType.icon;
                return (
                  <div
                    key={nodeType.type}
                    className="group flex items-center gap-3 p-3 bg-white border border-gray-200 rounded-lg cursor-pointer hover:border-gray-300 hover:shadow-sm transition-all duration-200"
                    draggable
                    onDragStart={(event) => {
                      event.dataTransfer.setData("application/reactflow", nodeType.type);
                      event.dataTransfer.effectAllowed = "move";
                    }}
                    onClick={() => addNode(nodeType.type)}
                  >
                    <div className={`w-8 h-8 ${nodeType.color} rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform`}>
                      <IconComponent className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">{nodeType.label}</div>
                      <div className="text-xs text-gray-500">{nodeType.description}</div>
                    </div>
                    <Plus className="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                  </div>
                );
              })}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="border-t border-gray-200 pt-4">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h4>
            <div className="space-y-2">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8"
                onClick={() => {/* Add global node logic */}}
              >
                <Globe className="h-4 w-4 mr-2" />
                Add Global Node
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-start text-sm h-8"
                onClick={() => {/* Clear canvas logic */}}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Canvas
              </Button>
            </div>
          </div>
        </div>

        {/* VAPI-style Canvas */}
        <div className="flex-1 bg-gray-50" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
            defaultEdgeOptions={{
              style: { strokeWidth: 2, stroke: '#6366f1' },
              type: 'smoothstep',
            }}
            connectionLineStyle={{ strokeWidth: 2, stroke: '#6366f1' }}
            snapToGrid={true}
            snapGrid={[15, 15]}
          >
            {/* VAPI-style Controls */}
            <Panel position="bottom-right" className="mb-4 mr-4">
              <div className="flex items-center gap-2 bg-white border border-gray-200 rounded-lg p-2 shadow-sm">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Plus className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Zoom In</TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Copy className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Fit View</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </Panel>

            <Controls
              className="bg-white border border-gray-200 rounded-lg shadow-sm"
              showZoom={false}
              showFitView={false}
              showInteractive={false}
            />

            <MiniMap
              className="bg-white border border-gray-200 rounded-lg shadow-sm"
              nodeColor="#6366f1"
              maskColor="rgba(0, 0, 0, 0.1)"
            />

            <Background
              variant={BackgroundVariant.Dots}
              gap={20}
              size={1}
              color="#e5e7eb"
            />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
};

// Wrap with ReactFlowProvider
const WorkflowBuilderWrapper = () => (
  <ReactFlowProvider>
    <WorkflowBuilder />
  </ReactFlowProvider>
);

export default WorkflowBuilderWrapper;
