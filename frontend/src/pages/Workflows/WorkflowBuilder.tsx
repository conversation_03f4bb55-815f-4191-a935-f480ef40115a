import { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Connection,
  ReactFlowProvider,
  ReactFlowInstance,
  Panel,
} from "@reactflow/core";
import { toast } from "sonner";
import {
  Play,
  Save,
  ArrowLeft,
  Plus,
  Settings,
  Download,
  Upload,
  Trash2,
  Copy,
  Undo,
  Redo,
  MessageCircle,
  Globe,
  Phone,
  PhoneOff,
  Wrench,
  Zap,
  MoreHorizontal,
  MessageSquare,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  Toolt<PERSON><PERSON>rovider,
  Toolt<PERSON><PERSON>rigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  getWorkflowById,
  updateWorkflow,
  testWorkflow,
  autoSaveWorkflow,
} from "@/api/services/workflows/workflowService";
import { Workflow, WorkflowNode as WorkflowNodeType, WorkflowEdge } from "@/types/api";

// Import custom node components
import ConversationNode from "./nodes/ConversationNode";
import ApiRequestNode from "./nodes/ApiRequestNode";
import TransferCallNode from "./nodes/TransferCallNode";
import EndCallNode from "./nodes/EndCallNode";
import ToolNode from "./nodes/ToolNode";

// VAPI-style node type definitions
const VAPI_NODE_TYPES = [
  {
    type: "conversation",
    label: "Conversation",
    icon: MessageCircle,
    color: "bg-blue-500",
    description: "Main conversation node with AI responses",
  },
  {
    type: "apiRequest",
    label: "API Request",
    icon: Zap,
    color: "bg-green-500",
    description: "Make HTTP requests to external APIs",
  },
  {
    type: "transferCall",
    label: "Transfer Call",
    icon: Phone,
    color: "bg-yellow-500",
    description: "Transfer call to another number",
  },
  {
    type: "endCall",
    label: "End Call",
    icon: PhoneOff,
    color: "bg-red-500",
    description: "End the conversation",
  },
  {
    type: "tool",
    label: "Tool",
    icon: Wrench,
    color: "bg-purple-500",
    description: "Execute custom tools and functions",
  },
];

// Node types mapping
const nodeTypes = {
  conversation: ConversationNode,
  apiRequest: ApiRequestNode,
  transferCall: TransferCallNode,
  endCall: EndCallNode,
  tool: ToolNode,
};

// Initial node position
let nodeId = 0;
const getNodeId = () => `node_${nodeId++}`;

const WorkflowBuilder = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showNodePalette, setShowNodePalette] = useState(false);

  // Fetch workflow data
  const { data: workflowData, isLoading } = useQuery({
    queryKey: ["workflow", id],
    queryFn: () => getWorkflowById(id!),
    enabled: !!id,
  });

  // Auto-save mutation
  const autoSaveMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Workflow> }) =>
      autoSaveWorkflow(id, data),
    onSuccess: () => {
      setLastSaved(new Date());
      setIsAutoSaving(false);
    },
    onError: () => {
      setIsAutoSaving(false);
    },
  });

  // Test workflow mutation
  const testMutation = useMutation({
    mutationFn: testWorkflow,
    onSuccess: () => {
      toast.success("Workflow test initiated successfully");
    },
  });

  // Convert workflow data to React Flow format
  useEffect(() => {
    if (workflowData?.data) {
      const workflow = workflowData.data;
      
      // Convert nodes
      const flowNodes: Node[] = workflow.nodes?.map((node: WorkflowNodeType, index: number) => ({
        id: node.name || `node-${index}`,
        type: node.type,
        position: { x: index * 250, y: index * 100 },
        data: {
          ...node,
          onUpdate: (updatedData: any) => handleNodeUpdate(node.name || `node-${index}`, updatedData),
          onDelete: () => handleNodeDelete(node.name || `node-${index}`),
        },
      })) || [];

      // Convert edges
      const flowEdges: Edge[] = workflow.edges?.map((edge: WorkflowEdge, index: number) => ({
        id: `edge-${index}`,
        source: edge.from,
        target: edge.to,
        label: edge.condition?.prompt || edge.condition?.expression || "",
        type: "smoothstep",
        animated: true,
        style: { stroke: "#3b82f6", strokeWidth: 2 },
      })) || [];

      setNodes(flowNodes);
      setEdges(flowEdges);
      nodeId = flowNodes.length;
    }
  }, [workflowData, setNodes, setEdges]);

  // Auto-save when nodes or edges change
  useEffect(() => {
    if (nodes.length > 0 && id && !isAutoSaving) {
      const timeoutId = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [nodes, edges, id, isAutoSaving]);

  const handleAutoSave = useCallback(() => {
    if (!id || isAutoSaving) return;

    setIsAutoSaving(true);
    
    // Convert React Flow data back to workflow format
    const workflowNodes: WorkflowNodeType[] = nodes.map((node) => ({
      type: node.type as any,
      name: node.id,
      isStart: node.data.isStart || false,
      prompt: node.data.prompt || "",
      firstMessage: node.data.firstMessage || "",
      metadata: node.data.metadata || {},
      ...node.data,
    }));

    const workflowEdges: WorkflowEdge[] = edges.map((edge) => ({
      from: edge.source,
      to: edge.target,
      condition: edge.label ? {
        type: "ai" as const,
        prompt: edge.label as string,
      } : undefined,
    }));

    autoSaveMutation.mutate({
      id,
      data: {
        nodes: workflowNodes,
        edges: workflowEdges,
      },
    });
  }, [id, nodes, edges, isAutoSaving, autoSaveMutation]);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = {
        ...params,
        type: "smoothstep",
        animated: true,
        style: { stroke: "#3b82f6", strokeWidth: 2 },
      };
      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData("application/reactflow");

      if (typeof type === "undefined" || !type || !reactFlowInstance || !reactFlowBounds) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: Node = {
        id: getNodeId(),
        type,
        position,
        data: {
          type,
          name: `${type}-${nodeId}`,
          prompt: `Enter prompt for ${type} node`,
          onUpdate: (updatedData: any) => handleNodeUpdate(`${type}-${nodeId}`, updatedData),
          onDelete: () => handleNodeDelete(`${type}-${nodeId}`),
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  const handleNodeUpdate = useCallback((nodeId: string, updatedData: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...updatedData } }
          : node
      )
    );
  }, [setNodes]);

  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
  }, [setNodes, setEdges]);

  const handleTestWorkflow = () => {
    if (id) {
      testMutation.mutate(id);
    }
  };

  const handleSave = () => {
    handleAutoSave();
    toast.success("Workflow saved successfully");
  };

  const addNode = (type: string) => {
    const position = { x: Math.random() * 400, y: Math.random() * 400 };
    const newNode: Node = {
      id: getNodeId(),
      type,
      position,
      data: {
        type,
        name: `${type}-${nodeId}`,
        prompt: `Enter prompt for ${type} node`,
        onUpdate: (updatedData: any) => handleNodeUpdate(`${type}-${nodeId}`, updatedData),
        onDelete: () => handleNodeDelete(`${type}-${nodeId}`),
      },
    };
    setNodes((nds) => nds.concat(newNode));
  };

  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node);
  }, []);

  const updateNodeData = useCallback((nodeId: string, newData: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...newData } }
          : node
      )
    );
  }, [setNodes]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading workflow...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-900">
      {/* Exact VAPI Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/workflows")}
              className="text-gray-300 hover:text-white h-8"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div className="h-4 w-px bg-gray-600" />
            <div className="flex items-center space-x-2">
              <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white text-xs font-bold">W</span>
              </div>
              <span className="text-white font-medium">
                {workflowData?.data?.name || "Untitled Workflow"}
              </span>
              <Badge variant="secondary" className="bg-gray-700 text-gray-300 text-xs">
                {isAutoSaving ? "Saving..." : "Saved"}
              </Badge>
            </div>
          </div>

          {/* VAPI Toolbar */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-300 hover:text-white h-8 px-3"
            >
              Global Prompt
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-300 hover:text-white h-8 px-3"
            >
              Global Voice
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-gray-300 hover:text-white h-8 px-3"
            >
              Variables
            </Button>

            <div className="h-4 w-px bg-gray-600 mx-2" />

            <Button
              onClick={handleSave}
              disabled={isAutoSaving}
              variant="ghost"
              size="sm"
              className="text-gray-300 hover:text-white h-8 px-3"
            >
              Save
            </Button>

            <Button
              onClick={handleTestWorkflow}
              disabled={testMutation.isPending}
              size="sm"
              className="bg-green-600 hover:bg-green-700 h-8 px-3 text-white"
            >
              <Phone className="h-4 w-4 mr-1" />
              Call
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-300 hover:text-white">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-gray-800 border-gray-700">
                <DropdownMenuItem className="text-gray-300 hover:text-white">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </DropdownMenuItem>
                <DropdownMenuItem className="text-gray-300 hover:text-white">
                  <Upload className="mr-2 h-4 w-4" />
                  Import
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Exact VAPI Left Sidebar */}
        <div className="w-64 bg-gray-800 border-r border-gray-700 p-4">
          <div className="mb-6">
            <Button
              className="w-full bg-teal-600 hover:bg-teal-700 text-white mb-4 h-10"
              onClick={() => setShowNodePalette(!showNodePalette)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add a Node
            </Button>

            {/* Node Palette Dropdown */}
            {showNodePalette && (
              <div className="space-y-2 mb-4">
                {VAPI_NODE_TYPES.map((nodeType) => {
                  const IconComponent = nodeType.icon;
                  return (
                    <div
                      key={nodeType.type}
                      className="flex items-center gap-3 p-3 bg-gray-700 hover:bg-gray-600 rounded-lg cursor-pointer transition-colors"
                      onClick={() => {
                        addNode(nodeType.type);
                        setShowNodePalette(false);
                      }}
                    >
                      <div className={`w-6 h-6 ${nodeType.color} rounded flex items-center justify-center`}>
                        <IconComponent className="h-3 w-3 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-white">{nodeType.label}</div>
                        <div className="text-xs text-gray-400">⌘ + Shift + {nodeType.type.charAt(0).toUpperCase()}</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* VAPI Sidebar Options */}
          <div className="space-y-2">
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700 h-10"
            >
              <Globe className="h-4 w-4 mr-3" />
              Global Prompt
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700 h-10"
            >
              <Globe className="h-4 w-4 mr-3" />
              Global Voice
            </Button>
            <Button
              variant="ghost"
              className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-700 h-10"
            >
              <Settings className="h-4 w-4 mr-3" />
              Variables
            </Button>
          </div>
        </div>

        {/* VAPI Canvas Area */}
        <div className="flex-1 bg-gray-900" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            onNodeClick={onNodeClick}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
            defaultEdgeOptions={{
              style: { strokeWidth: 2, stroke: '#10b981' },
              type: 'smoothstep',
              animated: true,
            }}
            connectionLineStyle={{ strokeWidth: 2, stroke: '#10b981' }}
            snapToGrid={true}
            snapGrid={[15, 15]}
            className="bg-gray-900"
          >
            <Controls
              className="bg-gray-800 border border-gray-700 rounded-lg shadow-sm"
              showZoom={true}
              showFitView={true}
              showInteractive={false}
            />

            <MiniMap
              className="bg-gray-800 border border-gray-700 rounded-lg shadow-sm"
              nodeColor="#3b82f6"
              maskColor="rgba(0, 0, 0, 0.3)"
            />

            <Background
              variant={BackgroundVariant.Dots}
              gap={20}
              size={1}
              color="#374151"
            />
          </ReactFlow>
        </div>

        {/* VAPI Right Configuration Panel */}
        {selectedNode && (
          <div className="w-80 bg-gray-800 border-l border-gray-700 p-4 overflow-y-auto">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-teal-600 rounded flex items-center justify-center">
                  <MessageSquare className="h-3 w-3 text-white" />
                </div>
                <h3 className="text-white font-medium">
                  {selectedNode.data.label || selectedNode.type}
                </h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedNode(null)}
                className="h-6 w-6 p-0 text-gray-400 hover:text-white"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Node Configuration Form */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Node Type
                </label>
                <div className="text-sm text-gray-400 bg-gray-700 p-2 rounded">
                  {selectedNode.data.label || selectedNode.type}
                </div>
              </div>

              {selectedNode.type === 'conversation' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      First Message
                    </label>
                    <textarea
                      className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-white text-sm"
                      rows={3}
                      placeholder="Say something... Use {{variableName}} for dynamic content"
                      value={selectedNode.data.firstMessage || ''}
                      onChange={(e) => updateNodeData(selectedNode.id, { firstMessage: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Prompt
                    </label>
                    <textarea
                      className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-white text-sm"
                      rows={4}
                      placeholder="Enter the prompt for this conversation node"
                      value={selectedNode.data.prompt || ''}
                      onChange={(e) => updateNodeData(selectedNode.id, { prompt: e.target.value })}
                    />
                  </div>
                </>
              )}

              {/* Global Node Toggle */}
              <div className="flex items-center justify-between p-3 bg-gray-700 rounded">
                <div>
                  <div className="text-sm font-medium text-white">Global Node</div>
                  <div className="text-xs text-gray-400">Make this node available from any point in the conversation</div>
                </div>
                <input
                  type="checkbox"
                  className="w-4 h-4 text-teal-600 bg-gray-600 border-gray-500 rounded focus:ring-teal-500"
                  checked={selectedNode.data.isGlobal || false}
                  onChange={(e) => updateNodeData(selectedNode.id, { isGlobal: e.target.checked })}
                />
              </div>

              {/* Additional Settings */}
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-gray-300">Model Settings</h4>
                <div className="text-xs text-gray-400">Configure the LLM model</div>

                <h4 className="text-sm font-medium text-gray-300">Voice Settings</h4>
                <div className="text-xs text-gray-400">Configure the text-to-speech engine</div>

                <h4 className="text-sm font-medium text-gray-300">Transcriber Settings</h4>
                <div className="text-xs text-gray-400">Configure the speech-to-text engine</div>

                <h4 className="text-sm font-medium text-gray-300">Extract Variables</h4>
                <div className="text-xs text-gray-400">Extract data from the conversation. Extracted data will be available to use as dynamic variables for the rest of the conversation</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Wrap with ReactFlowProvider
const WorkflowBuilderWrapper = () => (
  <ReactFlowProvider>
    <WorkflowBuilder />
  </ReactFlowProvider>
);

export default WorkflowBuilderWrapper;
