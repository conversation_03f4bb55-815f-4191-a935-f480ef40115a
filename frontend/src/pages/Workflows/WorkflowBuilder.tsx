import { useState, useCallback, useRef, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  Connection,
  ReactFlowProvider,
  ReactFlowInstance,
} from "@reactflow/core";
import { toast } from "sonner";
import {
  Play,
  Save,
  ArrowLeft,
  Plus,
  Settings,
  Download,
  Upload,
  Trash2,
  Copy,
  Undo,
  Redo,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  getWorkflowById,
  updateWorkflow,
  testWorkflow,
  autoSaveWorkflow,
} from "@/api/services/workflows/workflowService";
import { Workflow, WorkflowNode as WorkflowNodeType, WorkflowEdge } from "@/types/api";

// Import custom node components
import ConversationNode from "./nodes/ConversationNode";
import ApiRequestNode from "./nodes/ApiRequestNode";
import TransferCallNode from "./nodes/TransferCallNode";
import EndCallNode from "./nodes/EndCallNode";
import ToolNode from "./nodes/ToolNode";

// Node types mapping
const nodeTypes = {
  conversation: ConversationNode,
  apiRequest: ApiRequestNode,
  transferCall: TransferCallNode,
  endCall: EndCallNode,
  tool: ToolNode,
};

// Initial node position
let nodeId = 0;
const getNodeId = () => `node_${nodeId++}`;

const WorkflowBuilder = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedNode, setSelectedNode] = useState<Node | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Fetch workflow data
  const { data: workflowData, isLoading } = useQuery({
    queryKey: ["workflow", id],
    queryFn: () => getWorkflowById(id!),
    enabled: !!id,
  });

  // Auto-save mutation
  const autoSaveMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Workflow> }) =>
      autoSaveWorkflow(id, data),
    onSuccess: () => {
      setLastSaved(new Date());
      setIsAutoSaving(false);
    },
    onError: () => {
      setIsAutoSaving(false);
    },
  });

  // Test workflow mutation
  const testMutation = useMutation({
    mutationFn: testWorkflow,
    onSuccess: () => {
      toast.success("Workflow test initiated successfully");
    },
  });

  // Convert workflow data to React Flow format
  useEffect(() => {
    if (workflowData?.data) {
      const workflow = workflowData.data;
      
      // Convert nodes
      const flowNodes: Node[] = workflow.nodes?.map((node: WorkflowNodeType, index: number) => ({
        id: node.name || `node-${index}`,
        type: node.type,
        position: { x: index * 250, y: index * 100 },
        data: {
          ...node,
          onUpdate: (updatedData: any) => handleNodeUpdate(node.name || `node-${index}`, updatedData),
          onDelete: () => handleNodeDelete(node.name || `node-${index}`),
        },
      })) || [];

      // Convert edges
      const flowEdges: Edge[] = workflow.edges?.map((edge: WorkflowEdge, index: number) => ({
        id: `edge-${index}`,
        source: edge.from,
        target: edge.to,
        label: edge.condition?.prompt || edge.condition?.expression || "",
        type: "smoothstep",
        animated: true,
        style: { stroke: "#3b82f6", strokeWidth: 2 },
      })) || [];

      setNodes(flowNodes);
      setEdges(flowEdges);
      nodeId = flowNodes.length;
    }
  }, [workflowData, setNodes, setEdges]);

  // Auto-save when nodes or edges change
  useEffect(() => {
    if (nodes.length > 0 && id && !isAutoSaving) {
      const timeoutId = setTimeout(() => {
        handleAutoSave();
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [nodes, edges, id, isAutoSaving]);

  const handleAutoSave = useCallback(() => {
    if (!id || isAutoSaving) return;

    setIsAutoSaving(true);
    
    // Convert React Flow data back to workflow format
    const workflowNodes: WorkflowNodeType[] = nodes.map((node) => ({
      type: node.type as any,
      name: node.id,
      isStart: node.data.isStart || false,
      prompt: node.data.prompt || "",
      firstMessage: node.data.firstMessage || "",
      metadata: node.data.metadata || {},
      ...node.data,
    }));

    const workflowEdges: WorkflowEdge[] = edges.map((edge) => ({
      from: edge.source,
      to: edge.target,
      condition: edge.label ? {
        type: "ai" as const,
        prompt: edge.label as string,
      } : undefined,
    }));

    autoSaveMutation.mutate({
      id,
      data: {
        nodes: workflowNodes,
        edges: workflowEdges,
      },
    });
  }, [id, nodes, edges, isAutoSaving, autoSaveMutation]);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge = {
        ...params,
        type: "smoothstep",
        animated: true,
        style: { stroke: "#3b82f6", strokeWidth: 2 },
      };
      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData("application/reactflow");

      if (typeof type === "undefined" || !type || !reactFlowInstance || !reactFlowBounds) {
        return;
      }

      const position = reactFlowInstance.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: Node = {
        id: getNodeId(),
        type,
        position,
        data: {
          type,
          name: `${type}-${nodeId}`,
          prompt: `Enter prompt for ${type} node`,
          onUpdate: (updatedData: any) => handleNodeUpdate(`${type}-${nodeId}`, updatedData),
          onDelete: () => handleNodeDelete(`${type}-${nodeId}`),
        },
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  const handleNodeUpdate = useCallback((nodeId: string, updatedData: any) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...updatedData } }
          : node
      )
    );
  }, [setNodes]);

  const handleNodeDelete = useCallback((nodeId: string) => {
    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
  }, [setNodes, setEdges]);

  const handleTestWorkflow = () => {
    if (id) {
      testMutation.mutate(id);
    }
  };

  const handleSave = () => {
    handleAutoSave();
    toast.success("Workflow saved successfully");
  };

  const addNode = (type: string) => {
    const position = { x: Math.random() * 400, y: Math.random() * 400 };
    const newNode: Node = {
      id: getNodeId(),
      type,
      position,
      data: {
        type,
        name: `${type}-${nodeId}`,
        prompt: `Enter prompt for ${type} node`,
        onUpdate: (updatedData: any) => handleNodeUpdate(`${type}-${nodeId}`, updatedData),
        onDelete: () => handleNodeDelete(`${type}-${nodeId}`),
      },
    };
    setNodes((nds) => nds.concat(newNode));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading workflow...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/workflows")}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Workflows
            </Button>
            <div>
              <h1 className="text-xl font-semibold">
                {workflowData?.data?.name || "Workflow Builder"}
              </h1>
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <span>
                  {isAutoSaving ? "Auto-saving..." : lastSaved ? `Last saved: ${lastSaved.toLocaleTimeString()}` : ""}
                </span>
                <Badge variant="outline">
                  {nodes.length} nodes
                </Badge>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSave}
                    disabled={isAutoSaving}
                  >
                    <Save className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Save Workflow</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <Button
              onClick={handleTestWorkflow}
              disabled={testMutation.isPending}
              className="bg-green-600 hover:bg-green-700"
            >
              <Play className="h-4 w-4 mr-2" />
              {testMutation.isPending ? "Testing..." : "Test Workflow"}
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Sidebar - Node Palette */}
        <div className="w-64 bg-white border-r border-gray-200 p-4">
          <h3 className="font-semibold mb-4">Node Types</h3>
          <div className="space-y-2">
            {[
              { type: "conversation", label: "Conversation", icon: "💬" },
              { type: "apiRequest", label: "API Request", icon: "🔗" },
              { type: "transferCall", label: "Transfer Call", icon: "📞" },
              { type: "endCall", label: "End Call", icon: "🔚" },
              { type: "tool", label: "Tool", icon: "🔧" },
            ].map((nodeType) => (
              <div
                key={nodeType.type}
                className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                draggable
                onDragStart={(event) => {
                  event.dataTransfer.setData("application/reactflow", nodeType.type);
                  event.dataTransfer.effectAllowed = "move";
                }}
                onClick={() => addNode(nodeType.type)}
              >
                <span className="text-lg">{nodeType.icon}</span>
                <span className="text-sm font-medium">{nodeType.label}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Main Canvas */}
        <div className="flex-1" ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onInit={setReactFlowInstance}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={nodeTypes}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap />
            <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
          </ReactFlow>
        </div>
      </div>
    </div>
  );
};

// Wrap with ReactFlowProvider
const WorkflowBuilderWrapper = () => (
  <ReactFlowProvider>
    <WorkflowBuilder />
  </ReactFlowProvider>
);

export default WorkflowBuilderWrapper;
