import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, Edit, Play, Settings, Trash2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { getWorkflowById } from "@/api/services/workflows/workflowService";
import { Workflow, WorkflowNode } from "@/types/api";
import Loading from "@/Loading";

const WorkflowDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Fetch workflow data
  const { data: workflowData, isLoading, error } = useQuery({
    queryKey: ["workflow", id],
    queryFn: () => getWorkflowById(id!),
    enabled: !!id,
  });

  if (isLoading) {
    return <Loading />;
  }

  if (error || !workflowData?.success) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Oops! The page you're looking for doesn't exist.
          </h2>
          <p className="text-gray-600 mb-6">
            The workflow you're trying to access might have been deleted or the URL is incorrect.
          </p>
          <Button onClick={() => navigate("/workflows")} className="bg-primary hover:bg-primary/90">
            Go Home
          </Button>
        </div>
      </div>
    );
  }

  const workflow: Workflow = workflowData.data;

  const getNodeTypeColor = (type: string) => {
    switch (type) {
      case 'conversation':
        return 'bg-blue-100 text-blue-800';
      case 'apiRequest':
        return 'bg-green-100 text-green-800';
      case 'transferCall':
        return 'bg-yellow-100 text-yellow-800';
      case 'endCall':
        return 'bg-red-100 text-red-800';
      case 'tool':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate("/workflows")}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Workflows</span>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{workflow.name}</h1>
            <p className="text-gray-600">
              Created: {new Date(workflow.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/workflows/${id}/edit`)}
            className="flex items-center space-x-2"
          >
            <Edit className="h-4 w-4" />
            <span>Edit</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
          >
            <Play className="h-4 w-4" />
            <span>Test</span>
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center space-x-2"
          >
            <Settings className="h-4 w-4" />
            <span>Settings</span>
          </Button>
        </div>
      </div>

      <Separator />

      {/* Workflow Info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Overview</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Status</label>
              <div className="mt-1">
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Active
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Nodes</label>
              <p className="text-lg font-semibold">{workflow.nodes?.length || 0}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Edges</label>
              <p className="text-lg font-semibold">{workflow.edges?.length || 0}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Last Updated</label>
              <p className="text-sm text-gray-900">
                {new Date(workflow.updatedAt).toLocaleString()}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Model</label>
              <p className="text-sm text-gray-900">
                {workflow.model ? `${workflow.model.provider} - ${workflow.model.model}` : 'Not configured'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Voice</label>
              <p className="text-sm text-gray-900">
                {workflow.voice ? workflow.voice.name : 'Not configured'}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Background Sound</label>
              <p className="text-sm text-gray-900">
                {workflow.backgroundSound || 'Off'}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Statistics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Executions</label>
              <p className="text-lg font-semibold">
                {workflow.localData?.execution_count || 0}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Last Executed</label>
              <p className="text-sm text-gray-900">
                {workflow.localData?.last_executed 
                  ? new Date(workflow.localData.last_executed).toLocaleString()
                  : 'Never'
                }
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Version</label>
              <p className="text-sm text-gray-900">
                {workflow.localData?.version || '1.0.0'}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Nodes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Workflow Nodes</CardTitle>
        </CardHeader>
        <CardContent>
          {workflow.nodes && workflow.nodes.length > 0 ? (
            <div className="space-y-4">
              {workflow.nodes.map((node: WorkflowNode, index: number) => (
                <div
                  key={index}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <Badge className={getNodeTypeColor(node.type)}>
                        {node.type}
                      </Badge>
                      <h3 className="font-medium text-gray-900">{node.name}</h3>
                      {node.isStart && (
                        <Badge variant="outline" className="text-xs">
                          Start Node
                        </Badge>
                      )}
                    </div>
                  </div>
                  {node.prompt && (
                    <div className="mt-2">
                      <label className="text-xs font-medium text-gray-600">Prompt:</label>
                      <p className="text-sm text-gray-800 mt-1 bg-gray-50 p-2 rounded">
                        {node.prompt}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No nodes configured</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Global Prompt */}
      {workflow.globalPrompt && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Global Prompt</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-800">{workflow.globalPrompt}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Description */}
      {workflow.localData?.description && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-800">{workflow.localData.description}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default WorkflowDetail;
