import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Play,
  Edit,
  Copy,
  Trash2,
  Download,
  Upload,
  Zap,
  MessageCircle,
  Phone,
  PhoneOff,
  Wrench,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { vapiWorkflowService, VapiWorkflow } from "@/services/vapiWorkflowService";

const VapiWorkflows = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");

  // Fetch VAPI workflows
  const { data: workflows = [], isLoading } = useQuery({
    queryKey: ["vapi-workflows"],
    queryFn: () => vapiWorkflowService.getWorkflows(),
  });

  // Delete workflow mutation
  const deleteWorkflowMutation = useMutation({
    mutationFn: (id: string) => vapiWorkflowService.deleteWorkflow(id),
    onSuccess: () => {
      toast.success("Workflow deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["vapi-workflows"] });
    },
    onError: () => {
      toast.error("Failed to delete workflow");
    },
  });

  // Clone workflow mutation
  const cloneWorkflowMutation = useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      vapiWorkflowService.cloneWorkflow(id, name),
    onSuccess: () => {
      toast.success("Workflow cloned successfully");
      queryClient.invalidateQueries({ queryKey: ["vapi-workflows"] });
    },
    onError: () => {
      toast.error("Failed to clone workflow");
    },
  });

  // Execute workflow mutation
  const executeWorkflowMutation = useMutation({
    mutationFn: (id: string) => vapiWorkflowService.executeWorkflow(id),
    onSuccess: (execution) => {
      toast.success(`Workflow execution started: ${execution.id}`);
    },
    onError: () => {
      toast.error("Failed to execute workflow");
    },
  });

  // Filter workflows
  const filteredWorkflows = workflows.filter((workflow) => {
    const matchesSearch = workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         workflow.description?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === "all" || workflow.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const getNodeTypeIcon = (type: string) => {
    switch (type) {
      case "conversation":
        return MessageCircle;
      case "apiRequest":
        return Zap;
      case "transferCall":
        return Phone;
      case "endCall":
        return PhoneOff;
      case "tool":
        return Wrench;
      default:
        return MessageCircle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-yellow-100 text-yellow-800";
      case "archived":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleDeleteWorkflow = (id: string) => {
    if (window.confirm("Are you sure you want to delete this workflow?")) {
      deleteWorkflowMutation.mutate(id);
    }
  };

  const handleCloneWorkflow = (workflow: VapiWorkflow) => {
    const newName = prompt("Enter name for cloned workflow:", `${workflow.name} (Copy)`);
    if (newName) {
      cloneWorkflowMutation.mutate({ id: workflow.id, name: newName });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading VAPI workflows...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">VAPI Workflows</h1>
          <p className="text-gray-600 mt-1">
            Build and manage voice AI workflows with visual flow builder
          </p>
        </div>
        <Button onClick={() => navigate("/workflows/vapi/create")} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Create Workflow
        </Button>
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search workflows..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md text-sm"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="draft">Draft</option>
          <option value="archived">Archived</option>
        </select>
      </div>

      {/* Workflows Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredWorkflows.map((workflow) => {
          const nodeTypes = [...new Set(workflow.nodes.map(node => node.type))];
          
          return (
            <Card key={workflow.id} className="hover:shadow-lg transition-shadow duration-200">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
                      {workflow.name}
                    </CardTitle>
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {workflow.description || "No description"}
                    </p>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => navigate(`/workflows/vapi/${workflow.id}`)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => executeWorkflowMutation.mutate(workflow.id)}>
                        <Play className="mr-2 h-4 w-4" />
                        Test Call
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleCloneWorkflow(workflow)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Clone
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={() => handleDeleteWorkflow(workflow.id)}>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Status and Stats */}
                  <div className="flex items-center justify-between">
                    <Badge className={`text-xs px-2 py-1 ${getStatusColor(workflow.status)}`}>
                      {workflow.status}
                    </Badge>
                    <div className="text-xs text-gray-500">
                      {workflow.nodes.length} nodes • {workflow.edges.length} connections
                    </div>
                  </div>

                  {/* Node Types */}
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">Node types:</span>
                    <div className="flex gap-1">
                      {nodeTypes.slice(0, 4).map((type) => {
                        const IconComponent = getNodeTypeIcon(type);
                        return (
                          <div
                            key={type}
                            className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center"
                            title={type}
                          >
                            <IconComponent className="h-3 w-3 text-gray-600" />
                          </div>
                        );
                      })}
                      {nodeTypes.length > 4 && (
                        <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center text-xs text-gray-600">
                          +{nodeTypes.length - 4}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => navigate(`/workflows/vapi/${workflow.id}`)}
                      className="flex-1"
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => executeWorkflowMutation.mutate(workflow.id)}
                      disabled={executeWorkflowMutation.isPending}
                      className="flex-1 bg-green-600 hover:bg-green-700"
                    >
                      <Phone className="h-3 w-3 mr-1" />
                      Call
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredWorkflows.length === 0 && (
        <div className="text-center py-12">
          <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No workflows found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || selectedStatus !== "all"
              ? "Try adjusting your search or filters"
              : "Create your first VAPI workflow to get started"}
          </p>
          {!searchQuery && selectedStatus === "all" && (
            <Button onClick={() => navigate("/workflows/vapi/create")}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Workflow
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default VapiWorkflows;
