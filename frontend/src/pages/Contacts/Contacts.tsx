import React, { useEffect, useState } from "react";
import { ModernTable } from "@/components/ModernTable";
import Header from "@/components/Header";
import Page from "@/components/Page";
import Loading from "@/Loading";
import {
  Edit,
  Eye,
  Trash,
  Plus,
  Users,
  Mail,
  Phone,
  Tag,
  UserPlus,
  MessageSquare,
  Calendar,
  Share2,
  Circle,
} from "lucide-react";
import { AddContact } from "./AddContact";
import { useQuery } from "@tanstack/react-query";
import { getAllContacts } from "@/api/services/contact/contactService";
import CustomPagination from "@/components/CustomPagination";
import { useSearchParams } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import ContactGrid from "@/components/ContactGrid";
import ContactFilterPopover from "@/components/ContactFilterPopover";

interface Contact {
  id: string;
  email: string;
  contact_number: string;
  list_name: string;
  // other properties
}

const Contacts = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [debouncedSearch, setDebouncedSearch] = useState(
    searchParams.get("search") || ""
  );
  const [searchQuery, setSearchQuery] = useState(debouncedSearch);
  const [viewMode, setViewMode] = useState<"list" | "grid">(
    searchParams.get("view") === "grid" ? "grid" : "list"
  );
  const [activeFilters, setActiveFilters] = useState<any>({});
  const [showFilters, setShowFilters] = useState(false);

  // Extract current page from searchParams
  const currentPage = parseInt(searchParams.get("page") || "1");

  // Debouncing effect for search
  useEffect(() => {
    const handler = setTimeout(() => {
      setSearchQuery(debouncedSearch);
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev);
        if (debouncedSearch) {
          newParams.set("search", debouncedSearch);
        } else {
          newParams.delete("search");
        }
        return newParams;
      });
    }, 500); // Reduced debounce time for better UX

    return () => clearTimeout(handler);
  }, [debouncedSearch, setSearchParams]);

  // Update URL when view mode changes
  useEffect(() => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      if (viewMode === "grid") {
        newParams.set("view", "grid");
      } else {
        newParams.delete("view");
      }
      return newParams;
    });
  }, [viewMode, setSearchParams]);

  const { data, isLoading, error } = useQuery({
    queryKey: ["getAllContacts", currentPage, searchQuery],
    queryFn: () => getAllContacts(currentPage, searchQuery),
  });

  if (isLoading) return <Loading />;

  if (error) {
    console.error("Error fetching contacts", error);
    return <div>Error loading contacts</div>;
  }

  const handlePageChange = (page: number) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set("page", page.toString());
      return newParams;
    });
  };

  const handleSearchChange = (value: string) => {
    setDebouncedSearch(value); // Update the debounced search state
  };

  const handleViewModeChange = (mode: "list" | "grid") => {
    setViewMode(mode);
  };

  const handleFilterChange = (filters: any) => {
    setActiveFilters(filters);
    setShowFilters(Object.keys(filters).some((key) => filters[key] !== null));
  };

  // Map data.contacts to include 'id' property
  const contactsWithId = data.contacts.map((contact: any) => ({
    id: contact.contact_id,
    ...contact,
  }));

  // Filter data based on active filters
  const filteredData = contactsWithId.filter((item: any) => {
    // Status filter
    const matchesStatus = activeFilters.status
      ? String(item.id).charCodeAt(0) % 3 ===
        (activeFilters.status === "Active"
          ? 0
          : activeFilters.status === "Inactive"
          ? 1
          : 2)
      : true;

    // List filter
    const matchesList = activeFilters.list
      ? item.list_name === activeFilters.list
      : true;

    return matchesStatus && matchesList;
  });

  // Function to get initials from email
  const getInitials = (email: string) => {
    const name = email.split("@")[0];
    return name.substring(0, 2).toUpperCase();
  };

  // Function to format phone number
  const formatPhoneNumber = (phone: string) => {
    if (!phone) return "";
    // Basic formatting for display
    if (phone.startsWith("+")) {
      // International format
      return phone.replace(/(\+\d{1,3})(\d{3})(\d{3})(\d{4})/, "$1 $2 $3 $4");
    }
    // US format
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  };

  // Function to get random list badge color
  const getListBadgeVariant = (listName: string): "purple" | "info" | "success" | "warning" | "secondary" => {
    const variants = ["purple", "info", "success", "warning", "secondary"] as const;
    // Simple hash function to consistently assign the same color to the same list
    const hash = listName
      .split("")
      .reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return variants[hash % variants.length];
  };

  // Common actions for both list and grid views
  const contactActions = [
    {
      label: "Send Message",
      icon: <MessageSquare className="h-4 w-4" />,
      onClick: (item: any) => console.log("Messaging", item),
    },
    {
      label: "Edit Contact",
      icon: <Edit className="h-4 w-4" />,
      onClick: (item: any) => console.log("Editing", item),
    },
    {
      label: "Delete Contact",
      icon: <Trash className="h-4 w-4" />,
      onClick: (item: any) => console.log("Deleting", item),
    },
  ];

  return (
    <Page>
      <Header
        title="Contact Directory"
        buttonText="Contact"
        filterByName={true}
        filterWord={debouncedSearch}
        onFilterChange={handleSearchChange}
        useSheet={true}
        sheetContent={<AddContact />}
        viewMode={viewMode}
        onViewModeChange={handleViewModeChange}
        showViewToggle={true}
        showFilters={false} // We'll use our custom filter component instead
      />

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="px-3 py-1">
            <Users className="h-3 w-3 mr-1" />
            <span>{filteredData.length} Contacts</span>
          </Badge>
        </div>

        <ContactFilterPopover onFilterChange={handleFilterChange} />
      </div>

      {showFilters && (
        <div className="mb-4">
          {/* Active filters will be displayed by the FilterPopover component */}
        </div>
      )}

      {viewMode === "list" ? (
        <ModernTable
          data={filteredData}
          isLoading={isLoading}
          isSelectable={true}
          showColumnSelection={true}
          onRowClick={(item) => console.log("Row clicked", item)}
          columns={[
            {
              header: "Contact",
              accessor: "email",
              className: "min-w-[250px]",
              cell: (value, item) => {
                const initials = getInitials(String(value));

                return (
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-9 w-9 bg-gradient-to-br from-primary/20 to-primary/10">
                      <AvatarFallback className="text-primary font-medium">
                        {initials}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium text-gray-900">
                        {String(value)}
                      </div>
                      <div className="text-xs text-gray-500">
                        ID: {String(item.id).substring(0, 8)}
                      </div>
                    </div>
                  </div>
                );
              },
              sortable: true,
            },
            {
              header: "Phone",
              accessor: "contact_number",
              className: "min-w-[180px]",
              cell: (value) => (
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-700">
                    {formatPhoneNumber(String(value))}
                  </span>
                </div>
              ),
            },
            {
              header: "List",
              accessor: "list_name",
              className: "min-w-[150px]",
              cell: (value) => {
                if (!value)
                  return <span className="text-gray-500 text-sm">No list</span>;

                return (
                  <Badge
                    variant={getListBadgeVariant(String(value))}
                    className="capitalize"
                  >
                    <Tag className="h-3 w-3 mr-1" />
                    {String(value)}
                  </Badge>
                );
              },
              sortable: true,
            },
            {
              header: "Status",
              accessor: "id",
              className: "min-w-[120px]",
              cell: (value) => {
                // This is just for demo - in a real app you'd have a proper status field
                const statuses = ["Active", "Inactive", "New"];
                const statusVariants = ["success", "secondary", "info"];
                const index = String(value).charCodeAt(0) % 3;

                return (
                  <div className="flex items-center justify-start">
                    <Badge
                    variant={statusVariants[index] as any}
                    className="capitalize"
                  >
                    {statuses[index]}
                  </Badge>
                  </div>

                );
              },
            },
          ]}
          actions={contactActions}
          emptyState={
            <div className="text-center p-8">
              <div className="h-16 w-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <UserPlus className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-lg font-medium mb-1">No contacts found</h3>
              <p className="text-gray-500 mb-4">
                Get started by adding your first contact
              </p>
              <button
                className="bg-primary px-4 py-2 rounded-md text-white flex items-center justify-center mx-auto"
                onClick={() => console.log("Add contact")}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Contact
              </button>
            </div>
          }
        />
      ) : (
        <ContactGrid
          data={filteredData}
          onItemClick={(item) => console.log("Grid item clicked", item)}
          actions={contactActions}
          getInitials={getInitials}
          formatPhoneNumber={formatPhoneNumber}
          getListBadgeVariant={getListBadgeVariant}
        />
      )}

      <div className="mt-8 border-t border-gray-200 pt-4">
        <CustomPagination
          currentPage={currentPage}
          totalPages={data.totalPages}
          onPageChange={handlePageChange}
        />
      </div>
    </Page>
  );
};

export default Contacts;
