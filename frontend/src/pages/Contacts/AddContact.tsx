// src/pages/Contacts/AddContact.tsx

import { useRef } from "react";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  SheetClose,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  UploadIcon,
  UserPlus,
  Mail,
  Phone,
  FileText,
  Tag,
  Users,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// Define the shape of form data
interface FormData {
  name: string;
  email: string;
  contactNumber: string;
  description?: string;
}

// Define the validation schema using Yup
const validationSchema = Yup.object().shape({
  name: Yup.string()
    .required("Name is required")
    .min(2, "Name must be at least 2 characters"),
  email: Yup.string().required("Email is required").email("Email is invalid"),
  contactNumber: Yup.string()
    .required("Contact Number is required")
    .matches(
      /^\+?[1-9]\d{1,14}$/,
      "Contact Number must include country code and be valid"
    ),
  description: Yup.string()
    .optional()
    .max(500, "Description cannot exceed 500 characters"),
});

export function AddContact() {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize React Hook Form
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormData>({
    resolver: yupResolver(validationSchema),
  });

  // Handle form submission
  const onSubmit = async (data: FormData) => {
    try {
      console.log("Form Data:", data);
      // TODO: Implement form submission logic (e.g., send to backend)

      // Optionally, handle the reset after successful submission
      reset();
      alert("Contact added successfully!");
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Failed to add contact. Please try again.");
    }
  };

  // Handle file import click
  const handleImportClick = () => {
    fileInputRef.current?.click();
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Handle file upload logic here
      console.log("Importing file:", file.name);
      // Reset the input value to allow re-uploading the same file if needed
      e.target.value = "";

      // TODO: Implement file parsing and data integration logic
    }
  };

  return (
    <>
      <SheetTitle className="text-xl font-semibold jakarta bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-1">
        Add New Contact
      </SheetTitle>
      <SheetDescription className="text-muted-foreground mb-6">
        Add a new contact to your directory for voice AI agents to reach out to.
      </SheetDescription>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <UserPlus className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Contact Information</h3>
          </div>

          {/* Name Field */}
          <div>
            <Label
              htmlFor="name"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Name<span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              placeholder="Enter contact name"
              className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
              {...register("name")}
            />
            {errors.name && (
              <p className="mt-1 text-xs text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Email Field */}
          <div>
            <Label
              htmlFor="email"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Email<span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="email"
                type="email"
                placeholder="Enter contact email"
                className="w-full pl-10 border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
                {...register("email")}
              />
            </div>
            {errors.email && (
              <p className="mt-1 text-xs text-red-600">
                {errors.email.message}
              </p>
            )}
          </div>

          {/* Contact Number Field */}
          <div>
            <Label
              htmlFor="contactNumber"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Contact Number<span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="contactNumber"
                type="tel"
                placeholder="Enter contact number with country code (e.g., +1234567890)"
                className="w-full pl-10 border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
                {...register("contactNumber")}
              />
            </div>
            {errors.contactNumber && (
              <p className="mt-1 text-xs text-red-600">
                {errors.contactNumber.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <Tag className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Additional Information</h3>
          </div>

          {/* Contact List Field */}
          <div>
            <Label
              htmlFor="list"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Contact List
            </Label>
            <Select>
              <SelectTrigger className="w-full border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20">
                <SelectValue placeholder="Select a contact list" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="leads">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-primary" />
                    <span>Sales Leads</span>
                  </div>
                </SelectItem>
                <SelectItem value="customers">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-chart-2" />
                    <span>Existing Customers</span>
                  </div>
                </SelectItem>
                <SelectItem value="prospects">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-chart-3" />
                    <span>Prospects</span>
                  </div>
                </SelectItem>
                <SelectItem value="partners">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-2 text-chart-4" />
                    <span>Partners</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Description Field */}
          <div>
            <Label
              htmlFor="description"
              className="text-sm font-medium text-foreground mb-1.5 block"
            >
              Notes
            </Label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Textarea
                id="description"
                placeholder="Add any additional notes about this contact..."
                className="w-full pl-10 min-h-[100px] border-border/50 bg-card focus:border-primary/50 focus:ring-primary/20"
                {...register("description")}
              />
            </div>
            {errors.description && (
              <p className="mt-1 text-xs text-red-600">
                {errors.description.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-2 mb-2">
            <UploadIcon className="h-5 w-5 text-primary" />
            <h3 className="text-sm font-medium">Bulk Import</h3>
          </div>

          {/* Import Section */}
          <div className="border-2 border-dashed border-border/50 rounded-lg p-6 text-center">
            <div className="flex flex-col items-center justify-center">
              <UploadIcon className="h-8 w-8 text-muted-foreground mb-2" />
              <h3 className="text-sm font-medium mb-1">Import Contacts</h3>
              <p className="text-xs text-muted-foreground mb-3">
                Drag and drop a CSV or Excel file here or click to browse
              </p>
              <Button
                type="button"
                variant="outline"
                className="text-xs border-border/50 hover:bg-muted/20"
                onClick={handleImportClick}
              >
                Browse Files
              </Button>
              <input
                type="file"
                id="import"
                accept=".xlsx, .csv"
                ref={fileInputRef}
                onChange={handleFileChange}
                className="hidden"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex justify-end space-x-3">
          <SheetClose asChild>
            <Button
              type="button"
              variant="outline"
              className="border-border/50 text-foreground hover:bg-muted/20"
            >
              Cancel
            </Button>
          </SheetClose>

          <Button
            type="submit"
            className="btn-gradient text-white"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Saving..." : "Add Contact"}
          </Button>
        </div>
      </form>
    </>
  );
}
