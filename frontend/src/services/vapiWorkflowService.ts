import axiosInstance from '../api/axios/axiosInstance';

// VAPI Workflow Types (Based on Official VAPI Documentation)
export interface VapiWorkflowNode {
  id: string;
  type: 'conversation' | 'apiRequest' | 'transferCall' | 'endCall' | 'tool';
  name: string;
  position: { x: number; y: number };
  
  // Conversation Node Properties
  firstMessage?: string;
  prompt?: string;
  model?: {
    provider: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
  };
  voice?: {
    provider: string;
    voiceId: string;
  };
  transcriber?: {
    provider: string;
    model?: string;
  };
  
  // Variable Extraction
  extractVariables?: Array<{
    name: string;
    type: 'string' | 'number' | 'boolean' | 'integer';
    description: string;
    enum?: string[];
  }>;
  
  // API Request Node Properties
  url?: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeoutSeconds?: number;
  
  // Transfer Call Node Properties
  destination?: string;
  transferPlan?: {
    message?: string;
    summary?: string;
  };
  
  // Tool Node Properties
  toolId?: string;
  toolName?: string;
  toolType?: string;
  
  // Global Node Properties
  isGlobal?: boolean;
  globalCondition?: string;
  
  // Start Node Properties
  isStart?: boolean;
}

export interface VapiWorkflowEdge {
  id: string;
  source: string;
  target: string;
  condition?: string;
  conditionType?: 'ai' | 'logical' | 'combined';
}

export interface VapiWorkflow {
  id: string;
  name: string;
  description?: string;
  nodes: VapiWorkflowNode[];
  edges: VapiWorkflowEdge[];
  variables?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  status: 'draft' | 'active' | 'archived';
}

export interface CreateVapiWorkflowRequest {
  name: string;
  description?: string;
  nodes?: VapiWorkflowNode[];
  edges?: VapiWorkflowEdge[];
}

export interface UpdateVapiWorkflowRequest {
  name?: string;
  description?: string;
  nodes?: VapiWorkflowNode[];
  edges?: VapiWorkflowEdge[];
  status?: 'draft' | 'active' | 'archived';
}

export interface VapiWorkflowExecution {
  id: string;
  workflowId: string;
  callId?: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  currentNodeId?: string;
  variables?: Record<string, any>;
  startedAt: string;
  completedAt?: string;
  error?: string;
}

// VAPI Workflow Service
class VapiWorkflowService {
  private baseUrl = '/api/vapi/workflows';

  // Create a new VAPI workflow
  async createWorkflow(data: CreateVapiWorkflowRequest): Promise<VapiWorkflow> {
    const response = await axiosInstance.post<VapiWorkflow>(this.baseUrl, data);
    return response.data;
  }

  // Get all VAPI workflows
  async getWorkflows(): Promise<VapiWorkflow[]> {
    const response = await axiosInstance.get<VapiWorkflow[]>(this.baseUrl);
    return response.data;
  }

  // Get a specific VAPI workflow by ID
  async getWorkflowById(id: string): Promise<VapiWorkflow> {
    const response = await axiosInstance.get<VapiWorkflow>(`${this.baseUrl}/${id}`);
    return response.data;
  }

  // Update a VAPI workflow
  async updateWorkflow(id: string, data: UpdateVapiWorkflowRequest): Promise<VapiWorkflow> {
    const response = await axiosInstance.put<VapiWorkflow>(`${this.baseUrl}/${id}`, data);
    return response.data;
  }

  // Delete a VAPI workflow
  async deleteWorkflow(id: string): Promise<void> {
    await axiosInstance.delete(`${this.baseUrl}/${id}`);
  }

  // Auto-save workflow (for real-time updates)
  async autoSaveWorkflow(id: string, data: Partial<UpdateVapiWorkflowRequest>): Promise<VapiWorkflow> {
    const response = await axiosInstance.patch<VapiWorkflow>(`${this.baseUrl}/${id}/auto-save`, data);
    return response.data;
  }

  // Execute/Test a VAPI workflow
  async executeWorkflow(id: string, phoneNumber?: string): Promise<VapiWorkflowExecution> {
    const response = await axiosInstance.post<VapiWorkflowExecution>(`${this.baseUrl}/${id}/execute`, {
      phoneNumber,
    });
    return response.data;
  }

  // Get workflow execution status
  async getExecutionStatus(executionId: string): Promise<VapiWorkflowExecution> {
    const response = await axiosInstance.get<VapiWorkflowExecution>(`${this.baseUrl}/executions/${executionId}`);
    return response.data;
  }

  // Stop workflow execution
  async stopExecution(executionId: string): Promise<void> {
    await axiosInstance.post(`${this.baseUrl}/executions/${executionId}/stop`);
  }

  // Validate workflow structure
  async validateWorkflow(id: string): Promise<{ isValid: boolean; errors: string[] }> {
    const response = await axiosInstance.post<{ isValid: boolean; errors: string[] }>(`${this.baseUrl}/${id}/validate`);
    return response.data;
  }

  // Export workflow as JSON
  async exportWorkflow(id: string): Promise<VapiWorkflow> {
    const response = await axiosInstance.get<VapiWorkflow>(`${this.baseUrl}/${id}/export`);
    return response.data;
  }

  // Import workflow from JSON
  async importWorkflow(workflowData: VapiWorkflow): Promise<VapiWorkflow> {
    const response = await axiosInstance.post<VapiWorkflow>(`${this.baseUrl}/import`, workflowData);
    return response.data;
  }

  // Clone/Duplicate workflow
  async cloneWorkflow(id: string, name: string): Promise<VapiWorkflow> {
    const response = await axiosInstance.post<VapiWorkflow>(`${this.baseUrl}/${id}/clone`, { name });
    return response.data;
  }

  // Get workflow analytics
  async getWorkflowAnalytics(id: string, dateRange?: { start: string; end: string }) {
    const params = dateRange ? { start: dateRange.start, end: dateRange.end } : {};
    const response = await axiosInstance.get(`${this.baseUrl}/${id}/analytics`, { params });
    return response.data;
  }
}

export const vapiWorkflowService = new VapiWorkflowService();

// Export individual functions for easier imports
export const {
  createWorkflow,
  getWorkflows,
  getWorkflowById,
  updateWorkflow,
  deleteWorkflow,
  autoSaveWorkflow,
  executeWorkflow,
  getExecutionStatus,
  stopExecution,
  validateWorkflow,
  exportWorkflow,
  importWorkflow,
  cloneWorkflow,
  getWorkflowAnalytics,
} = vapiWorkflowService;
