@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Raleway:ital,wght@0,100..900;1,100..900&family=Rubik:ital,wght@0,300..900;1,300..900&family=Plus+Jakarta+Sans:wght@200..800&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern UI theme inspired by UIFlexer */
    --background: 220 33% 98%;
    --foreground: 224 71% 4%;
    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;
    --primary: 262 83% 58%;
    --primary-foreground: 210 40% 98%;
    --secondary: 214 32% 91%;
    --secondary-foreground: 222 47% 11%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 25% 40%;
    --accent: 262 83% 58%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;
    /* Chart colors - vibrant palette */
    --chart-1: 262 83% 58%;
    --chart-2: 196 80% 45%;
    --chart-3: 335 65% 55%;
    --chart-4: 150 60% 45%;
    --chart-5: 30 95% 60%;
  }
}

body {
  font-family: "Plus Jakarta Sans", "Inter", sans-serif;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  background-image: radial-gradient(
      circle at 20% 35%,
      rgba(147, 51, 234, 0.03) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 44%,
      rgba(59, 130, 246, 0.03) 0%,
      transparent 50%
    ),
    linear-gradient(rgba(255, 255, 255, 0.01) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.01) 1px, transparent 1px);
  background-size: 100% 100%, 100% 100%, 20px 20px, 20px 20px;
  background-position: 0 0, 0 0, -1px -1px, -1px -1px;
  background-attachment: fixed;
}

/* Card styling with subtle gradient */
.card {
  background: linear-gradient(145deg, hsl(var(--card)), hsl(var(--card)));
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, hsl(var(--primary)), hsl(var(--chart-3)));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}

/* Button styling with gradient */
.btn-gradient {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)),
    hsl(var(--primary) / 0.8)
  );
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--primary), 0.25);
}

.jakarta {
  font-family: "Plus Jakarta Sans", sans-serif;
}

.rubik {
  font-family: "Rubik", sans-serif;
}

.inter {
  font-family: "Inter", sans-serif;
}

.noto-sans {
  font-family: "Noto Sans", sans-serif;
}

/* Hide scrollbar but keep functionality */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}
