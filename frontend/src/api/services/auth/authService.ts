// src/api/services/auth/authService.ts

import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

interface LoginResponse {
  message: string;
  data: string;
}

export const loginService = async (data: {
  email: string;
  password: string;
}): Promise<string> => {
  try {
    const response = await axiosInstance.post<LoginResponse>(
      "/api/users/login",
      data
    );

    if (response.status === 200) {
      toast.success(response.data.message);
      // Assuming the token is in response.data.data.token
      return response.data.data;
    }

    throw new Error("Login failed");
  } catch (error: any) {
    if (error.response && error.response.data && error.response.data.message) {
      toast.error(error.response.data.message);
    } else {
      toast.error("An unexpected error occurred.");
    }
    throw error;
  }
};
