import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface Recording {
  id: string;
  recordingName: string;
  duration: string;
  date: string;
  type: string;
  status: "processing" | "completed" | "failed";
  fileSize: string;
  format: string;
  transcript: string;
  callId?: string;
  assistantId?: string;
  phoneNumber?: string;
  cost: number;
  metadata: {
    quality: string;
    sampleRate: number;
    channels: number;
    originalName?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface RecordingFilters {
  search?: string;
  type?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  assistantId?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface RecordingListResponse {
  recordings: Recording[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalRecordings: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  filters: RecordingFilters;
}

export interface RecordingAnalytics {
  recordingId: string;
  duration: string;
  fileSize: string;
  cost: number;
  quality: string;
  transcriptWordCount: number;
  playCount: number;
  downloadCount: number;
  shareCount: number;
  lastPlayed: string;
  averageListenDuration: string;
  completionRate: string;
}

export interface ShareRecordingData {
  email?: string;
  message?: string;
  expiresIn?: "1d" | "7d" | "30d";
}

export interface ShareRecordingResponse {
  shareLink: string;
  shareToken: string;
  expiresAt: string;
  sharedWith: string;
  message?: string;
}

// Get all recordings
export async function getAllRecordings(
  page: number = 1,
  limit: number = 10,
  filters: RecordingFilters = {}
) {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
      ...Object.fromEntries(
        Object.entries(filters).filter(([_, value]) => value !== undefined && value !== "")
      ),
    });

    const res = await axiosInstance.get(`/api/recordings?${params.toString()}`);
    return res.data.data as RecordingListResponse;
  } catch (error: any) {
    console.error("Error fetching recordings:", error);
    const message = error.response?.data?.message || "Failed to fetch recordings";
    toast.error(message);
    throw error;
  }
}

// Get recording by ID
export async function getRecordingById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/recordings/${id}`);
    return res.data.data as Recording;
  } catch (error: any) {
    console.error("Error fetching recording:", error);
    const message = error.response?.data?.message || "Failed to fetch recording";
    toast.error(message);
    throw error;
  }
}

// Upload recording
export async function uploadRecording(
  file: File,
  recordingData: {
    recordingName: string;
    type?: string;
    assistantId?: string;
    callId?: string;
    phoneNumber?: string;
  }
) {
  try {
    const formData = new FormData();
    formData.append("audio", file);
    formData.append("recordingName", recordingData.recordingName);
    
    if (recordingData.type) formData.append("type", recordingData.type);
    if (recordingData.assistantId) formData.append("assistantId", recordingData.assistantId);
    if (recordingData.callId) formData.append("callId", recordingData.callId);
    if (recordingData.phoneNumber) formData.append("phoneNumber", recordingData.phoneNumber);

    const res = await axiosInstance.post("/api/recordings/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    toast.success("Recording uploaded successfully");
    return res.data.data as Recording;
  } catch (error: any) {
    console.error("Error uploading recording:", error);
    const message = error.response?.data?.message || "Failed to upload recording";
    toast.error(message);
    throw error;
  }
}

// Update recording
export async function updateRecording(id: string, updateData: Partial<Recording>) {
  try {
    const res = await axiosInstance.patch(`/api/recordings/${id}`, updateData);
    toast.success("Recording updated successfully");
    return res.data.data as Recording;
  } catch (error: any) {
    console.error("Error updating recording:", error);
    const message = error.response?.data?.message || "Failed to update recording";
    toast.error(message);
    throw error;
  }
}

// Delete recording
export async function deleteRecording(id: string) {
  try {
    await axiosInstance.delete(`/api/recordings/${id}`);
    toast.success("Recording deleted successfully");
  } catch (error: any) {
    console.error("Error deleting recording:", error);
    const message = error.response?.data?.message || "Failed to delete recording";
    toast.error(message);
    throw error;
  }
}

// Get recording stream URL
export async function getRecordingStreamUrl(id: string) {
  try {
    const res = await axiosInstance.get(`/api/recordings/${id}/stream`);
    return res.data.data;
  } catch (error: any) {
    console.error("Error getting stream URL:", error);
    const message = error.response?.data?.message || "Failed to get stream URL";
    toast.error(message);
    throw error;
  }
}

// Download recording
export async function downloadRecording(id: string, filename?: string) {
  try {
    const res = await axiosInstance.get(`/api/recordings/${id}/download`, {
      responseType: "blob",
    });
    
    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename || `recording-${id}.mp3`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    
    toast.success("Recording downloaded successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error downloading recording:", error);
    const message = error.response?.data?.message || "Failed to download recording";
    toast.error(message);
    throw error;
  }
}

// Get recording transcript
export async function getRecordingTranscript(id: string) {
  try {
    const res = await axiosInstance.get(`/api/recordings/${id}/transcript`);
    return res.data.data;
  } catch (error: any) {
    console.error("Error fetching transcript:", error);
    const message = error.response?.data?.message || "Failed to fetch transcript";
    toast.error(message);
    throw error;
  }
}

// Update recording transcript
export async function updateRecordingTranscript(id: string, transcript: string) {
  try {
    const res = await axiosInstance.patch(`/api/recordings/${id}/transcript`, {
      transcript,
    });
    toast.success("Transcript updated successfully");
    return res.data.data;
  } catch (error: any) {
    console.error("Error updating transcript:", error);
    const message = error.response?.data?.message || "Failed to update transcript";
    toast.error(message);
    throw error;
  }
}

// Get recording analytics
export async function getRecordingAnalytics(id: string) {
  try {
    const res = await axiosInstance.get(`/api/recordings/${id}/analytics`);
    return res.data.data as RecordingAnalytics;
  } catch (error: any) {
    console.error("Error fetching recording analytics:", error);
    const message = error.response?.data?.message || "Failed to fetch analytics";
    toast.error(message);
    throw error;
  }
}

// Share recording
export async function shareRecording(id: string, shareData: ShareRecordingData) {
  try {
    const res = await axiosInstance.post(`/api/recordings/${id}/share`, shareData);
    toast.success("Recording shared successfully");
    return res.data.data as ShareRecordingResponse;
  } catch (error: any) {
    console.error("Error sharing recording:", error);
    const message = error.response?.data?.message || "Failed to share recording";
    toast.error(message);
    throw error;
  }
}

// Get recording metadata
export async function getRecordingMetadata(id: string) {
  try {
    const res = await axiosInstance.get(`/api/recordings/${id}/metadata`);
    return res.data.data;
  } catch (error: any) {
    console.error("Error fetching recording metadata:", error);
    const message = error.response?.data?.message || "Failed to fetch metadata";
    toast.error(message);
    throw error;
  }
}
