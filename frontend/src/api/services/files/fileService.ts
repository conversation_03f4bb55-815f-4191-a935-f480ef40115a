import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface FileItem {
  id: string;
  name: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  purpose: "assistant" | "knowledge-base" | "training" | "other";
  status: "uploading" | "processing" | "ready" | "error";
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface FileUploadConfig {
  purpose: "assistant" | "knowledge-base" | "training" | "other";
  metadata?: Record<string, any>;
}

export interface FileStats {
  totalFiles: number;
  totalSize: number;
  byPurpose: {
    purpose: string;
    count: number;
    size: number;
  }[];
  byMimeType: {
    mimeType: string;
    count: number;
    size: number;
  }[];
}

// Get all files
export async function getAllFiles(page?: number, search?: string, purpose?: string) {
  try {
    const res = await axiosInstance.get("/api/files", {
      params: { page, search, purpose },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching files:", error);
    toast.error("Failed to fetch files");
    throw error;
  }
}

// Get file by ID
export async function getFileById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/files/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching file:", error);
    toast.error("Failed to fetch file");
    throw error;
  }
}

// Upload file
export async function uploadFile(file: File, config: FileUploadConfig) {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("purpose", config.purpose);
    if (config.metadata) {
      formData.append("metadata", JSON.stringify(config.metadata));
    }

    const res = await axiosInstance.post("/api/files", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          // You can emit this progress to a global state or callback
          console.log(`Upload progress: ${percentCompleted}%`);
        }
      },
    });
    toast.success("File uploaded successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error uploading file:", error);
    const message = error.response?.data?.message || "Failed to upload file";
    toast.error(message);
    throw error;
  }
}

// Upload multiple files
export async function uploadMultipleFiles(files: File[], config: FileUploadConfig) {
  try {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });
    formData.append("purpose", config.purpose);
    if (config.metadata) {
      formData.append("metadata", JSON.stringify(config.metadata));
    }

    const res = await axiosInstance.post("/api/files/bulk", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    toast.success(`${files.length} files uploaded successfully`);
    return res.data;
  } catch (error: any) {
    console.error("Error uploading files:", error);
    const message = error.response?.data?.message || "Failed to upload files";
    toast.error(message);
    throw error;
  }
}

// Update file metadata
export async function updateFile(id: string, data: { name?: string; metadata?: Record<string, any> }) {
  try {
    const res = await axiosInstance.patch(`/api/files/${id}`, data);
    toast.success("File updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating file:", error);
    const message = error.response?.data?.message || "Failed to update file";
    toast.error(message);
    throw error;
  }
}

// Delete file
export async function deleteFile(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/files/${id}`);
    toast.success("File deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting file:", error);
    const message = error.response?.data?.message || "Failed to delete file";
    toast.error(message);
    throw error;
  }
}

// Get file statistics
export async function getFileStats() {
  try {
    const res = await axiosInstance.get("/api/files/stats");
    return res.data;
  } catch (error) {
    console.error("Error fetching file stats:", error);
    toast.error("Failed to fetch file statistics");
    throw error;
  }
}

// Download file
export async function downloadFile(id: string, filename?: string) {
  try {
    const res = await axiosInstance.get(`/api/files/${id}/download`, {
      responseType: "blob",
    });
    
    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", filename || `file-${id}`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    
    toast.success("File downloaded successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error downloading file:", error);
    const message = error.response?.data?.message || "Failed to download file";
    toast.error(message);
    throw error;
  }
}
