import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface AssistantConfig {
  name: string;
  firstMessage: string;
  transcriber: {
    provider: string;
    model: string;
  };
  model: {
    provider: string;
    model: string;
    temperature?: number;
  };
  voice: {
    provider: string;
    voiceId: string;
  };
  systemMessage?: string;
  functions?: any[];
  endCallMessage?: string;
  endCallPhrases?: string[];
  metadata?: Record<string, any>;
}

export interface Assistant {
  id: string;
  name: string;
  firstMessage: string;
  transcriber: any;
  model: any;
  voice: any;
  systemMessage?: string;
  functions?: any[];
  endCallMessage?: string;
  endCallPhrases?: string[];
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Get all assistants with pagination
export async function fetchAllAgents(page: number, search: string) {
  try {
    const res = await axiosInstance.get("/api/assistant/get_all_assistants", {
      params: { page, search },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching agents:", error);
    toast.error("Failed to fetch agents");
    throw error;
  }
}

// Get all assistants without pagination (fetch all pages)
export async function fetchAllAgentsComplete(search: string = "") {
  try {
    let allAssistants: Assistant[] = [];
    let currentPage = 1;
    let totalPages = 1;

    do {
      const res = await axiosInstance.get("/api/assistant/get_all_assistants", {
        params: { page: currentPage, search, limit: 50 }, // Increase limit for efficiency
      });

      const { data, totalPages: total } = res.data;
      allAssistants = [...allAssistants, ...data];
      totalPages = total;
      currentPage++;
    } while (currentPage <= totalPages);

    return { data: allAssistants, total: allAssistants.length };
  } catch (error) {
    console.error("Error fetching all agents:", error);
    toast.error("Failed to fetch all agents");
    throw error;
  }
}

// Get all assistants from VAPI directly
export async function fetchAllAgentsFromVapi() {
  try {
    const res = await axiosInstance.get("/api/assistant/list-all-assistants-from-vapi");
    return res.data;
  } catch (error) {
    console.error("Error fetching agents from VAPI:", error);
    toast.error("Failed to fetch agents from VAPI");
    throw error;
  }
}

// Get assistant by ID
export async function getAssistantById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/assistant/get-assistants/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching assistant:", error);
    toast.error("Failed to fetch assistant");
    throw error;
  }
}

// Create new assistant
export async function createAssistant(data: AssistantConfig) {
  try {
    const res = await axiosInstance.post("/api/assistant/create_assistant", data);
    toast.success("Assistant created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating assistant:", error);
    const message = error.response?.data?.message || "Failed to create assistant";
    toast.error(message);
    throw error;
  }
}

// Update assistant
export async function updateAssistant(id: string, data: Partial<AssistantConfig>) {
  try {
    const res = await axiosInstance.put(`/api/assistant/update_assistant/${id}`, data);
    toast.success("Assistant updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating assistant:", error);
    const message = error.response?.data?.message || "Failed to update assistant";
    toast.error(message);
    throw error;
  }
}

// Delete assistant
export async function deleteAssistant(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/assistant/delete_assistant/${id}`);
    toast.success("Assistant deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting assistant:", error);
    const message = error.response?.data?.message || "Failed to delete assistant";
    toast.error(message);
    throw error;
  }
}
