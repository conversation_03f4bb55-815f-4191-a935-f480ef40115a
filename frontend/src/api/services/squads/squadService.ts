import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface Squad {
  id: string;
  name: string;
  description?: string;
  members: SquadMember[];
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
}

export interface SquadMember {
  id: string;
  assistantId: string;
  assistantName: string;
  role: "leader" | "member" | "specialist";
  priority: number;
  conditions?: {
    keywords?: string[];
    sentiment?: "positive" | "negative" | "neutral";
    language?: string;
    timeOfDay?: {
      start: string;
      end: string;
    };
  };
  transferSettings?: {
    enabled: boolean;
    timeout?: number;
    maxTransfers?: number;
  };
}

export interface SquadConfig {
  name: string;
  description?: string;
  members: Array<{
    assistantId: string;
    role: "leader" | "member" | "specialist";
    priority: number;
    conditions?: {
      keywords?: string[];
      sentiment?: "positive" | "negative" | "neutral";
      language?: string;
      timeOfDay?: {
        start: string;
        end: string;
      };
    };
    transferSettings?: {
      enabled: boolean;
      timeout?: number;
      maxTransfers?: number;
    };
  }>;
}

export interface SquadStats {
  totalSquads: number;
  activeSquads: number;
  totalMembers: number;
  averageMembersPerSquad: number;
  transferStats: {
    totalTransfers: number;
    successfulTransfers: number;
    averageTransferTime: number;
  };
}

// Get all squads
export async function getAllSquads(page?: number, search?: string, status?: string) {
  try {
    const res = await axiosInstance.get("/api/squads", {
      params: { page, search, status },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching squads:", error);
    toast.error("Failed to fetch squads");
    throw error;
  }
}

// Get squad by ID
export async function getSquadById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/squads/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching squad:", error);
    toast.error("Failed to fetch squad");
    throw error;
  }
}

// Create squad
export async function createSquad(data: SquadConfig) {
  try {
    const res = await axiosInstance.post("/api/squads", data);
    toast.success("Squad created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating squad:", error);
    const message = error.response?.data?.message || "Failed to create squad";
    toast.error(message);
    throw error;
  }
}

// Update squad
export async function updateSquad(id: string, data: Partial<SquadConfig>) {
  try {
    const res = await axiosInstance.patch(`/api/squads/${id}`, data);
    toast.success("Squad updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating squad:", error);
    const message = error.response?.data?.message || "Failed to update squad";
    toast.error(message);
    throw error;
  }
}

// Delete squad
export async function deleteSquad(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/squads/${id}`);
    toast.success("Squad deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting squad:", error);
    const message = error.response?.data?.message || "Failed to delete squad";
    toast.error(message);
    throw error;
  }
}

// Add member to squad
export async function addMemberToSquad(id: string, member: {
  assistantId: string;
  role: "leader" | "member" | "specialist";
  priority: number;
  conditions?: {
    keywords?: string[];
    sentiment?: "positive" | "negative" | "neutral";
    language?: string;
    timeOfDay?: {
      start: string;
      end: string;
    };
  };
  transferSettings?: {
    enabled: boolean;
    timeout?: number;
    maxTransfers?: number;
  };
}) {
  try {
    const res = await axiosInstance.post(`/api/squads/${id}/members`, member);
    toast.success("Member added to squad successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error adding member to squad:", error);
    const message = error.response?.data?.message || "Failed to add member to squad";
    toast.error(message);
    throw error;
  }
}

// Remove member from squad
export async function removeMemberFromSquad(id: string, memberId: string) {
  try {
    const res = await axiosInstance.delete(`/api/squads/${id}/members/${memberId}`);
    toast.success("Member removed from squad successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error removing member from squad:", error);
    const message = error.response?.data?.message || "Failed to remove member from squad";
    toast.error(message);
    throw error;
  }
}

// Update squad member
export async function updateSquadMember(id: string, memberId: string, data: Partial<SquadMember>) {
  try {
    const res = await axiosInstance.patch(`/api/squads/${id}/members/${memberId}`, data);
    toast.success("Squad member updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating squad member:", error);
    const message = error.response?.data?.message || "Failed to update squad member";
    toast.error(message);
    throw error;
  }
}

// Get squad statistics
export async function getSquadStats() {
  try {
    const res = await axiosInstance.get("/api/squads/stats");
    return res.data;
  } catch (error) {
    console.error("Error fetching squad stats:", error);
    toast.error("Failed to fetch squad statistics");
    throw error;
  }
}
