import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface OutboundCall {
  id: string;
  assistantId: string;
  phoneNumber: string;
  status: "queued" | "ringing" | "in-progress" | "forwarding" | "ended";
  startedAt?: string;
  endedAt?: string;
  duration?: number;
  cost?: number;
  endedReason?: string;
  transcript?: string;
  recording?: string;
  summary?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface OutboundCallConfig {
  assistantId: string;
  phoneNumber: string;
  metadata?: Record<string, any>;
  maxDurationSeconds?: number;
  firstMessage?: string;
  voicemailMessage?: string;
}

export interface CallStats {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageDuration: number;
  totalCost: number;
  callsByStatus: {
    status: string;
    count: number;
  }[];
  callsByHour: {
    hour: number;
    count: number;
  }[];
  callsByDay: {
    date: string;
    count: number;
  }[];
}

// Create outbound call
export async function createOutboundCall(data: OutboundCallConfig) {
  try {
    const res = await axiosInstance.post("/api/outboundcall/createCall", {
      id: data.assistantId,
      phoneNumber: data.phoneNumber,
      metadata: data.metadata,
      maxDurationSeconds: data.maxDurationSeconds,
      firstMessage: data.firstMessage,
      voicemailMessage: data.voicemailMessage,
    });
    toast.success("Outbound call initiated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating outbound call:", error);
    const message = error.response?.data?.message || "Failed to create outbound call";
    toast.error(message);
    throw error;
  }
}

// Get all outbound calls
export async function getAllOutboundCalls(page?: number, search?: string, status?: string) {
  try {
    const res = await axiosInstance.get("/api/outboundcall/getAllCalls", {
      params: { page, search, status },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching outbound calls:", error);
    toast.error("Failed to fetch outbound calls");
    throw error;
  }
}

// Get outbound call by ID
export async function getOutboundCallById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/outboundcall/getCallsbyID/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching outbound call:", error);
    toast.error("Failed to fetch outbound call");
    throw error;
  }
}

// End outbound call
export async function endOutboundCall(id: string) {
  try {
    const res = await axiosInstance.post(`/api/outboundcall/endCall/${id}`);
    toast.success("Call ended successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error ending outbound call:", error);
    const message = error.response?.data?.message || "Failed to end call";
    toast.error(message);
    throw error;
  }
}

// Get call transcript
export async function getCallTranscript(id: string) {
  try {
    const res = await axiosInstance.get(`/api/outboundcall/transcript/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching call transcript:", error);
    toast.error("Failed to fetch call transcript");
    throw error;
  }
}

// Get call recording
export async function getCallRecording(id: string) {
  try {
    const res = await axiosInstance.get(`/api/outboundcall/recording/${id}`);
    return res.data;
  } catch (error: any) {
    console.error("Error fetching call recording:", error);
    const message = error.response?.data?.message || "Failed to fetch recording";
    toast.error(message);
    throw error;
  }
}

// Download call recording
export async function downloadCallRecording(id: string) {
  try {
    const res = await axiosInstance.get(`/api/outboundcall/recording/${id}`, {
      responseType: "blob",
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `call-recording-${id}.mp3`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    toast.success("Recording downloaded successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error downloading call recording:", error);
    const message = error.response?.data?.message || "Failed to download recording";
    toast.error(message);
    throw error;
  }
}

// Get call statistics
export async function getCallStats(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/outboundcall/stats", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching call stats:", error);
    toast.error("Failed to fetch call statistics");
    throw error;
  }
}

// Bulk create outbound calls
export async function createBulkOutboundCalls(data: {
  assistantId: string;
  phoneNumbers: string[];
  metadata?: Record<string, any>;
  maxDurationSeconds?: number;
  firstMessage?: string;
  voicemailMessage?: string;
}) {
  try {
    const res = await axiosInstance.post("/api/outboundcall/createBulkCalls", data);
    toast.success(`${data.phoneNumbers.length} outbound calls initiated successfully`);
    return res.data;
  } catch (error: any) {
    console.error("Error creating bulk outbound calls:", error);
    const message = error.response?.data?.message || "Failed to create bulk outbound calls";
    toast.error(message);
    throw error;
  }
}

// Schedule outbound call
export async function scheduleOutboundCall(data: OutboundCallConfig & {
  scheduledAt: string;
}) {
  try {
    const res = await axiosInstance.post("/api/outboundcall/schedule", data);
    toast.success("Outbound call scheduled successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error scheduling outbound call:", error);
    const message = error.response?.data?.message || "Failed to schedule outbound call";
    toast.error(message);
    throw error;
  }
}
