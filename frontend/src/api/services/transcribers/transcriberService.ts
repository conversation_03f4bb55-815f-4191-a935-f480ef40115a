import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface TranscriberProvider {
  name: string;
  models: TranscriberModel[];
}

export interface TranscriberModel {
  id: string;
  name: string;
  provider: string;
  language?: string;
  accuracy?: string;
  latency?: string;
  description?: string;
}

export interface TranscriberConfig {
  provider: string;
  model: string;
  language?: string;
  keywords?: string[];
  endpointing?: number;
  punctuate?: boolean;
  includesPunctuationInTheTranscript?: boolean;
  numChannels?: number;
  smartFormat?: boolean;
}

export interface TranscriberStats {
  totalTranscriptions: number;
  totalMinutes: number;
  averageAccuracy: number;
  averageLatency: number;
  errorRate: number;
  costBreakdown: {
    provider: string;
    cost: number;
  }[];
}

// Get all available transcribers
export async function getAvailableTranscribers() {
  try {
    const res = await axiosInstance.get("/api/transcribers/available");
    return res.data;
  } catch (error) {
    console.error("Error fetching available transcribers:", error);
    toast.error("Failed to fetch available transcribers");
    throw error;
  }
}

// Create transcriber configuration
export async function createTranscriberConfig(data: TranscriberConfig) {
  try {
    const res = await axiosInstance.post("/api/transcribers/config", data);
    toast.success("Transcriber configuration created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating transcriber config:", error);
    const message = error.response?.data?.message || "Failed to create transcriber configuration";
    toast.error(message);
    throw error;
  }
}

// Test transcriber
export async function testTranscriber(data: {
  provider: string;
  model: string;
  audioFile?: File;
  audioUrl?: string;
  language?: string;
}) {
  try {
    const formData = new FormData();
    formData.append("provider", data.provider);
    formData.append("model", data.model);
    if (data.language) {
      formData.append("language", data.language);
    }
    if (data.audioFile) {
      formData.append("audioFile", data.audioFile);
    }
    if (data.audioUrl) {
      formData.append("audioUrl", data.audioUrl);
    }

    const res = await axiosInstance.post("/api/transcribers/test", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    toast.success("Transcriber test completed successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error testing transcriber:", error);
    const message = error.response?.data?.message || "Failed to test transcriber";
    toast.error(message);
    throw error;
  }
}

// Get transcriber usage statistics
export async function getTranscriberStats() {
  try {
    const res = await axiosInstance.get("/api/transcribers/stats");
    return res.data;
  } catch (error) {
    console.error("Error fetching transcriber stats:", error);
    toast.error("Failed to fetch transcriber statistics");
    throw error;
  }
}

// Get transcribers by provider
export async function getTranscribersByProvider(provider: string) {
  try {
    const res = await axiosInstance.get(`/api/transcribers/provider/${provider}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching transcribers by provider:", error);
    toast.error("Failed to fetch transcribers");
    throw error;
  }
}

// Get supported languages for a transcriber
export async function getSupportedLanguages(provider: string, model: string) {
  try {
    const res = await axiosInstance.get(`/api/transcribers/${provider}/${model}/languages`);
    return res.data;
  } catch (error) {
    console.error("Error fetching supported languages:", error);
    toast.error("Failed to fetch supported languages");
    throw error;
  }
}
