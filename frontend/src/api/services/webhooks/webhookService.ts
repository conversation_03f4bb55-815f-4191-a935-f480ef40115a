import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  secret?: string;
  enabled: boolean;
  retryConfig: {
    maxRetries: number;
    retryDelay: number;
    backoffMultiplier: number;
  };
  headers?: Record<string, string>;
  timeout: number;
  lastTriggered?: string;
  successCount: number;
  failureCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface WebhookConfig {
  name: string;
  url: string;
  events: string[];
  secret?: string;
  enabled?: boolean;
  retryConfig?: {
    maxRetries: number;
    retryDelay: number;
    backoffMultiplier: number;
  };
  headers?: Record<string, string>;
  timeout?: number;
}

export interface WebhookEvent {
  id: string;
  webhookId: string;
  event: string;
  payload: any;
  status: "pending" | "delivered" | "failed" | "retrying";
  attempts: number;
  lastAttempt?: string;
  nextRetry?: string;
  response?: {
    status: number;
    body: string;
    headers: Record<string, string>;
  };
  error?: string;
  createdAt: string;
}

export interface WebhookStats {
  totalWebhooks: number;
  activeWebhooks: number;
  totalEvents: number;
  successfulDeliveries: number;
  failedDeliveries: number;
  averageResponseTime: number;
  eventsByType: Array<{
    event: string;
    count: number;
  }>;
  deliveriesByDay: Array<{
    date: string;
    successful: number;
    failed: number;
  }>;
}

// Server webhook events
export const SERVER_WEBHOOK_EVENTS = [
  "conversation-update",
  "function-call", 
  "hang",
  "speech-update",
  "status-update",
  "transcript",
  "tool-calls",
  "transfer-destination-request",
  "user-interrupted",
  "end-of-call-report"
];

// Client webhook events  
export const CLIENT_WEBHOOK_EVENTS = [
  "conversation-update",
  "function-call",
  "hang", 
  "model-output",
  "speech-update",
  "status-update",
  "transfer-update",
  "transcript",
  "tool-calls",
  "user-interrupted",
  "voice-input"
];

// Get webhook configuration
export async function getWebhookConfig() {
  try {
    const res = await axiosInstance.get("/api/webhooks/config");
    return res.data;
  } catch (error) {
    console.error("Error fetching webhook config:", error);
    toast.error("Failed to fetch webhook configuration");
    throw error;
  }
}

// Update webhook configuration
export async function updateWebhookConfig(data: {
  serverUrl?: string;
  clientUrl?: string;
  secret?: string;
  events?: string[];
}) {
  try {
    const res = await axiosInstance.post("/api/webhooks/config", data);
    toast.success("Webhook configuration updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating webhook config:", error);
    const message = error.response?.data?.message || "Failed to update webhook configuration";
    toast.error(message);
    throw error;
  }
}

// Test webhook
export async function testWebhook(data: {
  url: string;
  event: string;
  payload?: any;
  headers?: Record<string, string>;
}) {
  try {
    const res = await axiosInstance.post("/api/webhooks/test", data);
    toast.success("Webhook test completed successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error testing webhook:", error);
    const message = error.response?.data?.message || "Failed to test webhook";
    toast.error(message);
    throw error;
  }
}

// Get all webhooks
export async function getAllWebhooks(page?: number, search?: string) {
  try {
    const res = await axiosInstance.get("/api/webhooks", {
      params: { page, search },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching webhooks:", error);
    toast.error("Failed to fetch webhooks");
    throw error;
  }
}

// Get webhook by ID
export async function getWebhookById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/webhooks/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching webhook:", error);
    toast.error("Failed to fetch webhook");
    throw error;
  }
}

// Create webhook
export async function createWebhook(data: WebhookConfig) {
  try {
    const res = await axiosInstance.post("/api/webhooks", data);
    toast.success("Webhook created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating webhook:", error);
    const message = error.response?.data?.message || "Failed to create webhook";
    toast.error(message);
    throw error;
  }
}

// Update webhook
export async function updateWebhook(id: string, data: Partial<WebhookConfig>) {
  try {
    const res = await axiosInstance.patch(`/api/webhooks/${id}`, data);
    toast.success("Webhook updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating webhook:", error);
    const message = error.response?.data?.message || "Failed to update webhook";
    toast.error(message);
    throw error;
  }
}

// Delete webhook
export async function deleteWebhook(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/webhooks/${id}`);
    toast.success("Webhook deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting webhook:", error);
    const message = error.response?.data?.message || "Failed to delete webhook";
    toast.error(message);
    throw error;
  }
}

// Get webhook events
export async function getWebhookEvents(webhookId: string, page?: number, status?: string) {
  try {
    const res = await axiosInstance.get(`/api/webhooks/${webhookId}/events`, {
      params: { page, status },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching webhook events:", error);
    toast.error("Failed to fetch webhook events");
    throw error;
  }
}

// Retry webhook event
export async function retryWebhookEvent(eventId: string) {
  try {
    const res = await axiosInstance.post(`/api/webhooks/events/${eventId}/retry`);
    toast.success("Webhook event retry initiated");
    return res.data;
  } catch (error: any) {
    console.error("Error retrying webhook event:", error);
    const message = error.response?.data?.message || "Failed to retry webhook event";
    toast.error(message);
    throw error;
  }
}

// Get webhook statistics
export async function getWebhookStats(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/webhooks/stats", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching webhook stats:", error);
    toast.error("Failed to fetch webhook statistics");
    throw error;
  }
}
