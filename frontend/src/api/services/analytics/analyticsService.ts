import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface AnalyticsQuery {
  table: string;
  columns: string[];
  filters?: Array<{
    column: string;
    operator: "=" | "!=" | ">" | "<" | ">=" | "<=" | "LIKE" | "IN" | "NOT IN";
    value: any;
  }>;
  groupBy?: string[];
  orderBy?: Array<{
    column: string;
    direction: "ASC" | "DESC";
  }>;
  limit?: number;
  offset?: number;
  dateRange?: {
    start: string;
    end: string;
    column?: string;
  };
}

export interface AnalyticsResult {
  data: any[];
  total: number;
  columns: Array<{
    name: string;
    type: string;
  }>;
  executionTime: number;
}

export interface CallAnalytics {
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  averageDuration: number;
  totalDuration: number;
  callsByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  callsByHour: Array<{
    hour: number;
    count: number;
  }>;
  callsByDay: Array<{
    date: string;
    count: number;
    duration: number;
  }>;
  callsByAssistant: Array<{
    assistantId: string;
    assistantName: string;
    count: number;
    averageDuration: number;
  }>;
}

export interface CostAnalytics {
  totalCost: number;
  costByProvider: Array<{
    provider: string;
    cost: number;
    percentage: number;
  }>;
  costByService: Array<{
    service: string;
    cost: number;
    percentage: number;
  }>;
  costByDay: Array<{
    date: string;
    cost: number;
  }>;
  costByAssistant: Array<{
    assistantId: string;
    assistantName: string;
    cost: number;
  }>;
  projectedMonthlyCost: number;
}

export interface PerformanceAnalytics {
  averageResponseTime: number;
  averageLatency: number;
  errorRate: number;
  uptime: number;
  throughput: number;
  performanceByEndpoint: Array<{
    endpoint: string;
    averageResponseTime: number;
    requestCount: number;
    errorRate: number;
  }>;
  performanceByDay: Array<{
    date: string;
    averageResponseTime: number;
    errorRate: number;
    requestCount: number;
  }>;
}

export interface UsageAnalytics {
  totalRequests: number;
  totalTokens: number;
  totalMinutes: number;
  usageByService: Array<{
    service: string;
    requests: number;
    tokens?: number;
    minutes?: number;
  }>;
  usageByDay: Array<{
    date: string;
    requests: number;
    tokens: number;
    minutes: number;
  }>;
  usageByAssistant: Array<{
    assistantId: string;
    assistantName: string;
    requests: number;
    tokens: number;
    minutes: number;
  }>;
  topUsers: Array<{
    userId: string;
    userName: string;
    requests: number;
  }>;
}

// Create analytics query
export async function createAnalyticsQuery(query: AnalyticsQuery) {
  try {
    const res = await axiosInstance.post("/api/analytics/query", query);
    return res.data;
  } catch (error: any) {
    console.error("Error creating analytics query:", error);
    const message = error.response?.data?.message || "Failed to execute analytics query";
    toast.error(message);
    throw error;
  }
}

// Get call analytics
export async function getCallAnalytics(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/analytics/calls", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching call analytics:", error);
    toast.error("Failed to fetch call analytics");
    throw error;
  }
}

// Get cost analytics
export async function getCostAnalytics(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/analytics/costs", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching cost analytics:", error);
    toast.error("Failed to fetch cost analytics");
    throw error;
  }
}

// Get performance analytics
export async function getPerformanceAnalytics(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/analytics/performance", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching performance analytics:", error);
    toast.error("Failed to fetch performance analytics");
    throw error;
  }
}

// Get usage analytics
export async function getUsageAnalytics(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/analytics/usage", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching usage analytics:", error);
    toast.error("Failed to fetch usage analytics");
    throw error;
  }
}

// Get real-time analytics
export async function getRealTimeAnalytics() {
  try {
    const res = await axiosInstance.get("/api/analytics/realtime");
    return res.data;
  } catch (error) {
    console.error("Error fetching real-time analytics:", error);
    toast.error("Failed to fetch real-time analytics");
    throw error;
  }
}

// Export analytics data
export async function exportAnalyticsData(query: AnalyticsQuery, format: "csv" | "json" | "xlsx") {
  try {
    const res = await axiosInstance.post("/api/analytics/export", 
      { query, format },
      { responseType: "blob" }
    );
    
    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `analytics-export.${format}`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    
    toast.success("Analytics data exported successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error exporting analytics data:", error);
    const message = error.response?.data?.message || "Failed to export analytics data";
    toast.error(message);
    throw error;
  }
}
