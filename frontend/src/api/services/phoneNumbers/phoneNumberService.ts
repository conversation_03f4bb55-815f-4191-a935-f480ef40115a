import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface PhoneNumber {
  id: string;
  number: string;
  friendlyName?: string;
  provider?: string;
  capabilities?: {
    voice: boolean;
    sms: boolean;
    mms: boolean;
  };
  status: "active" | "inactive" | "pending";
  assistantId?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface PhoneNumberConfig {
  number?: string;
  friendlyName?: string;
  assistantId?: string;
  provider?: string;
  capabilities?: {
    voice: boolean;
    sms: boolean;
    mms: boolean;
  };
}

export interface AvailablePhoneNumber {
  number: string;
  friendlyName?: string;
  locality?: string;
  region?: string;
  country?: string;
  capabilities?: {
    voice: boolean;
    sms: boolean;
    mms: boolean;
  };
  monthlyPrice?: number;
}

export interface PhoneNumberSearchParams {
  areaCode?: string;
  contains?: string;
  locality?: string;
  region?: string;
  country?: string;
  limit?: number;
}

// Get all phone numbers
export async function getAllPhoneNumbers(page?: number, search?: string) {
  try {
    const res = await axiosInstance.get("/api/phone-numbers", {
      params: { page, search },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching phone numbers:", error);
    toast.error("Failed to fetch phone numbers");
    throw error;
  }
}

// Get phone number by ID
export async function getPhoneNumberById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/phone-numbers/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching phone number:", error);
    toast.error("Failed to fetch phone number");
    throw error;
  }
}

// Create phone number
export async function createPhoneNumber(data: PhoneNumberConfig) {
  try {
    const res = await axiosInstance.post("/api/phone-numbers", data);
    toast.success("Phone number created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating phone number:", error);
    const message = error.response?.data?.message || "Failed to create phone number";
    toast.error(message);
    throw error;
  }
}

// Update phone number
export async function updatePhoneNumber(id: string, data: Partial<PhoneNumberConfig>) {
  try {
    const res = await axiosInstance.patch(`/api/phone-numbers/${id}`, data);
    toast.success("Phone number updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating phone number:", error);
    const message = error.response?.data?.message || "Failed to update phone number";
    toast.error(message);
    throw error;
  }
}

// Delete phone number
export async function deletePhoneNumber(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/phone-numbers/${id}`);
    toast.success("Phone number deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting phone number:", error);
    const message = error.response?.data?.message || "Failed to delete phone number";
    toast.error(message);
    throw error;
  }
}

// Buy new phone number
export async function buyPhoneNumber(data: {
  number: string;
  friendlyName?: string;
  assistantId?: string;
}) {
  try {
    const res = await axiosInstance.post("/api/phone-numbers/buy", data);
    toast.success("Phone number purchased successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error buying phone number:", error);
    const message = error.response?.data?.message || "Failed to purchase phone number";
    toast.error(message);
    throw error;
  }
}

// Search available phone numbers
export async function searchAvailablePhoneNumbers(params: PhoneNumberSearchParams) {
  try {
    const res = await axiosInstance.get("/api/phone-numbers/search/available", {
      params,
    });
    return res.data;
  } catch (error) {
    console.error("Error searching available phone numbers:", error);
    toast.error("Failed to search available phone numbers");
    throw error;
  }
}
