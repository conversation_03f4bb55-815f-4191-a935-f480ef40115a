import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface VoiceProvider {
  name: string;
  voices: Voice[];
}

export interface Voice {
  id: string;
  name: string;
  provider: string;
  gender?: string;
  accent?: string;
  language?: string;
  description?: string;
  previewUrl?: string;
}

export interface VoiceConfig {
  provider: string;
  voiceId: string;
  speed?: number;
  pitch?: number;
  volume?: number;
  stability?: number;
  similarityBoost?: number;
  style?: number;
  useSpeakerBoost?: boolean;
}

export interface VoiceStats {
  totalSynthesis: number;
  totalCharacters: number;
  averageLatency: number;
  errorRate: number;
  costBreakdown: {
    provider: string;
    cost: number;
  }[];
}

// Get all available voices
export async function getAvailableVoices() {
  try {
    const res = await axiosInstance.get("/api/voices/available");
    return res.data;
  } catch (error) {
    console.error("Error fetching available voices:", error);
    toast.error("Failed to fetch available voices");
    throw error;
  }
}

// Create voice configuration
export async function createVoiceConfig(data: VoiceConfig) {
  try {
    const res = await axiosInstance.post("/api/voices/config", data);
    toast.success("Voice configuration created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating voice config:", error);
    const message = error.response?.data?.message || "Failed to create voice configuration";
    toast.error(message);
    throw error;
  }
}

// Test voice synthesis
export async function testVoiceSynthesis(data: {
  provider: string;
  voiceId: string;
  text: string;
  speed?: number;
  pitch?: number;
}) {
  try {
    const res = await axiosInstance.post("/api/voices/test", data);
    toast.success("Voice test completed successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error testing voice:", error);
    const message = error.response?.data?.message || "Failed to test voice";
    toast.error(message);
    throw error;
  }
}

// Get voice usage statistics
export async function getVoiceStats() {
  try {
    const res = await axiosInstance.get("/api/voices/stats");
    return res.data;
  } catch (error) {
    console.error("Error fetching voice stats:", error);
    toast.error("Failed to fetch voice statistics");
    throw error;
  }
}

// Get voices by provider
export async function getVoicesByProvider(provider: string) {
  try {
    const res = await axiosInstance.get(`/api/voices/provider/${provider}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching voices by provider:", error);
    toast.error("Failed to fetch voices");
    throw error;
  }
}

// Clone voice (for supported providers)
export async function cloneVoice(data: {
  provider: string;
  name: string;
  description?: string;
  files: File[];
}) {
  try {
    const formData = new FormData();
    formData.append("provider", data.provider);
    formData.append("name", data.name);
    if (data.description) {
      formData.append("description", data.description);
    }
    data.files.forEach((file, index) => {
      formData.append(`files[${index}]`, file);
    });

    const res = await axiosInstance.post("/api/voices/clone", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    toast.success("Voice cloned successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error cloning voice:", error);
    const message = error.response?.data?.message || "Failed to clone voice";
    toast.error(message);
    throw error;
  }
}
