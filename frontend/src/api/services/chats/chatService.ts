import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface Chat {
  id: string;
  name?: string;
  assistantId?: string;
  status: "active" | "ended";
  messageCount: number;
  startedAt: string;
  endedAt?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface ChatConfig {
  name?: string;
  assistantId?: string;
  metadata?: Record<string, any>;
}

export interface ChatMessage {
  id: string;
  chatId: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface ChatCompletion {
  model: string;
  messages: Array<{
    role: "user" | "assistant" | "system";
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stream?: boolean;
}

export interface ChatStats {
  totalChats: number;
  activeChats: number;
  totalMessages: number;
  averageMessagesPerChat: number;
  chatsByDay: {
    date: string;
    count: number;
  }[];
  messagesByDay: {
    date: string;
    count: number;
  }[];
}

// Get all chats
export async function getAllChats(page?: number, search?: string, status?: string) {
  try {
    const res = await axiosInstance.get("/api/chats", {
      params: { page, search, status },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching chats:", error);
    toast.error("Failed to fetch chats");
    throw error;
  }
}

// Get chat by ID
export async function getChatById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/chats/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching chat:", error);
    toast.error("Failed to fetch chat");
    throw error;
  }
}

// Create chat
export async function createChat(data: ChatConfig) {
  try {
    const res = await axiosInstance.post("/api/chats", data);
    toast.success("Chat created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating chat:", error);
    const message = error.response?.data?.message || "Failed to create chat";
    toast.error(message);
    throw error;
  }
}

// Delete chat
export async function deleteChat(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/chats/${id}`);
    toast.success("Chat deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting chat:", error);
    const message = error.response?.data?.message || "Failed to delete chat";
    toast.error(message);
    throw error;
  }
}

// Send message to chat
export async function sendMessageToChat(id: string, data: {
  content: string;
  role?: "user" | "system";
  metadata?: Record<string, any>;
}) {
  try {
    const res = await axiosInstance.post(`/api/chats/${id}/messages`, data);
    return res.data;
  } catch (error: any) {
    console.error("Error sending message to chat:", error);
    const message = error.response?.data?.message || "Failed to send message";
    toast.error(message);
    throw error;
  }
}

// Get chat messages
export async function getChatMessages(id: string, page?: number, limit?: number) {
  try {
    const res = await axiosInstance.get(`/api/chats/${id}/messages`, {
      params: { page, limit },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching chat messages:", error);
    toast.error("Failed to fetch chat messages");
    throw error;
  }
}

// Create chat completion (OpenAI compatible)
export async function createChatCompletion(data: ChatCompletion) {
  try {
    const res = await axiosInstance.post("/api/chats/completions", data);
    return res.data;
  } catch (error: any) {
    console.error("Error creating chat completion:", error);
    const message = error.response?.data?.message || "Failed to create chat completion";
    toast.error(message);
    throw error;
  }
}

// Stream chat completion
export async function streamChatCompletion(data: ChatCompletion, onChunk: (chunk: string) => void) {
  try {
    const response = await fetch(`${axiosInstance.defaults.baseURL}/api/chats/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${localStorage.getItem("token")}`,
      },
      body: JSON.stringify({ ...data, stream: true }),
    });

    if (!response.ok) {
      throw new Error("Failed to stream chat completion");
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error("No reader available");
    }

    const decoder = new TextDecoder();
    let buffer = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const data = line.slice(6);
          if (data === "[DONE]") {
            return;
          }
          try {
            const parsed = JSON.parse(data);
            const content = parsed.choices?.[0]?.delta?.content;
            if (content) {
              onChunk(content);
            }
          } catch (e) {
            console.warn("Failed to parse SSE data:", data);
          }
        }
      }
    }
  } catch (error: any) {
    console.error("Error streaming chat completion:", error);
    const message = error.message || "Failed to stream chat completion";
    toast.error(message);
    throw error;
  }
}

// Get chat statistics
export async function getChatStats(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/chats/stats/overview", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching chat stats:", error);
    toast.error("Failed to fetch chat statistics");
    throw error;
  }
}
