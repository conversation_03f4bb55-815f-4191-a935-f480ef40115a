// src/api/services/contact/contactService.ts

import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface Contact {
  id: string;
  email: string;
  contact_number: string;
  firstName?: string;
  lastName?: string;
  company?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  listId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ContactList {
  id: string;
  list_name: string;
  list_description?: string;
  user_id: number;
  contactCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ContactPayload {
  email: string;
  contact_number: string;
  firstName?: string;
  lastName?: string;
  company?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

export interface ContactServiceData {
  user_id: number;
  list_name: string;
  list_description: string;
  contacts: ContactPayload[];
}

export interface ContactStats {
  totalContacts: number;
  totalLists: number;
  contactsByList: Array<{
    listId: string;
    listName: string;
    count: number;
  }>;
  contactsByTag: Array<{
    tag: string;
    count: number;
  }>;
  recentContacts: Contact[];
}

// Create contact list
export const createContactService = async (
  data: ContactServiceData
): Promise<string> => {
  try {
    const response = await axiosInstance.post("/api/contacts", data);
    toast.success("Contact list created successfully");
    return response.data;
  } catch (error) {
    console.error("Contact Service Error:", error);
    toast.error("Failed to create contact list");
    throw error;
  }
};

// Get all contacts
export const getAllContacts = async (page: number, search: string) => {
  try {
    const response = await axiosInstance.get("/api/contacts", {
      params: { page, search },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching contacts:", error);
    toast.error("Failed to fetch contacts");
    throw error;
  }
};

// Get contact by ID
export const getContactById = async (id: string) => {
  try {
    const response = await axiosInstance.get(`/api/contacts/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching contact:", error);
    toast.error("Failed to fetch contact");
    throw error;
  }
};

// Update contact
export const updateContact = async (id: string, data: Partial<ContactPayload>) => {
  try {
    const response = await axiosInstance.put(`/api/contacts/${id}`, data);
    toast.success("Contact updated successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error updating contact:", error);
    const message = error.response?.data?.message || "Failed to update contact";
    toast.error(message);
    throw error;
  }
};

// Delete contact
export const deleteContact = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/api/contacts/${id}`);
    toast.success("Contact deleted successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error deleting contact:", error);
    const message = error.response?.data?.message || "Failed to delete contact";
    toast.error(message);
    throw error;
  }
};

// Get all contact lists
export const getAllContactLists = async (page?: number, search?: string) => {
  try {
    const response = await axiosInstance.get("/api/contacts/lists", {
      params: { page, search },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching contact lists:", error);
    toast.error("Failed to fetch contact lists");
    throw error;
  }
};

// Get contact list by ID
export const getContactListById = async (id: string) => {
  try {
    const response = await axiosInstance.get(`/api/contacts/lists/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching contact list:", error);
    toast.error("Failed to fetch contact list");
    throw error;
  }
};

// Update contact list
export const updateContactList = async (id: string, data: {
  list_name?: string;
  list_description?: string;
}) => {
  try {
    const response = await axiosInstance.put(`/api/contacts/lists/${id}`, data);
    toast.success("Contact list updated successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error updating contact list:", error);
    const message = error.response?.data?.message || "Failed to update contact list";
    toast.error(message);
    throw error;
  }
};

// Delete contact list
export const deleteContactList = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/api/contacts/lists/${id}`);
    toast.success("Contact list deleted successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error deleting contact list:", error);
    const message = error.response?.data?.message || "Failed to delete contact list";
    toast.error(message);
    throw error;
  }
};

// Add contact to list
export const addContactToList = async (listId: string, contact: ContactPayload) => {
  try {
    const response = await axiosInstance.post(`/api/contacts/lists/${listId}/contacts`, contact);
    toast.success("Contact added to list successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error adding contact to list:", error);
    const message = error.response?.data?.message || "Failed to add contact to list";
    toast.error(message);
    throw error;
  }
};

// Remove contact from list
export const removeContactFromList = async (listId: string, contactId: string) => {
  try {
    const response = await axiosInstance.delete(`/api/contacts/lists/${listId}/contacts/${contactId}`);
    toast.success("Contact removed from list successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error removing contact from list:", error);
    const message = error.response?.data?.message || "Failed to remove contact from list";
    toast.error(message);
    throw error;
  }
};

// Import contacts from CSV
export const importContactsFromCSV = async (listId: string, file: File) => {
  try {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("listId", listId);

    const response = await axiosInstance.post("/api/contacts/import", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    toast.success("Contacts imported successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error importing contacts:", error);
    const message = error.response?.data?.message || "Failed to import contacts";
    toast.error(message);
    throw error;
  }
};

// Export contacts to CSV
export const exportContactsToCSV = async (listId?: string) => {
  try {
    const response = await axiosInstance.get("/api/contacts/export", {
      params: { listId },
      responseType: "blob",
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `contacts-export.csv`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    toast.success("Contacts exported successfully");
    return response.data;
  } catch (error: any) {
    console.error("Error exporting contacts:", error);
    const message = error.response?.data?.message || "Failed to export contacts";
    toast.error(message);
    throw error;
  }
};

// Get contact statistics
export const getContactStats = async () => {
  try {
    const response = await axiosInstance.get("/api/contacts/stats");
    return response.data;
  } catch (error) {
    console.error("Error fetching contact stats:", error);
    toast.error("Failed to fetch contact statistics");
    throw error;
  }
};

// Search contacts
export const searchContacts = async (query: string, filters?: {
  listId?: string;
  tags?: string[];
  company?: string;
}) => {
  try {
    const response = await axiosInstance.get("/api/contacts/search", {
      params: { query, ...filters },
    });
    return response.data;
  } catch (error) {
    console.error("Error searching contacts:", error);
    toast.error("Failed to search contacts");
    throw error;
  }
};
