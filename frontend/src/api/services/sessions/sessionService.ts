import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface Session {
  id: string;
  assistantId: string;
  status: "active" | "ended" | "paused";
  startedAt: string;
  endedAt?: string;
  duration?: number;
  messageCount: number;
  cost?: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface SessionConfig {
  assistantId: string;
  metadata?: Record<string, any>;
  maxDurationSeconds?: number;
  maxMessages?: number;
}

export interface SessionMessage {
  id: string;
  sessionId: string;
  role: "user" | "assistant" | "system";
  content: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface SessionStats {
  totalSessions: number;
  activeSessions: number;
  averageDuration: number;
  averageMessages: number;
  totalCost: number;
  sessionsByStatus: {
    status: string;
    count: number;
  }[];
  sessionsByDay: {
    date: string;
    count: number;
  }[];
}

// Get all sessions
export async function getAllSessions(page?: number, search?: string, status?: string) {
  try {
    const res = await axiosInstance.get("/api/sessions", {
      params: { page, search, status },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching sessions:", error);
    toast.error("Failed to fetch sessions");
    throw error;
  }
}

// Get session by ID
export async function getSessionById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/sessions/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching session:", error);
    toast.error("Failed to fetch session");
    throw error;
  }
}

// Create session
export async function createSession(data: SessionConfig) {
  try {
    const res = await axiosInstance.post("/api/sessions", data);
    toast.success("Session created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating session:", error);
    const message = error.response?.data?.message || "Failed to create session";
    toast.error(message);
    throw error;
  }
}

// Update session
export async function updateSession(id: string, data: Partial<SessionConfig>) {
  try {
    const res = await axiosInstance.patch(`/api/sessions/${id}`, data);
    toast.success("Session updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating session:", error);
    const message = error.response?.data?.message || "Failed to update session";
    toast.error(message);
    throw error;
  }
}

// Delete session
export async function deleteSession(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/sessions/${id}`);
    toast.success("Session deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting session:", error);
    const message = error.response?.data?.message || "Failed to delete session";
    toast.error(message);
    throw error;
  }
}

// End session
export async function endSession(id: string) {
  try {
    const res = await axiosInstance.post(`/api/sessions/${id}/end`);
    toast.success("Session ended successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error ending session:", error);
    const message = error.response?.data?.message || "Failed to end session";
    toast.error(message);
    throw error;
  }
}

// Pause session
export async function pauseSession(id: string) {
  try {
    const res = await axiosInstance.post(`/api/sessions/${id}/pause`);
    toast.success("Session paused successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error pausing session:", error);
    const message = error.response?.data?.message || "Failed to pause session";
    toast.error(message);
    throw error;
  }
}

// Resume session
export async function resumeSession(id: string) {
  try {
    const res = await axiosInstance.post(`/api/sessions/${id}/resume`);
    toast.success("Session resumed successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error resuming session:", error);
    const message = error.response?.data?.message || "Failed to resume session";
    toast.error(message);
    throw error;
  }
}

// Get session messages
export async function getSessionMessages(id: string, page?: number, limit?: number) {
  try {
    const res = await axiosInstance.get(`/api/sessions/${id}/messages`, {
      params: { page, limit },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching session messages:", error);
    toast.error("Failed to fetch session messages");
    throw error;
  }
}

// Send message to session
export async function sendMessageToSession(id: string, data: {
  content: string;
  role?: "user" | "system";
  metadata?: Record<string, any>;
}) {
  try {
    const res = await axiosInstance.post(`/api/sessions/${id}/messages`, data);
    return res.data;
  } catch (error: any) {
    console.error("Error sending message to session:", error);
    const message = error.response?.data?.message || "Failed to send message";
    toast.error(message);
    throw error;
  }
}

// Get session statistics
export async function getSessionStats(dateFrom?: string, dateTo?: string) {
  try {
    const res = await axiosInstance.get("/api/sessions/stats/overview", {
      params: { dateFrom, dateTo },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching session stats:", error);
    toast.error("Failed to fetch session statistics");
    throw error;
  }
}
