import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface ModelProvider {
  name: string;
  models: Model[];
}

export interface Model {
  id: string;
  name: string;
  provider: string;
  description?: string;
  contextLength?: number;
  pricing?: {
    input: number;
    output: number;
  };
}

export interface ModelConfig {
  provider: string;
  model: string;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  systemMessage?: string;
}

export interface ModelStats {
  totalRequests: number;
  totalTokens: number;
  averageLatency: number;
  errorRate: number;
  costBreakdown: {
    provider: string;
    cost: number;
  }[];
}

// Get all available models
export async function getAvailableModels() {
  try {
    const res = await axiosInstance.get("/api/models/available");
    return res.data;
  } catch (error) {
    console.error("Error fetching available models:", error);
    toast.error("Failed to fetch available models");
    throw error;
  }
}

// Create model configuration
export async function createModelConfig(data: ModelConfig) {
  try {
    const res = await axiosInstance.post("/api/models/config", data);
    toast.success("Model configuration created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating model config:", error);
    const message = error.response?.data?.message || "Failed to create model configuration";
    toast.error(message);
    throw error;
  }
}

// Test model configuration
export async function testModelConfig(data: {
  provider: string;
  model: string;
  message: string;
  temperature?: number;
}) {
  try {
    const res = await axiosInstance.post("/api/models/test", data);
    toast.success("Model test completed successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error testing model:", error);
    const message = error.response?.data?.message || "Failed to test model";
    toast.error(message);
    throw error;
  }
}

// Get model usage statistics
export async function getModelStats() {
  try {
    const res = await axiosInstance.get("/api/models/stats");
    return res.data;
  } catch (error) {
    console.error("Error fetching model stats:", error);
    toast.error("Failed to fetch model statistics");
    throw error;
  }
}

// Get model by provider and name
export async function getModelByProvider(provider: string, modelName: string) {
  try {
    const res = await axiosInstance.get(`/api/models/${provider}/${modelName}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching model:", error);
    toast.error("Failed to fetch model details");
    throw error;
  }
}
