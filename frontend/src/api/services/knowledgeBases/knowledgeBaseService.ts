import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface KnowledgeBase {
  id: string;
  name: string;
  description?: string;
  fileIds: string[];
  topK?: number;
  chunkSize?: number;
  chunkOverlap?: number;
  embeddingModel?: string;
  status: "creating" | "ready" | "error";
  documentsCount: number;
  chunksCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface KnowledgeBaseConfig {
  name: string;
  description?: string;
  fileIds: string[];
  topK?: number;
  chunkSize?: number;
  chunkOverlap?: number;
  embeddingModel?: string;
}

export interface KnowledgeBaseSearchQuery {
  query: string;
  topK?: number;
  threshold?: number;
  includeMetadata?: boolean;
}

export interface KnowledgeBaseSearchResult {
  id: string;
  content: string;
  metadata: Record<string, any>;
  score: number;
  source: {
    fileId: string;
    fileName: string;
    chunkIndex: number;
  };
}

export interface KnowledgeBaseStats {
  totalDocuments: number;
  totalChunks: number;
  averageChunkSize: number;
  embeddingModel: string;
  lastUpdated: string;
  searchQueries: {
    total: number;
    averageLatency: number;
    topQueries: Array<{
      query: string;
      count: number;
    }>;
  };
}

// Get all knowledge bases
export async function getAllKnowledgeBases(page?: number, search?: string) {
  try {
    const res = await axiosInstance.get("/api/knowledge-bases", {
      params: { page, search },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching knowledge bases:", error);
    toast.error("Failed to fetch knowledge bases");
    throw error;
  }
}

// Get knowledge base by ID
export async function getKnowledgeBaseById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/knowledge-bases/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching knowledge base:", error);
    toast.error("Failed to fetch knowledge base");
    throw error;
  }
}

// Create knowledge base
export async function createKnowledgeBase(data: KnowledgeBaseConfig) {
  try {
    const res = await axiosInstance.post("/api/knowledge-bases", data);
    toast.success("Knowledge base created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating knowledge base:", error);
    const message = error.response?.data?.message || "Failed to create knowledge base";
    toast.error(message);
    throw error;
  }
}

// Update knowledge base
export async function updateKnowledgeBase(id: string, data: Partial<KnowledgeBaseConfig>) {
  try {
    const res = await axiosInstance.patch(`/api/knowledge-bases/${id}`, data);
    toast.success("Knowledge base updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating knowledge base:", error);
    const message = error.response?.data?.message || "Failed to update knowledge base";
    toast.error(message);
    throw error;
  }
}

// Delete knowledge base
export async function deleteKnowledgeBase(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/knowledge-bases/${id}`);
    toast.success("Knowledge base deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting knowledge base:", error);
    const message = error.response?.data?.message || "Failed to delete knowledge base";
    toast.error(message);
    throw error;
  }
}

// Search knowledge base
export async function searchKnowledgeBase(id: string, query: KnowledgeBaseSearchQuery) {
  try {
    const res = await axiosInstance.post(`/api/knowledge-bases/${id}/search`, query);
    return res.data;
  } catch (error: any) {
    console.error("Error searching knowledge base:", error);
    const message = error.response?.data?.message || "Failed to search knowledge base";
    toast.error(message);
    throw error;
  }
}

// Get knowledge base statistics
export async function getKnowledgeBaseStats(id: string) {
  try {
    const res = await axiosInstance.get(`/api/knowledge-bases/${id}/stats`);
    return res.data;
  } catch (error) {
    console.error("Error fetching knowledge base stats:", error);
    toast.error("Failed to fetch knowledge base statistics");
    throw error;
  }
}

// Add files to knowledge base
export async function addFilesToKnowledgeBase(id: string, fileIds: string[]) {
  try {
    const res = await axiosInstance.post(`/api/knowledge-bases/${id}/files`, { fileIds });
    toast.success("Files added to knowledge base successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error adding files to knowledge base:", error);
    const message = error.response?.data?.message || "Failed to add files to knowledge base";
    toast.error(message);
    throw error;
  }
}

// Remove files from knowledge base
export async function removeFilesFromKnowledgeBase(id: string, fileIds: string[]) {
  try {
    const res = await axiosInstance.delete(`/api/knowledge-bases/${id}/files`, {
      data: { fileIds },
    });
    toast.success("Files removed from knowledge base successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error removing files from knowledge base:", error);
    const message = error.response?.data?.message || "Failed to remove files from knowledge base";
    toast.error(message);
    throw error;
  }
}
