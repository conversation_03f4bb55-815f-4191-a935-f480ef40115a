import axiosInstance from "@/api/axios/axiosInstance";
import { toast } from "sonner";

// Types
export interface Tool {
  id: string;
  type: "function" | "dtmf" | "endCall" | "transferCall" | "voicemail";
  name: string;
  description?: string;
  function?: {
    name: string;
    description: string;
    parameters: {
      type: string;
      properties: Record<string, any>;
      required?: string[];
    };
    url?: string;
    method?: "GET" | "POST" | "PUT" | "DELETE";
    headers?: Record<string, string>;
  };
  dtmf?: {
    function: {
      name: string;
      description: string;
      parameters: any;
    };
  };
  endCall?: {
    enabled: boolean;
  };
  transferCall?: {
    destinations: Array<{
      type: "number" | "assistant";
      value: string;
      description?: string;
    }>;
  };
  voicemail?: {
    enabled: boolean;
    detectVoicemail?: boolean;
    voicemailDetectionTimeout?: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface ToolConfig {
  type: "function" | "dtmf" | "endCall" | "transferCall" | "voicemail";
  name: string;
  description?: string;
  function?: {
    name: string;
    description: string;
    parameters: {
      type: string;
      properties: Record<string, any>;
      required?: string[];
    };
    url?: string;
    method?: "GET" | "POST" | "PUT" | "DELETE";
    headers?: Record<string, string>;
  };
  dtmf?: {
    function: {
      name: string;
      description: string;
      parameters: any;
    };
  };
  endCall?: {
    enabled: boolean;
  };
  transferCall?: {
    destinations: Array<{
      type: "number" | "assistant";
      value: string;
      description?: string;
    }>;
  };
  voicemail?: {
    enabled: boolean;
    detectVoicemail?: boolean;
    voicemailDetectionTimeout?: number;
  };
}

export interface ToolTemplate {
  id: string;
  name: string;
  description: string;
  type: string;
  template: ToolConfig;
}

// Get all tools
export async function getAllTools(page?: number, search?: string) {
  try {
    const res = await axiosInstance.get("/api/tools", {
      params: { page, search },
    });
    return res.data;
  } catch (error) {
    console.error("Error fetching tools:", error);
    toast.error("Failed to fetch tools");
    throw error;
  }
}

// Get tool by ID
export async function getToolById(id: string) {
  try {
    const res = await axiosInstance.get(`/api/tools/${id}`);
    return res.data;
  } catch (error) {
    console.error("Error fetching tool:", error);
    toast.error("Failed to fetch tool");
    throw error;
  }
}

// Create tool
export async function createTool(data: ToolConfig) {
  try {
    const res = await axiosInstance.post("/api/tools", data);
    toast.success("Tool created successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error creating tool:", error);
    const message = error.response?.data?.message || "Failed to create tool";
    toast.error(message);
    throw error;
  }
}

// Update tool
export async function updateTool(id: string, data: Partial<ToolConfig>) {
  try {
    const res = await axiosInstance.patch(`/api/tools/${id}`, data);
    toast.success("Tool updated successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error updating tool:", error);
    const message = error.response?.data?.message || "Failed to update tool";
    toast.error(message);
    throw error;
  }
}

// Delete tool
export async function deleteTool(id: string) {
  try {
    const res = await axiosInstance.delete(`/api/tools/${id}`);
    toast.success("Tool deleted successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error deleting tool:", error);
    const message = error.response?.data?.message || "Failed to delete tool";
    toast.error(message);
    throw error;
  }
}

// Get tool templates
export async function getToolTemplates() {
  try {
    const res = await axiosInstance.get("/api/tools/templates/all");
    return res.data;
  } catch (error) {
    console.error("Error fetching tool templates:", error);
    toast.error("Failed to fetch tool templates");
    throw error;
  }
}

// Test tool function
export async function testTool(id: string, testData?: any) {
  try {
    const res = await axiosInstance.post(`/api/tools/${id}/test`, testData);
    toast.success("Tool test completed successfully");
    return res.data;
  } catch (error: any) {
    console.error("Error testing tool:", error);
    const message = error.response?.data?.message || "Failed to test tool";
    toast.error(message);
    throw error;
  }
}
