// src/App.tsx

import { Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";
import { Toaster } from "./components/ui/toaster";

// Layouts
const DashboardLayout = lazy(
  () => import("./layout/Dashboard/DashboardLayout")
);
import AuthLayout from "./layout/Auth/AuthLayout";

// Pages
const Login = lazy(() => import("./pages/auth/Login"));
const Register = lazy(() => import("./pages/auth/Register"));
const Dashboard = lazy(() => import("./pages/dashboard/Dashboard"));
const KnowledgeBases = lazy(
  () => import("./pages/KnowledgeBases/KnowledgeBases")
);
const Action = lazy(() => import("./pages/Actions/Action"));
const Contacts = lazy(() => import("./pages/Contacts/Contacts"));
const Lists = lazy(() => import("./pages/Lists/Lists"));
const Campaigns = lazy(() => import("./pages/Campaigns/Campaigns"));
const Recordings = lazy(() => import("./pages/Recordings/Recordings"));
const RecordingDetail = lazy(() => import("./pages/Recordings/RecordingDetail"));
const Support = lazy(() => import("./pages/Support/Support"));
const Agents = lazy(() => import("./pages/Agents/Agents"));
const AddAgent = lazy(() => import("./pages/Agents/AddAgent").then(module => ({ default: module.AddAgent })));
const Models = lazy(() => import("./pages/Models/Models"));
const Voices = lazy(() => import("./pages/Voices/Voices"));
const PhoneNumbers = lazy(() => import("./pages/PhoneNumbers/PhoneNumbers"));
const OutboundCalls = lazy(() => import("./pages/OutboundCalls/OutboundCalls"));
const Workflows = lazy(() => import("./pages/Workflows/Workflows"));
const WorkflowDetail = lazy(() => import("./pages/Workflows/WorkflowDetail"));
const WorkflowBuilder = lazy(() => import("./pages/Workflows/WorkflowBuilder"));


const Settings = lazy(() => import("./pages/Settings/Settings"));
const NotFound = lazy(() => import("./pages/NotFound/NotFound"));

// Protected Route
import ProtectedRoutes from "./components/ProtectedRoutes";

// Theme Provider
import { ThemeProvider } from "@/components/theme-provider";
import Loading from "./Loading";
import { AuthProvider } from "./context/AuthContext";
import { MobileMenuProvider } from "./context/MobileMenuContext";

// Tanstack Query
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

function App() {
  const queryClient = new QueryClient();

  return (
    <>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <AuthProvider>
            <MobileMenuProvider>
              <Suspense fallback={<Loading />}>
              <Routes>
                {/* Authentication Routes */}
                <Route element={<AuthLayout />}>
                  <Route path="login" element={<Login />} />
                  <Route path="register" element={<Register />} />
                </Route>

                {/* Protected Routes */}
                <Route element={<ProtectedRoutes />}>
                  <Route path="/" element={<DashboardLayout />}>
                    <Route index element={<Dashboard />} />
                    <Route path="dashboard" element={<Dashboard />} />
                    <Route path="agents" element={<Agents />} />
                    <Route path="agents/add" element={<AddAgent />} />
                    <Route path="models" element={<Models />} />
                    <Route path="voices" element={<Voices />} />
                    <Route path="phone-numbers" element={<PhoneNumbers />} />
                    <Route path="outbound-calls" element={<OutboundCalls />} />
                    <Route path="workflows" element={<Workflows />} />
                    <Route path="workflows/:id" element={<WorkflowDetail />} />
                    <Route path="workflows/:id/edit" element={<WorkflowBuilder />} />


                    <Route
                      path="knowledge-bases"
                      element={<KnowledgeBases />}
                    />
                    <Route path="actions" element={<Action />} />
                    <Route path="contacts" element={<Contacts />} />
                    <Route path="lists" element={<Lists />} />
                    <Route path="campaigns" element={<Campaigns />} />
                    <Route path="recordings" element={<Recordings />} />
                    <Route path="recordings/:id" element={<RecordingDetail />} />

                    <Route path="support" element={<Support />} />
                    <Route path="settings" element={<Settings />} />
                  </Route>
                </Route>

                {/* Fallback Routes */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
            </MobileMenuProvider>
          </AuthProvider>
        </ThemeProvider>
        <Toaster richColors position="top-right" />
      </QueryClientProvider>
    </>
  );
}

export default App;
