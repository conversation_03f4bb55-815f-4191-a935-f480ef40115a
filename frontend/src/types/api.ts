// API Types
// This file contains all the TypeScript types used across the API services

// Common types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface DateRangeParams {
  dateFrom?: string;
  dateTo?: string;
}

// Re-export all types from services
export type {
  // Auth types
  LoginResponse
} from '../api/services/auth/authService';

export type {
  // Agent types
  Assistant,
  AssistantConfig
} from '../api/services/agents/agentService';

export type {
  // Contact types
  Contact,
  ContactList,
  ContactPayload,
  ContactServiceData,
  ContactStats
} from '../api/services/contact/contactService';

export type {
  // Model types
  Model,
  ModelProvider,
  ModelConfig,
  ModelStats
} from '../api/services/models/modelService';

export type {
  // Voice types
  Voice,
  VoiceProvider,
  VoiceConfig,
  VoiceStats
} from '../api/services/voices/voiceService';

export type {
  // Transcriber types
  TranscriberModel,
  TranscriberProvider,
  TranscriberConfig,
  TranscriberStats
} from '../api/services/transcribers/transcriberService';

export type {
  // Phone Number types
  PhoneNumber,
  PhoneNumberConfig,
  AvailablePhoneNumber,
  PhoneNumberSearchParams
} from '../api/services/phoneNumbers/phoneNumberService';

export type {
  // Tool types
  Tool,
  ToolConfig,
  ToolTemplate
} from '../api/services/tools/toolService';

export type {
  // File types
  FileItem,
  FileUploadConfig,
  FileStats
} from '../api/services/files/fileService';

export type {
  // Knowledge Base types
  KnowledgeBase,
  KnowledgeBaseConfig,
  KnowledgeBaseSearchQuery,
  KnowledgeBaseSearchResult,
  KnowledgeBaseStats
} from '../api/services/knowledgeBases/knowledgeBaseService';

export type {
  // Outbound Call types
  OutboundCall,
  OutboundCallConfig,
  CallStats
} from '../api/services/outbound/outboundService';

export type {
  // Session types
  Session,
  SessionConfig,
  SessionMessage,
  SessionStats
} from '../api/services/sessions/sessionService';

export type {
  // Chat types
  Chat,
  ChatConfig,
  ChatMessage,
  ChatCompletion,
  ChatStats
} from '../api/services/chats/chatService';

export type {
  // Squad types
  Squad,
  SquadMember,
  SquadConfig,
  SquadStats
} from '../api/services/squads/squadService';



export type {
  // Analytics types
  AnalyticsQuery,
  AnalyticsResult,
  CallAnalytics,
  CostAnalytics,
  PerformanceAnalytics,
  UsageAnalytics
} from '../api/services/analytics/analyticsService';

export type {
  // Log types
  LogEntry,
  LogQuery,
  LogStats,
  LogAlert
} from '../api/services/logs/logService';

export type {
  // Webhook types
  Webhook,
  WebhookConfig,
  WebhookEvent,
  WebhookStats
} from '../api/services/webhooks/webhookService';

// Status types
export type Status = "active" | "inactive" | "pending" | "completed" | "failed" | "cancelled";

// Provider types
export type AIProvider = "openai" | "anthropic" | "groq" | "together" | "anyscale" | "openrouter" | "perplexity";
export type VoiceProvider = "11labs" | "playht" | "azure" | "openai" | "deepgram" | "cartesia" | "lmnt";
export type TranscriberProvider = "deepgram" | "assembly" | "openai" | "azure" | "google";

// Common enums
export enum LogLevel {
  DEBUG = "debug",
  INFO = "info",
  WARN = "warn", 
  ERROR = "error",
  FATAL = "fatal"
}

export enum CallStatus {
  QUEUED = "queued",
  RINGING = "ringing",
  IN_PROGRESS = "in-progress",
  FORWARDING = "forwarding",
  ENDED = "ended"
}

// Workflow types
export interface WorkflowNode {
  type: 'conversation' | 'apiRequest' | 'transferCall' | 'endCall' | 'tool';
  name: string;
  isStart?: boolean;
  prompt?: string;
  firstMessage?: string;
  model?: any;
  transcriber?: any;
  voice?: any;
  tools?: any[];
  toolIds?: string[];
  globalNodePlan?: {
    enabled: boolean;
    enterCondition: string;
  };
  variableExtractionPlan?: {
    schema: any;
    aliases?: Array<{ key: string; value: string }>;
  };
  metadata?: any;
}

export interface WorkflowEdge {
  from: string;
  to: string;
  condition?: {
    type: 'ai' | 'logical';
    prompt?: string;
    expression?: string;
  };
  metadata?: any;
}

export interface Workflow {
  id?: string;
  orgId?: string;
  name: string;
  description?: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  model?: any;
  transcriber?: any;
  voice?: any;
  observabilityPlan?: any;
  backgroundSound?: string;
  hooks?: any[];
  credentials?: any[];
  globalPrompt?: string;
  server?: any;
  compliancePlan?: any;
  analysisPlan?: any;
  artifactPlan?: any;
  startSpeakingPlan?: any;
  stopSpeakingPlan?: any;
  monitorPlan?: any;
  backgroundSpeechDenoisingPlan?: any;
  credentialIds?: string[];
  keypadInputPlan?: any;
  createdAt?: string;
  updatedAt?: string;
  localData?: any;
}

export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  callId?: string;
  executionType: 'test' | 'production';
  status: 'queued' | 'ringing' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
  phoneNumber?: string;
  duration: number;
  cost: number;
  metadata?: any;
  startedAt?: string;
  endedAt?: string;
  createdAt: string;
}

export enum WorkflowNodeType {
  CONVERSATION = "conversation",
  API_REQUEST = "apiRequest",
  TRANSFER_CALL = "transferCall",
  END_CALL = "endCall",
  TOOL = "tool"
}

export enum WorkflowStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  DRAFT = "draft",
  ARCHIVED = "archived"
}

export enum WebhookEventType {
  CONVERSATION_UPDATE = "conversation-update",
  FUNCTION_CALL = "function-call",
  HANG = "hang",
  SPEECH_UPDATE = "speech-update",
  STATUS_UPDATE = "status-update",
  TRANSCRIPT = "transcript",
  TOOL_CALLS = "tool-calls",
  TRANSFER_DESTINATION_REQUEST = "transfer-destination-request",
  USER_INTERRUPTED = "user-interrupted",
  END_OF_CALL_REPORT = "end-of-call-report",
  MODEL_OUTPUT = "model-output",
  TRANSFER_UPDATE = "transfer-update",
  VOICE_INPUT = "voice-input"
}
