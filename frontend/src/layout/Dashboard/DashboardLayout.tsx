import { Outlet } from "react-router-dom";
import Sidebar from "./Sidebar";
import Topbar from "./Topbar";
import SettingsIcon from "./SettingsIcon";
import { useMobileMenu } from "@/context/MobileMenuContext";

const DashboardLayout = () => {
  const { isMobileView } = useMobileMenu();

  return (
    <main
      className="h-screen flex bg-background text-foreground overflow-hidden"
      id="application-theme"
    >
      <div className="absolute inset-0 z-0 opacity-30 pointer-events-none">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent"></div>
        <div className="grid grid-cols-[repeat(20,1fr)] grid-rows-[repeat(20,1fr)] h-full w-full">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={`grid-line-h-${i}`}
              className="border-t border-primary/[0.03]"
            ></div>
          ))}
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={`grid-line-v-${i}`}
              className="border-l border-primary/[0.03]"
            ></div>
          ))}
        </div>
      </div>

      {/* Sidebar is always rendered but visibility is controlled by its internal state */}
      <Sidebar />

      <section className="flex flex-col justify-start w-full relative z-10">
        <Topbar />
        <div className="flex-1 overflow-auto">
          <Outlet />
        </div>
        {!isMobileView && <SettingsIcon />}
      </section>
    </main>
  );
};

export default DashboardLayout;
