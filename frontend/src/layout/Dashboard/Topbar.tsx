// src/layout/Dashboard/Topbar.tsx
import { FC } from "react";
import { BellIcon, LayoutDashboard, Menu } from "lucide-react";
import { ModeToggle } from "@/components/mode-toggle";
import { Link } from "react-router-dom";
import Breadcrumbs from "@/components/Breadcrumbs";
import { useMobileMenu } from "@/context/MobileMenuContext";
import ProfileMenu from "@/components/ProfileMenu";
import { cn } from "@/lib/utils";

const Topbar: FC = () => {
  const { toggleMobileMenu, isMobileView } = useMobileMenu();

  return (
    <div className="h-16 border-b border-primary/10 backdrop-blur-sm bg-background/80 flex items-center justify-between px-4 md:px-6 z-10">
      <div className="flex items-center space-x-2 md:space-x-6 flex-1 min-w-0">
        {/* Mobile menu hamburger - only visible on mobile */}
        {isMobileView && (
          <button
            onClick={toggleMobileMenu}
            className="p-2 rounded-md hover:bg-secondary/80 transition-colors"
            aria-label="Toggle mobile menu"
          >
            <Menu className="h-5 w-5" />
          </button>
        )}

        {/* Logo/Dashboard link - visible on all screens */}
        <Link to="/" className="flex-shrink-0">
          <h2 className="flex items-center gap-2 text-lg font-bold px-3 py-1 rounded-md bg-secondary/80">
            <LayoutDashboard className="h-4 w-4" />
            <span className="hidden xs:inline">Dashboard</span>
          </h2>
        </Link>

        {/* Breadcrumbs - hidden on mobile, visible on larger screens */}
        <div className={cn("min-w-0 flex-1 max-w-xl", isMobileView ? "hidden" : "block")}>
          <Breadcrumbs className="text-xs sm:text-sm" />
        </div>
      </div>

      <div className="flex items-center space-x-4 md:space-x-6">
        {/* Desktop view - show all elements */}
        {!isMobileView && (
          <>
            <ModeToggle />
            <div className="relative">
              <BellIcon className="w-6 h-6 cursor-pointer text-foreground/80 hover:text-primary transition-colors" />
              <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-primary text-[10px] flex items-center justify-center text-white font-bold">
                3
              </span>
            </div>
            <ProfileMenu showFullMenu={true} />
          </>
        )}

        {/* Mobile view - only show profile image with dropdown */}
        {isMobileView && (
          <ProfileMenu showFullMenu={false} />
        )}
      </div>
    </div>
  );
};

export default Topbar;
