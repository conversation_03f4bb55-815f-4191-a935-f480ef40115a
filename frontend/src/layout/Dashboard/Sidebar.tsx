import { FC, useState, useRef, useEffect } from "react";
import {
  SettingsIcon,
  LogOutIcon,
  Waypoints,
  Contact,
  List,
  Backpack,
  CassetteTape,
  MessageCircleQuestion,
  ChevronRight,
  ChevronLeft,
  UserCog,
  Book,
  X,
  Zap,
  Volume2,
  Phone,
  PhoneCall,
  Workflow,

} from "lucide-react";
import { cn } from "@/lib/utils"; // Utility function for combining class names
import { Link, useNavigate, useLocation } from "react-router-dom";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import gsap from "gsap";
import { motion, AnimatePresence } from "framer-motion";

import { useAuth } from "@/context/AuthContext";
import { useMobileMenu } from "@/context/MobileMenuContext";
import { ScrollArea } from "@/components/ui/scroll-area";

import { FireIcon as HeroFireIcon } from "@heroicons/react/24/solid";

// Animated Aura component for sidebar items
interface AnimatedAuraProps {
  isActive: boolean;
  colorScheme?: 'purple' | 'orange';
}

const AnimatedAura: FC<AnimatedAuraProps> = ({
  isActive,
  colorScheme = 'purple'
}) => {
  // Define color classes based on the color scheme
  const colors = {
    purple: {
      inner: "bg-purple-500/20",
      outer: "bg-indigo-400/15",
      particle1: "bg-violet-500/30",
      particle2: "bg-indigo-400/30",
      particle3: "bg-purple-400/30"
    },
    orange: {
      inner: "bg-orange-500/20",
      outer: "bg-orange-400/10",
      particle1: "bg-orange-500/30",
      particle2: "bg-orange-400/30",
      particle3: "bg-orange-400/30"
    }
  };

  const selectedColors = colors[colorScheme];

  return (
    <AnimatePresence>
      {isActive && (
        <>
          {/* Inner glow */}
          <motion.div
            className={`absolute inset-0 rounded-md ${selectedColors.inner} z-0 blur-[3px]`}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{
              scale: [0.8, 1.1, 0.9],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          />

          {/* Outer glow */}
          <motion.div
            className={`absolute inset-0 rounded-md ${selectedColors.outer} z-0 blur-[2px]`}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{
              scale: [0.9, 1.2, 1],
              opacity: [0.1, 0.4, 0.1],
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              repeatType: "reverse",
              ease: "easeInOut"
            }}
          />

          {/* Particle effect - top */}
          <motion.div
            className={`absolute w-2 h-2 ${selectedColors.particle1} rounded-full z-0 blur-[1px]`}
            style={{ top: '-10%', left: '50%', marginLeft: '-4px' }}
            initial={{ y: 0, opacity: 0, scale: 0.5 }}
            animate={{
              y: [-5, -12],
              opacity: [0, 0.6, 0],
              scale: [0.5, 1.2, 0.8]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "loop",
              ease: "easeOut"
            }}
          />

          {/* Particle effect - right */}
          <motion.div
            className={`absolute w-1.5 h-1.5 ${selectedColors.particle2} rounded-full z-0 blur-[1px]`}
            style={{ top: '30%', right: '-5%' }}
            initial={{ x: 0, opacity: 0, scale: 0.5 }}
            animate={{
              x: [0, 6],
              y: [-2, -6],
              opacity: [0, 0.5, 0],
              scale: [0.5, 1, 0.7]
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              repeatType: "loop",
              ease: "easeOut",
              delay: 0.5
            }}
          />

          {/* Particle effect - left */}
          <motion.div
            className={`absolute w-1.5 h-1.5 ${selectedColors.particle3} rounded-full z-0 blur-[1px]`}
            style={{ top: '30%', left: '-5%' }}
            initial={{ x: 0, opacity: 0, scale: 0.5 }}
            animate={{
              x: [-2, -6],
              y: [-2, -6],
              opacity: [0, 0.5, 0],
              scale: [0.5, 1, 0.7]
            }}
            transition={{
              duration: 2.5,
              repeat: Infinity,
              repeatType: "loop",
              ease: "easeOut",
              delay: 1
            }}
          />
        </>
      )}
    </AnimatePresence>
  );
};

// Animated Fire Icon component
interface AnimatedFireIconProps {
  className?: string;
  isAnimated?: boolean;
}

const AnimatedFireIcon: FC<AnimatedFireIconProps> = ({
  className,
  isAnimated = false
}) => {
  return (
    <div className="relative">
      {/* Base Fire Icon */}
      <HeroFireIcon
        className={cn(
          "relative z-10 transition-colors duration-300",
          isAnimated ? "text-orange-600" : "text-gray-700",
          className
        )}
      />

      {/* Animated flame aura - only visible when isAnimated is true */}
      <AnimatedAura isActive={isAnimated} colorScheme="orange" />
    </div>
  );
};

interface SidebarItemProps {
  icon: JSX.Element;
  label: string;
  href?: string;
  isCollapsed: boolean;
  isActive?: boolean;
  onclick?: () => void;
  classname?: string;
}

const SidebarItem: FC<SidebarItemProps> = ({
  icon,
  label,
  href = "#",
  isCollapsed,
  isActive = false,
  onclick,
  classname,
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link to={href} onClick={onclick}>
            <div
              className={cn(
                "flex items-center cursor-pointer rounded-md text-foreground transition-all duration-200 border border-transparent",
                "relative overflow-hidden",
                // Base padding that works for both collapsed and expanded states
                isCollapsed ? "p-3" : "px-3 py-3",
                // Ensure proper spacing in both modes
                !isCollapsed && "space-x-4",
                // Active state styling - more pronounced in collapsed mode
                isActive && isCollapsed
                  ? "bg-primary/15 border-primary/30"
                  : isActive
                    ? "bg-primary/10 border-primary/20 font-semibold"
                    : "hover:bg-primary/10 hover:border-primary/20",
                // Adjust padding when active but only in expanded mode
                isActive && !isCollapsed && "pl-5",
                classname
              )}
            >
              {/* Animated aura effect for active items */}
              <AnimatedAura isActive={isActive} colorScheme="purple" />

              {/* Left border accent for active state - more visible in collapsed mode */}
              {isActive && (
                <div className={cn(
                  "absolute left-0 top-0 bottom-0 bg-primary",
                  isCollapsed ? "w-1.5" : "w-1"
                )} />
              )}

              {/* Center the icon when collapsed for better visual balance */}
              <div className={cn(
                "flex-shrink-0 transition-all duration-200 relative z-10",
                isCollapsed ? "w-full flex justify-center items-center" : "w-6 h-6",
                isActive ? "text-primary" : "text-foreground"
              )}>
                <div className={cn(
                  "w-6 h-6 flex items-center justify-center",
                  // Add a subtle scale effect for active items in collapsed mode
                  isActive && isCollapsed && "scale-110"
                )}>
                  {icon}
                </div>
              </div>

              {!isCollapsed && (
                <span className={cn(
                  "text-sm transition-all duration-200 relative z-10",
                  isActive ? "font-semibold" : "font-medium"
                )}>
                  {label}
                </span>
              )}
            </div>
          </Link>
        </TooltipTrigger>
        {isCollapsed && (
          <TooltipContent
            side="right"
            align="center"
            className={cn(
              "transition-all duration-200 px-3 py-2 relative overflow-hidden",
              isActive
                ? "border-l-2 border-primary bg-primary/10 shadow-sm"
                : "bg-secondary/80"
            )}
          >
            {isActive && (
              <motion.div
                className="absolute inset-0 bg-purple-500/10 z-0 blur-[2px]"
                initial={{ opacity: 0 }}
                animate={{
                  opacity: [0.2, 0.4, 0.2],
                  scale: [0.95, 1.05, 0.95]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut"
                }}
              />
            )}
            <p className={cn(
              "text-sm relative z-10",
              isActive
                ? "font-semibold text-primary"
                : "text-foreground"
            )}>
              {label}
            </p>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
};

const Sidebar: FC = () => {
  const [isCollapsed, setIsCollapsed] = useState(true);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const toggleButtonRef = useRef<HTMLButtonElement>(null);
  const logoRef = useRef<HTMLImageElement>(null);
  const navigate = useNavigate();
  const location = useLocation();

  // contexts
  const { logout } = useAuth();
  const { isMobileMenuOpen, isMobileView, closeMobileMenu } = useMobileMenu();

  // Function to check if a route is active
  const isRouteActive = (path: string): boolean => {
    // Exact match for home/dashboard
    if (path === '/' && location.pathname === '/') {
      return true;
    }

    // For other routes, check if the current path starts with the given path
    // This handles nested routes like /settings/profile
    if (path !== '/') {
      return location.pathname.startsWith(path);
    }

    return false;
  };

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  // Define the target widths
  const expandedWidth = 240; // Equivalent to w-60 (15rem)
  const collapsedWidth = 80; // Equivalent to w-20 (5rem)

  // Handle navigation in mobile view
  const handleMobileNavigation = (path: string) => {
    if (isMobileView) {
      closeMobileMenu();
      navigate(path);
    }
  };

  useEffect(() => {
    const sidebar = sidebarRef.current;
    const toggleButton = toggleButtonRef.current;
    const logo = logoRef.current;

    if (sidebar && toggleButton && logo && !isMobileView) {
      // Initialize sidebar width (only for desktop)
      gsap.set(sidebar, {
        width: isCollapsed ? collapsedWidth : expandedWidth,
      });

      // Animate sidebar width on collapse/expand
      gsap.to(sidebar, {
        width: isCollapsed ? collapsedWidth : expandedWidth,
        duration: 0.3,
        ease: "power2.inOut",
      });

      // Optionally animate the logo size
      gsap.to(logo, {
        width: isCollapsed ? 16 : 32, // Adjust based on your logo sizes
        duration: 0.3,
        ease: "power2.inOut",
      });

      // Optionally animate the toggle button position
      gsap.to(toggleButton, {
        right: isCollapsed ? -16 : -32, // Adjust based on toggle button size
        duration: 0.3,
        ease: "power2.inOut",
      });
    }
  }, [isCollapsed, isMobileView]);

  // Determine sidebar classes based on mobile/desktop view
  const sidebarClasses = cn(
    "h-screen p-4 flex flex-col bg-secondary/80 backdrop-blur-sm border-r border-primary/10",
    {
      // Desktop styles
      "relative transition-width duration-300 ease-in-out": !isMobileView,
      // Mobile styles
      "fixed inset-y-0 left-0 z-50 w-[280px] shadow-xl transform transition-transform duration-300 ease-in-out": isMobileView,
      "translate-x-0": isMobileView && isMobileMenuOpen,
      "-translate-x-full": isMobileView && !isMobileMenuOpen,
    }
  );

  return (
    <>
      {/* Mobile overlay backdrop */}
      {isMobileView && isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 backdrop-blur-sm transition-opacity duration-300"
          onClick={closeMobileMenu}
        />
      )}

      <div
        ref={sidebarRef}
        className={sidebarClasses}
        style={!isMobileView ? { width: isCollapsed ? collapsedWidth : expandedWidth } : undefined}
      >
        {/* Mobile close button */}
        {isMobileView && (
          <button
            onClick={closeMobileMenu}
            className="absolute top-4 right-4 p-1 rounded-full bg-secondary/80 hover:bg-secondary text-foreground"
            aria-label="Close menu"
          >
            <X className="h-5 w-5" />
          </button>
        )}

        <div className="flex items-center justify-center mb-8">
          <AnimatedFireIcon
            className="w-8 h-8"
            isAnimated={location.pathname === "/"}
          />
          {(!isCollapsed || isMobileView) && (
            <h1 className="ml-4 text-lg font-bold font-mono tracking-widest">
              aicruitment
            </h1>
          )}
        </div>

        {/* Desktop toggle button - only show on desktop */}
        {!isMobileView && (
          <button
            ref={toggleButtonRef}
            onClick={() => setIsCollapsed((prev) => !prev)}
            className="h-8 w-8 flex items-center justify-center rounded-full bg-gray-800 focus:outline-none text-white absolute top-1/2 -right-3 transform -translate-y-1/2 z-50 shadow-md"
          >
            {isCollapsed ? <ChevronRight /> : <ChevronLeft />}
          </button>
        )}

      <ScrollArea>
        <nav className="flex-1 flex flex-col space-y-2">
          <SidebarItem
            icon={<UserCog />}
            label="Agents"
            href="/agents"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/agents')}
            onclick={isMobileView ? () => handleMobileNavigation('/agents') : undefined}
          />
          <SidebarItem
            icon={<Zap />}
            label="Models"
            href="/models"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/models')}
            onclick={isMobileView ? () => handleMobileNavigation('/models') : undefined}
          />
          <SidebarItem
            icon={<Volume2 />}
            label="Voices"
            href="/voices"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/voices')}
            onclick={isMobileView ? () => handleMobileNavigation('/voices') : undefined}
          />
          <SidebarItem
            icon={<Phone />}
            label="Phone Numbers"
            href="/phone-numbers"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/phone-numbers')}
            onclick={isMobileView ? () => handleMobileNavigation('/phone-numbers') : undefined}
          />
          <SidebarItem
            icon={<PhoneCall />}
            label="Outbound Calls"
            href="/outbound-calls"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/outbound-calls')}
            onclick={isMobileView ? () => handleMobileNavigation('/outbound-calls') : undefined}
          />
          <SidebarItem
            icon={<Workflow />}
            label="Workflows"
            href="/workflows"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/workflows')}
            onclick={isMobileView ? () => handleMobileNavigation('/workflows') : undefined}
          />

          <SidebarItem
            icon={<Book />}
            label="Knowledge Bases"
            href="/knowledge-bases"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/knowledge-bases')}
            onclick={isMobileView ? () => handleMobileNavigation('/knowledge-bases') : undefined}
          />
          <SidebarItem
            icon={<Waypoints />}
            label="Actions"
            href="/actions"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/actions')}
            onclick={isMobileView ? () => handleMobileNavigation('/actions') : undefined}
          />
          <SidebarItem
            icon={<Contact />}
            label="Contacts"
            href="/contacts"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/contacts')}
            onclick={isMobileView ? () => handleMobileNavigation('/contacts') : undefined}
          />
          <SidebarItem
            icon={<List />}
            label="Lists"
            href="/lists"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/lists')}
            onclick={isMobileView ? () => handleMobileNavigation('/lists') : undefined}
          />
          <SidebarItem
            icon={<Backpack />}
            label="Campaigns"
            href="/campaigns"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/campaigns')}
            onclick={isMobileView ? () => handleMobileNavigation('/campaigns') : undefined}
          />
          <SidebarItem
            icon={<CassetteTape />}
            label="Recordings"
            href="/recordings"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/recordings')}
            onclick={isMobileView ? () => handleMobileNavigation('/recordings') : undefined}
          />

          <SidebarItem
            icon={<MessageCircleQuestion />}
            label="Support"
            href="/support"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/support')}
            onclick={isMobileView ? () => handleMobileNavigation('/support') : undefined}
          />
          <SidebarItem
            icon={<SettingsIcon />}
            label="Settings"
            href="/settings"
            isCollapsed={isCollapsed && !isMobileView}
            isActive={isRouteActive('/settings')}
            onclick={isMobileView ? () => handleMobileNavigation('/settings') : undefined}
          />
        </nav>
      </ScrollArea>

      <div className="mt-auto">
        <SidebarItem
          icon={<LogOutIcon color="white" />}
          label="Logout"
          isCollapsed={isCollapsed && !isMobileView}
          onclick={handleLogout}
          classname="bg-gray-800 hover:bg-gray-700 text-white"
        />
      </div>
    </div>
    </>
  );
};

export default Sidebar;
