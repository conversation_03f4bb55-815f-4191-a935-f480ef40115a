// SettingsIcon.tsx

import React from "react";
import { Settings } from "lucide-react";

// Import Shadcn UI Sheet components
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

// Import useTheme hook
import { useTheme } from "@/components/theme-provider";

const SettingsIcon: React.FC = () => {
  const { theme } = useTheme();

  return (
    <Sheet>
      <SheetTrigger
        className="absolute bottom-5 right-5 rounded-full border border-primary shadow-sm shadow-primary h-10 w-10 flex items-center justify-center bg-primary text-white transition-transform transform hover:scale-125 focus:outline-none focus:ring-2 focus:ring-primary"
        aria-label="Open Settings"
      >
        <Settings size={24} className="transition-colors duration-200" />
      </SheetTrigger>

      <SheetContent className="w-full sm:w-96">
        <SheetHeader>
          <SheetTitle>Settings</SheetTitle>
          <SheetDescription>Configure application settings</SheetDescription>
        </SheetHeader>

        <div className="mt-6">
          <p className="text-muted-foreground">
            You're using the professional light theme designed for optimal
            readability and accessibility.
          </p>

          <div className="mt-4 p-4 bg-secondary rounded-md">
            <h3 className="font-medium mb-2">Theme Information</h3>
            <p className="text-sm text-muted-foreground">
              This theme is inspired by professional AI companies like
              Anthropic, OpenAI, and Perplexity, with colors chosen for
              accessibility and visual clarity.
            </p>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default SettingsIcon;
