import React, { useEffect, useRef } from 'react';
import { Outlet } from "react-router-dom";
import { motion } from 'framer-motion';
import {
  Mic,
  BarChart2,
  Calendar,
  Users,
  MessageSquare,
  Brain,
  Sparkles,
  Headphones,
  Shield,
  Zap
} from 'lucide-react';

// Waveform animation component
const VoiceWaveform: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions
    const resizeCanvas = () => {
      const parent = canvas.parentElement;
      if (parent) {
        canvas.width = parent.clientWidth;
        canvas.height = 120;
      }
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Animation variables
    let animationFrameId: number;
    let time = 0;

    // Draw waveform
    const drawWaveform = () => {
      if (!ctx || !canvas) return;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw waveform
      const centerY = canvas.height / 2;
      const amplitude = 30;
      const frequency = 0.02;
      const speed = 0.05;

      // Draw primary waveform
      ctx.beginPath();
      ctx.moveTo(0, centerY);

      for (let x = 0; x < canvas.width; x++) {
        // Create a complex waveform with multiple sine waves
        const y1 = Math.sin(x * frequency + time) * amplitude * 0.5;
        const y2 = Math.sin(x * frequency * 1.5 + time * 1.1) * amplitude * 0.3;
        const y3 = Math.sin(x * frequency * 0.5 + time * 0.9) * amplitude * 0.2;

        const y = centerY + y1 + y2 + y3;

        ctx.lineTo(x, y);
      }

      // Create gradient for primary wave
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient.addColorStop(0, 'rgba(99, 102, 241, 0.2)');
      gradient.addColorStop(0.5, 'rgba(99, 102, 241, 0.6)');
      gradient.addColorStop(1, 'rgba(99, 102, 241, 0.2)');

      ctx.strokeStyle = gradient;
      ctx.lineWidth = 2;
      ctx.stroke();

      // Draw secondary waveform (slightly offset)
      ctx.beginPath();
      ctx.moveTo(0, centerY);

      for (let x = 0; x < canvas.width; x++) {
        // Create a different pattern for the second wave
        const y1 = Math.sin(x * frequency * 0.8 + time * 1.2) * amplitude * 0.4;
        const y2 = Math.sin(x * frequency * 1.2 + time * 0.8) * amplitude * 0.2;

        const y = centerY + y1 + y2;

        ctx.lineTo(x, y);
      }

      // Create gradient for secondary wave
      const gradient2 = ctx.createLinearGradient(0, 0, canvas.width, 0);
      gradient2.addColorStop(0, 'rgba(139, 92, 246, 0.1)');  // Purple
      gradient2.addColorStop(0.5, 'rgba(139, 92, 246, 0.3)');
      gradient2.addColorStop(1, 'rgba(139, 92, 246, 0.1)');

      ctx.strokeStyle = gradient2;
      ctx.lineWidth = 1.5;
      ctx.stroke();

      // Add some particle effects
      const particleCount = 15;
      for (let i = 0; i < particleCount; i++) {
        const x = (canvas.width / particleCount) * i + Math.sin(time * 2 + i) * 5;
        const yOffset = Math.sin(time * 3 + i * 0.3) * amplitude * 0.7;
        const y = centerY + yOffset;
        const size = Math.abs(yOffset) / 10 + 1;

        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
        ctx.fill();
      }

      // Update time
      time += speed;

      // Continue animation
      animationFrameId = requestAnimationFrame(drawWaveform);
    };

    drawWaveform();

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="w-full h-[120px] opacity-70"
    />
  );
};

// Feature item component
interface FeatureItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay: number;
}

const FeatureItem: React.FC<FeatureItemProps> = ({ icon, title, description, delay }) => {
  return (
    <motion.div
      className="flex items-start mb-6"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: delay * 0.1 + 0.5 }}
    >
      <div className="flex-shrink-0 mr-4">
        <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center text-primary">
          {icon}
        </div>
      </div>
      <div>
        <h3 className="text-base font-semibold mb-1 text-white">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
    </motion.div>
  );
};

// Main component
const ModernAuthLayout: React.FC = () => {
  return (
    <main className="min-h-screen bg-background text-foreground flex flex-col md:flex-row">
      {/* Left panel - Product showcase */}
      <div className="md:flex md:w-1/2 bg-gradient-to-br from-indigo-950 via-indigo-900 to-violet-900 text-white relative overflow-hidden">
        {/* Mobile header - only visible on small screens */}
        <div className="md:hidden flex items-center justify-between p-6 border-b border-indigo-800">
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center mr-2">
              <Mic className="w-4 h-4 text-white" />
            </div>
            <h2 className="text-lg font-bold">VoiceAI</h2>
          </div>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-xs px-2 py-1 bg-white/10 rounded-full"
          >
            Enterprise Solution
          </motion.div>
        </div>
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-[10%] right-[-10%] w-[40%] h-[40%] rounded-full bg-indigo-600/10 blur-3xl"></div>
          <div className="absolute bottom-[10%] left-[-10%] w-[40%] h-[40%] rounded-full bg-violet-600/10 blur-3xl"></div>
          <div className="absolute top-[40%] left-[20%] w-[30%] h-[30%] rounded-full bg-indigo-500/10 blur-3xl"></div>
        </div>

        {/* Content container */}
        <div className="relative z-10 flex flex-col justify-between h-full w-full p-6 md:p-8 lg:p-12 md:block hidden">
          {/* Header */}
          <div>
            <motion.div
              className="flex items-center mb-6"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="w-10 h-10 rounded-full bg-white/10 flex items-center justify-center mr-3">
                <Mic className="w-5 h-5 text-white" />
              </div>
              <h2 className="text-xl font-bold">VoiceAI</h2>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <h1 className="text-3xl lg:text-4xl font-bold mb-3">Transform Your Business Communication</h1>
              <p className="text-lg text-indigo-100 mb-6">Intelligent voice agents that deliver exceptional customer experiences.</p>
            </motion.div>
          </div>

          {/* Voice waveform visualization */}
          <div className="my-8">
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              <VoiceWaveform />
            </motion.div>

            {/* AI Voice visualization dots */}
            <div className="flex items-center justify-center mt-4 space-x-2">
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 rounded-full bg-indigo-400"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.5, 1, 0.5]
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: "easeInOut"
                  }}
                />
              ))}
            </div>
          </div>

          {/* Features */}
          <div className="space-y-6">
            <FeatureItem
              icon={<Zap className="w-5 h-5" />}
              title="AI-Powered Voice Agents"
              description="Lifelike conversations that handle customer inquiries with natural flow."
              delay={1}
            />

            <FeatureItem
              icon={<Brain className="w-5 h-5" />}
              title="Advanced Natural Language Processing"
              description="Understands context, sentiment, and intent for human-like interactions."
              delay={2}
            />

            <FeatureItem
              icon={<Shield className="w-5 h-5" />}
              title="Enterprise-Grade Security"
              description="Secure handling of sensitive customer data with full compliance."
              delay={3}
            />
          </div>

          {/* Enterprise logos */}
          <motion.div
            className="mt-auto pt-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 1 }}
          >
            <p className="text-sm text-indigo-200 mb-3">Trusted by leading enterprises worldwide</p>

            {/* Enterprise logo placeholders */}
            <div className="flex items-center space-x-6">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-6 w-16 bg-white/10 rounded-md flex items-center justify-center">
                  <div className="w-10 h-2 bg-white/20 rounded-sm"></div>
                </div>
              ))}
            </div>

            {/* Security badges */}
            <div className="flex items-center mt-4 space-x-3">
              <div className="flex items-center text-xs text-indigo-200">
                <Shield className="w-3 h-3 mr-1" />
                <span>SOC 2</span>
              </div>
              <div className="flex items-center text-xs text-indigo-200">
                <Shield className="w-3 h-3 mr-1" />
                <span>GDPR</span>
              </div>
              <div className="flex items-center text-xs text-indigo-200">
                <Shield className="w-3 h-3 mr-1" />
                <span>HIPAA</span>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Mobile content - condensed version for small screens */}
        <div className="md:hidden p-6 pt-0">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-xl font-bold mb-2">Transform Your Business Communication</h1>
            <p className="text-sm text-indigo-100 mb-4">Intelligent voice agents for exceptional customer experiences.</p>
          </motion.div>

          {/* Condensed feature list */}
          <motion.div
            className="space-y-2 mb-6"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="flex items-center text-sm">
              <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center mr-2">
                <Zap className="w-3 h-3 text-white" />
              </div>
              <span>AI-Powered Voice Agents</span>
            </div>
            <div className="flex items-center text-sm">
              <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center mr-2">
                <Brain className="w-3 h-3 text-white" />
              </div>
              <span>Advanced NLP Technology</span>
            </div>
            <div className="flex items-center text-sm">
              <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center mr-2">
                <Shield className="w-3 h-3 text-white" />
              </div>
              <span>Enterprise-Grade Security</span>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Right panel - Authentication form */}
      <div className="flex-1 flex items-center justify-center p-6 md:p-10 relative">
        {/* Subtle background elements */}
        <div className="absolute top-0 right-0 w-full h-full overflow-hidden pointer-events-none">
          <div className="absolute top-[5%] right-[5%] w-[20%] h-[20%] rounded-full bg-primary/5 blur-3xl"></div>
          <div className="absolute bottom-[5%] left-[5%] w-[20%] h-[20%] rounded-full bg-primary/5 blur-3xl"></div>
        </div>

        {/* Form container with subtle animation */}
        <motion.div
          className="w-full max-w-md relative z-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Outlet />
        </motion.div>
      </div>
    </main>
  );
};

export default ModernAuthLayout;
