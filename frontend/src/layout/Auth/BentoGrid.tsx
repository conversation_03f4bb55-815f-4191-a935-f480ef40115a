import React from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>,
  Bar<PERSON>hart2,
  Calendar,
  Users,
  MessageSquare,
  <PERSON>,
  <PERSON>rk<PERSON>,
  <PERSON><PERSON>,
  Headphones
} from 'lucide-react';

interface BentoItemProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  className?: string;
  delay?: number;
  gradient?: string;
}

const BentoItem: React.FC<BentoItemProps> = ({
  title,
  description,
  icon,
  className = "",
  delay = 0,
  gradient = "from-primary/10 to-primary/5"
}) => {
  return (
    <motion.div
      className={`relative overflow-hidden rounded-xl p-4 md:p-6 border border-border/40 bg-gradient-to-br ${gradient} backdrop-blur-sm hover:shadow-md transition-all duration-300 ${className}`}
      variants={itemVariants}
      custom={delay}
      whileHover={{ scale: 1.02 }}
    >
      <div className="flex flex-col h-full">
        <div className="mb-3">
          <div className="inline-flex items-center justify-center w-10 h-10 rounded-lg bg-background/80 text-primary shadow-sm">
            {icon}
          </div>
        </div>
        <h3 className="text-lg font-semibold mb-2 text-foreground">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>

      {/* Decorative elements */}
      <div className="absolute -bottom-4 -right-4 w-24 h-24 rounded-full bg-background/10 blur-2xl pointer-events-none"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-br from-primary/5 to-transparent opacity-0 hover:opacity-30 transition-opacity duration-300 pointer-events-none"></div>
    </motion.div>
  );
};

const BentoGrid: React.FC = () => {
  return (
    <div className="w-full h-full flex flex-col justify-center p-6 md:p-8 lg:p-10 overflow-auto bg-gradient-to-br from-background via-background/95 to-background/90">
      <motion.div
        className="mb-8 text-center"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="inline-flex items-center justify-center mb-4 px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium"
        >
          <Sparkles className="w-4 h-4 mr-2" />
          AI-Powered Voice Solutions
        </motion.div>
        <h1 className="text-2xl md:text-3xl font-bold text-foreground mb-2">Transform Your Business Communication</h1>
        <p className="text-muted-foreground max-w-md mx-auto">Intelligent voice agents that handle calls, schedule meetings, and provide exceptional customer service.</p>
      </motion.div>

      <motion.div
        className="grid grid-cols-2 md:grid-cols-4 gap-4 auto-rows-fr"
        variants={floatingAnimation}
        initial="hidden"
        animate="visible"
      >
        <BentoItem
          title="AI Voice Agents"
          description="Lifelike voice agents that handle customer calls with natural conversation flow."
          icon={<Mic className="w-5 h-5" />}
          className="col-span-2 row-span-1"
          delay={1}
          gradient="from-indigo-500/10 to-indigo-500/5"
        />

        <BentoItem
          title="Analytics Dashboard"
          description="Comprehensive insights to track performance and customer interactions."
          icon={<BarChart2 className="w-5 h-5" />}
          className="col-span-2 row-span-1"
          delay={2}
          gradient="from-blue-500/10 to-blue-500/5"
        />

        <BentoItem
          title="Smart Scheduling"
          description="Automated appointment booking and calendar management."
          icon={<Calendar className="w-5 h-5" />}
          className="col-span-1 row-span-1"
          delay={3}
          gradient="from-green-500/10 to-green-500/5"
        />

        <BentoItem
          title="Contact Management"
          description="Organize and manage your customer database efficiently."
          icon={<Users className="w-5 h-5" />}
          className="col-span-1 row-span-1"
          delay={4}
          gradient="from-amber-500/10 to-amber-500/5"
        />

        <BentoItem
          title="Conversational AI"
          description="Advanced natural language processing for human-like interactions."
          icon={<Brain className="w-5 h-5" />}
          className="col-span-2 row-span-1"
          delay={5}
          gradient="from-purple-500/10 to-purple-500/5"
        />

        <BentoItem
          title="Multi-Channel Support"
          description="Seamless integration with phone, SMS, and messaging platforms."
          icon={<Headphones className="w-5 h-5" />}
          className="col-span-2 row-span-1"
          delay={6}
          gradient="from-rose-500/10 to-rose-500/5"
        />
      </motion.div>

      {/* Call to action */}
      <motion.div
        className="mt-8 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <p className="text-sm text-muted-foreground">Join thousands of businesses already using our AI voice solutions</p>
      </motion.div>
    </div>
  );
};

// Add a subtle floating animation to the grid
const floatingAnimation = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

// Item animation variants
const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: "easeOut"
    }
  })
};

// Export the component
export default BentoGrid;
