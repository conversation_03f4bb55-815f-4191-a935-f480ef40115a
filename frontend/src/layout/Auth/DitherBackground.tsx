import React, { useEffect, useRef } from 'react';

const DitherBackground: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas dimensions to match parent container
    const resizeCanvas = () => {
      const parent = canvas.parentElement;
      if (parent) {
        canvas.width = parent.clientWidth;
        canvas.height = parent.clientHeight;
        drawDitherPattern();
      }
    };

    // Draw the dither pattern
    const drawDitherPattern = () => {
      if (!ctx || !canvas) return;

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Create gradient background
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, '#4338ca'); // indigo-700
      gradient.addColorStop(0.5, '#6d28d9'); // purple-700
      gradient.addColorStop(1, '#1e40af'); // blue-800
      
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Draw dither pattern
      const patternSize = 4; // Size of each dither cell
      const opacity = 0.15; // Opacity of the pattern

      ctx.fillStyle = 'rgba(255, 255, 255, ' + opacity + ')';
      
      for (let y = 0; y < canvas.height; y += patternSize * 2) {
        for (let x = 0; x < canvas.width; x += patternSize * 2) {
          // Draw dither dots in a checkerboard pattern
          ctx.fillRect(x, y, patternSize, patternSize);
          ctx.fillRect(x + patternSize, y + patternSize, patternSize, patternSize);
        }
      }

      // Add some floating particles
      const particleCount = Math.floor(canvas.width * canvas.height / 10000);
      ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      
      for (let i = 0; i < particleCount; i++) {
        const x = Math.random() * canvas.width;
        const y = Math.random() * canvas.height;
        const size = Math.random() * 2 + 1;
        
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
      }

      // Add a subtle glow effect in the center
      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;
      const radius = Math.min(canvas.width, canvas.height) * 0.4;
      
      const glowGradient = ctx.createRadialGradient(
        centerX, centerY, 0,
        centerX, centerY, radius
      );
      
      glowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.1)');
      glowGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
      
      ctx.fillStyle = glowGradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
    };

    // Initial setup
    resizeCanvas();

    // Handle window resize
    window.addEventListener('resize', resizeCanvas);

    // Animation loop for subtle movement
    let animationFrameId: number;
    let time = 0;

    const animate = () => {
      time += 0.005;
      
      // Only redraw occasionally for performance
      if (Math.floor(time * 10) % 3 === 0) {
        drawDitherPattern();
      }
      
      animationFrameId = requestAnimationFrame(animate);
    };

    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  return (
    <div className="w-full h-full relative overflow-hidden">
      <canvas 
        ref={canvasRef} 
        className="absolute top-0 left-0 w-full h-full"
      />
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="text-white text-center p-8">
          <h1 className="text-4xl font-bold mb-4">AI Agent</h1>
          <p className="text-xl opacity-80">Intelligent voice solutions for your business</p>
        </div>
      </div>
    </div>
  );
};

export default DitherBackground;
