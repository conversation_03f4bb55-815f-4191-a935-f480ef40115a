import { Suspense, useRef } from "react";
import { <PERSON>vas, useFrame } from "@react-three/fiber";
import {
  OrbitControls,
  Environment,
  Html,
  Float,
  MeshDistortMaterial,
} from "@react-three/drei";
import * as THREE from "three";
import Loading from "@/Loading";

// Energy Flow Component
const EnergyFlow = ({ radius = 1, tubeRadius = 0.02, speed = 1 }) => {
  const flowRef = useRef<THREE.Mesh>(null!);

  useFrame((state) => {
    if (flowRef.current) {
      flowRef.current.rotation.x = state.clock.getElapsedTime() * 0.2 * speed;
      flowRef.current.rotation.y = state.clock.getElapsedTime() * 0.3 * speed;
    }
  });

  return (
    <mesh ref={flowRef}>
      <torusKnotGeometry args={[radius, tubeRadius, 256, 32, 2, 3]} />
      <MeshDistortMaterial
        color="#4F46E5"
        roughness={0.1}
        metalness={0.8}
        distort={0.3}
        speed={2}
      />
    </mesh>
  );
};

// Neural Sphere Component
const NeuralSphere = ({
  position = [0, 0, 0] as [number, number, number],
  size = 0.15,
}) => {
  const sphereRef = useRef<THREE.Mesh>(null!);

  useFrame((state) => {
    if (sphereRef.current) {
      sphereRef.current.scale.setScalar(
        size * (1 + Math.sin(state.clock.getElapsedTime() * 2) * 0.1)
      );
    }
  });

  return (
    <mesh ref={sphereRef} position={position}>
      <sphereGeometry args={[1, 64, 64]} />
      <meshPhysicalMaterial
        color="#818CF8"
        roughness={0.2}
        metalness={1}
        clearcoat={1}
        clearcoatRoughness={0.3}
        transmission={0.6}
      />
    </mesh>
  );
};

// Neural Network Component
const NeuralNetwork = () => {
  const positions = [
    [-0.5, 0.5, 0],
    [0.5, 0.5, 0],
    [-0.3, 0, 0.3],
    [0.3, 0, -0.3],
    [-0.4, -0.5, -0.2],
    [0.4, -0.5, 0.2],
  ];

  return (
    <group>
      {positions.map((pos, idx) => (
        <NeuralSphere
          key={idx}
          position={pos as [number, number, number]}
          size={0.15}
        />
      ))}
    </group>
  );
};

// Core Brain Component
const CoreBrain = () => {
  const brainRef = useRef<THREE.Group>(null!);

  useFrame((state) => {
    if (brainRef.current) {
      brainRef.current.rotation.y =
        Math.sin(state.clock.getElapsedTime() * 0.3) * 0.2;
    }
  });

  return (
    <group ref={brainRef}>
      {/* Main Core */}
      <mesh>
        <sphereGeometry args={[0.7, 128, 128]} />
        <meshPhysicalMaterial
          color="#6366F1"
          roughness={0.1}
          metalness={0.9}
          clearcoat={1}
          clearcoatRoughness={0.1}
          iridescence={1}
          iridescenceIOR={1.5}
        />
      </mesh>

      {/* Energy Flows */}
      <EnergyFlow radius={1.2} speed={1} />
      <EnergyFlow radius={1.4} speed={-0.8} />
      <EnergyFlow radius={1.6} speed={0.6} />

      {/* Neural Network */}
      <NeuralNetwork />
    </group>
  );
};

// Main Scene Component
const AuthVideoComponent = () => {
  return (
    <div className="hidden md:flex items-center justify-center w-full h-full bg-gradient-to-br from-indigo-900 via-purple-900 to-blue-900">
      <Canvas camera={{ position: [0, 0, 4], fov: 45 }}>
        <ambientLight intensity={0.2} />
        <spotLight
          position={[10, 10, 10]}
          angle={0.15}
          penumbra={1}
          intensity={1}
          castShadow
        />
        <pointLight position={[-10, -10, -10]} intensity={0.5} />

        <Suspense
          fallback={
            <Html>
              <Loading />
            </Html>
          }
        >
          <Float speed={1.2} rotationIntensity={0.5} floatIntensity={0.5}>
            <CoreBrain />
          </Float>
          <Environment preset="warehouse" />
        </Suspense>

        <OrbitControls
          enableZoom={false}
          enablePan={false}
          maxPolarAngle={Math.PI / 1.5}
          minPolarAngle={Math.PI / 2.5}
        />
      </Canvas>
    </div>
  );
};

export default AuthVideoComponent;

// import { Suspense, useRef } from "react";
// import { Canvas, useFrame } from "@react-three/fiber";
// import { OrbitControls, Environment, Html } from "@react-three/drei";
// import * as THREE from "three";
// import Loading from "@/Loading";

// // Rotating Lock Component
// const RotatingLock = () => {
//   const lockRef = useRef<THREE.Mesh>(null!);

//   // Rotate the lock continuously
//   useFrame(() => {
//     if (lockRef.current) {
//       lockRef.current.rotation.y += 0.005;
//       lockRef.current.rotation.x += 0.0025;
//     }
//   });

//   return (
//     <mesh ref={lockRef} position={[0, 0, 0]}>
//       {/* Lock Body */}
//       <cylinderGeometry args={[0.5, 0.5, 1, 32]} />
//       <meshStandardMaterial color="#4F46E5" />
//       {/* Lock Shackle */}
//       <mesh position={[0, 0.75, 0]}>
//         <ringGeometry args={[0.3, 0.35, 32]} />
//         <meshStandardMaterial color="#4F46E5" side={THREE.DoubleSide} />
//       </mesh>
//     </mesh>
//   );
// };

// // AuthVideoComponent
// const AuthVideoComponent = () => {
//   return (
//     <div className="hidden md:flex items-center justify-center bg-gradient-to-br from-indigo-500 to-purple-600">
//       <Canvas camera={{ position: [0, 0, 3] }}>
//         <ambientLight intensity={0.5} />
//         <pointLight position={[10, 10, 10]} />
//         <Suspense
//           fallback={
//             <Html>
//               <Loading />
//             </Html>
//           }
//         >
//           <RotatingLock />
//           <Environment preset="sunset" />
//         </Suspense>
//         <OrbitControls enableZoom={false} />
//       </Canvas>
//     </div>
//   );
// };

// export default AuthVideoComponent;
