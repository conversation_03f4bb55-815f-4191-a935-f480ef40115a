import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Share2, MessageSquare, Tag, Phone, Mail, Users } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";

interface ContactGridProps {
  data: any[];
  onItemClick: (item: any) => void;
  actions: {
    label: string;
    icon: React.ReactNode;
    onClick: (item: any) => void;
  }[];
  getInitials: (email: string) => string;
  formatPhoneNumber: (phone: string) => string;
  getListBadgeVariant: (listName: string) => string;
}

const ContactGrid: React.FC<ContactGridProps> = ({ 
  data, 
  onItemClick, 
  actions,
  getInitials,
  formatPhoneNumber,
  getListBadgeVariant
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-6">
      {data.map((item) => {
        const initials = getInitials(item.email);
        
        // This is just for demo - in a real app you'd have a proper status field
        const statuses = ["Active", "Inactive", "New"];
        const statusVariants = ["success", "secondary", "info"];
        const index = String(item.id).charCodeAt(0) % 3;
        
        return (
          <Card 
            key={item.id} 
            className="overflow-hidden border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
            onClick={() => onItemClick(item)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10 bg-gradient-to-br from-primary/20 to-primary/10">
                    <AvatarFallback className="text-primary font-medium">
                      {initials}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium text-gray-900 line-clamp-1">
                      {item.email}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: {String(item.id).substring(0, 8)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Message", item);
                    }}
                  >
                    <MessageSquare className="h-4 w-4 text-gray-500" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Share", item);
                    }}
                  >
                    <Share2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                      >
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[160px]">
                      {actions.map((action, index) => (
                        <DropdownMenuItem
                          key={index}
                          onClick={(e) => {
                            e.stopPropagation();
                            action.onClick(item);
                          }}
                          className="cursor-pointer"
                        >
                          <span className="mr-2 h-4 w-4 text-gray-500">
                            {action.icon}
                          </span>
                          <span>{action.label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              <div className="grid grid-cols-1 gap-3 mb-3">
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-700">
                    {formatPhoneNumber(item.contact_number)}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-700 line-clamp-1">
                    {item.email}
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">List</div>
                  {item.list_name ? (
                    <Badge
                      variant={getListBadgeVariant(item.list_name)}
                      className="capitalize"
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      {item.list_name}
                    </Badge>
                  ) : (
                    <span className="text-xs text-gray-500">No list</span>
                  )}
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Status</div>
                  <Badge
                    variant={statusVariants[index] as any}
                    className="capitalize"
                  >
                    {statuses[index]}
                  </Badge>
                </div>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default ContactGrid;
