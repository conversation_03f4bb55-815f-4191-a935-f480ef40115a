import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Share2, PlayCircle, Zap } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface ActionGridProps {
  data: any[];
  onItemClick: (item: any) => void;
  actions: {
    label: string;
    icon: React.ReactNode;
    onClick: (item: any) => void;
  }[];
  getActionTypeInfo: (actionName: string) => {
    icon: React.ReactNode;
    badge: string;
  };
}

const ActionGrid: React.FC<ActionGridProps> = ({ 
  data, 
  onItemClick, 
  actions,
  getActionTypeInfo
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-6">
      {data.map((item) => {
        const { icon, badge } = getActionTypeInfo(item.actionName);
        const type = item.actionName.split(" ")[0]; // Get first word as type
        
        // This is just for demo - in a real app you'd have a proper status field
        const statuses = ["Active", "Inactive", "Draft"];
        const statusVariants = ["success", "secondary", "warning"];
        const index = Number(item.id) % 3;
        
        return (
          <Card 
            key={item.id} 
            className="overflow-hidden border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
            onClick={() => onItemClick(item)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                    {icon}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 line-clamp-1">
                      {item.actionName}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: ACT-{String(item.id).padStart(4, "0")}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Run", item);
                    }}
                  >
                    <PlayCircle className="h-4 w-4 text-gray-500" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Share", item);
                    }}
                  >
                    <Share2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                      >
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[160px]">
                      {actions.map((action, index) => (
                        <DropdownMenuItem
                          key={index}
                          onClick={(e) => {
                            e.stopPropagation();
                            action.onClick(item);
                          }}
                          className="cursor-pointer"
                        >
                          <span className="mr-2 h-4 w-4 text-gray-500">
                            {action.icon}
                          </span>
                          <span>{action.label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              <div className="mb-3 text-sm text-gray-600 line-clamp-2">
                {item.description}
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Type</div>
                  <Badge
                    variant={badge as any}
                    className="capitalize"
                  >
                    {type}
                  </Badge>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Status</div>
                  <Badge
                    variant={statusVariants[index] as any}
                    className="capitalize"
                  >
                    {statuses[index]}
                  </Badge>
                </div>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default ActionGrid;
