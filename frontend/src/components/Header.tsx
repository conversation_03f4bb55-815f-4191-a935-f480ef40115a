// src/components/Header.tsx

import React from "react";
import {
  PlusIcon,
  SearchIcon,
  LayoutGrid,
  List,
  SlidersHorizontal,
} from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Sheet, SheetTrigger, SheetContent } from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface HeaderProps {
  title: string;
  buttonText: string;
  action?: () => void;
  filterByName: boolean;
  filterWord?: string;
  onFilterChange?: (value: string) => void;
  useSheet?: boolean;
  sheetContent?: React.ReactNode;
  sheetSize?: "sm" | "md" | "lg" | "xl" | "2xl";
  viewMode?: "list" | "grid";
  onViewModeChange?: (mode: "list" | "grid") => void;
  showViewToggle?: boolean;
  showFilters?: boolean;
}

const Header = ({
  title,
  buttonText,
  action,
  filterByName,
  filterWord,
  onFilterChange,
  useSheet = false,
  sheetContent,
  sheetSize = "sm",
  viewMode = "list",
  onViewModeChange,
  showViewToggle = false,
  showFilters = false,
}: HeaderProps) => {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 px-2">
      <h1 className="text-xl sm:text-2xl font-bold tracking-wide mb-4 sm:mb-0 jakarta text-gray-700">
        {title}
      </h1>

      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 sm:gap-4 mt-4 sm:mt-0">
        {filterByName && (
          <div className="relative w-full sm:w-auto">
            <input
              type="text"
              placeholder="Search products by name or keyword..."
              className="border border-gray-200 bg-white rounded-md py-2 px-4 pl-10 text-gray-800 focus:outline-none focus:ring-1 focus:ring-primary/30 focus:border-primary/50 w-full sm:w-[300px] text-sm"
              value={filterWord}
              onChange={(e) => onFilterChange && onFilterChange(e.target.value)}
            />
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
        )}

        {showFilters && (
          <Button
            variant="outline"
            size="sm"
            className="h-9 border-gray-200 text-gray-700 hover:bg-gray-50"
          >
            <SlidersHorizontal className="h-4 w-4 mr-1" />
            Filters
          </Button>
        )}

        {showViewToggle && (
          <div className="flex items-center border border-gray-200 rounded-md overflow-hidden h-9">
            <Button
              variant="ghost"
              size="sm"
              className={`h-full px-3 rounded-none ${
                viewMode === "list"
                  ? "bg-gray-100 text-gray-900"
                  : "text-gray-500 hover:bg-gray-50"
              }`}
              onClick={() => onViewModeChange && onViewModeChange("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className={`h-full px-3 rounded-none ${
                viewMode === "grid"
                  ? "bg-gray-100 text-gray-900"
                  : "text-gray-500 hover:bg-gray-50"
              }`}
              onClick={() => onViewModeChange && onViewModeChange("grid")}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
          </div>
        )}

        {useSheet && sheetContent ? (
          <Sheet>
            <SheetTrigger asChild>
              <Button
                className="bg-primary hover:bg-primary/90 flex items-center justify-center gap-2 w-full sm:w-auto text-white shadow-sm rounded-md h-9"
                variant="default"
              >
                <PlusIcon className="w-4 h-4" />
                <span className="font-medium">Add {buttonText}</span>
              </Button>
            </SheetTrigger>
            <SheetContent size={sheetSize}>{sheetContent}</SheetContent>
          </Sheet>
        ) : (
          <Button
            className="bg-primary hover:bg-primary/90 flex items-center justify-center gap-2 w-full sm:w-auto text-white shadow-sm rounded-md h-9"
            variant="default"
            onClick={action}
          >
            <PlusIcon className="w-4 h-4" />
            <span className="font-medium">Add {buttonText}</span>
          </Button>
        )}
      </div>
    </div>
  );
};

export default Header;
