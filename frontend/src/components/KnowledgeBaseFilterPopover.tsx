import React, { useState } from "react";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { SlidersHorizontal } from "lucide-react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

interface FilterOption {
  id: string;
  label: string;
}

interface KnowledgeBaseFilterPopoverProps {
  onFilterChange: (filters: any) => void;
}

const KnowledgeBaseFilterPopover: React.FC<KnowledgeBaseFilterPopoverProps> = ({ onFilterChange }) => {
  const [open, setOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [viewsRange, setViewsRange] = useState([0, 2000]);
  
  const categoryOptions: FilterOption[] = [
    { id: "onboarding", label: "Onboarding" },
    { id: "features", label: "Features" },
    { id: "support", label: "Support" },
    { id: "faq", label: "FAQ" },
    { id: "other", label: "Other" },
  ];

  const handleApplyFilters = () => {
    const filters = {
      category: categoryFilter,
      viewsRange,
    };
    
    // Update active filters for badge display
    const newActiveFilters = [];
    if (categoryFilter) newActiveFilters.push(`Category: ${categoryFilter}`);
    if (viewsRange[0] > 0 || viewsRange[1] < 2000) 
      newActiveFilters.push(`Views: ${viewsRange[0]} - ${viewsRange[1]}`);
    
    setActiveFilters(newActiveFilters);
    onFilterChange(filters);
    setOpen(false);
  };

  const handleResetFilters = () => {
    setCategoryFilter(null);
    setViewsRange([0, 2000]);
    setActiveFilters([]);
    onFilterChange({});
  };

  const removeFilter = (filter: string) => {
    const newActiveFilters = activeFilters.filter(f => f !== filter);
    setActiveFilters(newActiveFilters);
    
    // Reset the corresponding filter
    if (filter.startsWith("Category:")) setCategoryFilter(null);
    if (filter.startsWith("Views:")) setViewsRange([0, 2000]);
    
    onFilterChange({
      category: filter.startsWith("Category:") ? null : categoryFilter,
      viewsRange: filter.startsWith("Views:") ? [0, 2000] : viewsRange,
    });
  };

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={`h-9 border-gray-200 text-gray-700 hover:bg-gray-50 ${activeFilters.length > 0 ? 'border-primary/50 bg-primary/5' : ''}`}
          >
            <SlidersHorizontal className="h-4 w-4 mr-1" />
            Filters
            {activeFilters.length > 0 && (
              <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] font-medium text-white">
                {activeFilters.length}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[280px] p-4" align="end">
          <div className="space-y-4">
            <h4 className="font-medium text-sm">Filter Knowledge Bases</h4>
            
            <div className="space-y-2">
              <Label className="text-xs font-medium">Category</Label>
              <RadioGroup 
                value={categoryFilter || ""} 
                onValueChange={(value) => setCategoryFilter(value || null)}
              >
                {categoryOptions.map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.id} id={`category-${option.id}`} />
                    <Label htmlFor={`category-${option.id}`} className="text-sm font-normal cursor-pointer">
                      {option.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-xs font-medium">Views Range</Label>
                <span className="text-xs text-gray-500">
                  {viewsRange[0]} - {viewsRange[1]}
                </span>
              </div>
              <Slider
                defaultValue={[0, 2000]}
                value={viewsRange}
                max={2000}
                step={100}
                onValueChange={setViewsRange}
                className="py-2"
              />
            </div>
            
            <Separator />
            
            <div className="flex justify-between">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleResetFilters}
                className="text-xs h-8"
              >
                Reset
              </Button>
              <Button 
                size="sm" 
                onClick={handleApplyFilters}
                className="text-xs h-8 bg-primary"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Active filters display */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {activeFilters.map((filter) => (
            <Badge 
              key={filter} 
              variant="outline" 
              className="px-2 py-1 text-xs bg-primary/5 border-primary/20 text-primary flex items-center gap-1"
            >
              {filter}
              <button 
                className="ml-1 text-primary hover:text-primary/80"
                onClick={() => removeFilter(filter)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

export default KnowledgeBaseFilterPopover;
