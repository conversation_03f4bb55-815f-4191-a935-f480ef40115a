import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Share2, BarChart2, Megaphone, Calendar, PlayCircle, CheckCircle, Clock, PauseCircle, Edit } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface CampaignGridProps {
  data: any[];
  onItemClick: (item: any) => void;
  actions: {
    label: string;
    icon: React.ReactNode;
    onClick: (item: any) => void;
  }[];
  formatDate: (dateString: string) => string;
  getStatusBadgeVariant: (status: string) => string;
  getStatusIcon: (status: string) => string;
  getCampaignType: (campaignName: string) => string;
}

const CampaignGrid: React.FC<CampaignGridProps> = ({
  data,
  onItemClick,
  actions,
  formatDate,
  getStatusBadgeVariant,
  getStatusIcon,
  getCampaignType
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-6">
      {data.map((item) => {
        const campaignType = getCampaignType(item.campaignName);
        const status = String(item.status).toLowerCase();
        
        // Calculate progress based on status
        let progress = 0;
        if (status === "completed") progress = 100;
        else if (status === "active") {
          // Calculate progress based on dates
          const start = new Date(String(item.startDate));
          const end = new Date(String(item.endDate));
          const now = new Date();

          if (now < start) progress = 0;
          else if (now > end) progress = 100;
          else {
            const total = end.getTime() - start.getTime();
            const current = now.getTime() - start.getTime();
            progress = Math.round((current / total) * 100);
          }
        } else if (status === "paused")
          progress = Math.round(Number(item.id) * 17) % 100;
        else if (status === "scheduled") progress = 0;
        else if (status === "draft") progress = 0;
        
        return (
          <Card 
            key={item.id} 
            className="overflow-hidden border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
            onClick={() => onItemClick(item)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                    <Megaphone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 line-clamp-1">
                      {item.campaignName}
                    </div>
                    <div className="text-xs text-gray-500">
                      Type: {campaignType}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("View Analytics", item);
                    }}
                  >
                    <BarChart2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Share", item);
                    }}
                  >
                    <Share2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                      >
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[160px]">
                      {actions.map((action, index) => (
                        <DropdownMenuItem
                          key={index}
                          onClick={(e) => {
                            e.stopPropagation();
                            action.onClick(item);
                          }}
                          className="cursor-pointer"
                        >
                          <span className="mr-2 h-4 w-4 text-gray-500">
                            {action.icon}
                          </span>
                          <span>{action.label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              <div className="mb-3">
                <Badge
                  variant={getStatusBadgeVariant(item.status)}
                  className="capitalize"
                >
                  {getStatusIcon(item.status)}
                  {item.status}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Start Date</div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-700">
                      {formatDate(item.startDate)}
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">End Date</div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-700">
                      {formatDate(item.endDate)}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="w-full">
                <div className="flex justify-between text-xs mb-1">
                  <span>{progress}% Complete</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default CampaignGrid;