import React from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Share2, Mail, ListFilter, Users, Calendar } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface ListGridProps {
  data: any[];
  onItemClick: (item: any) => void;
  actions: {
    label: string;
    icon: React.ReactNode;
    onClick: (item: any) => void;
  }[];
  getListType: (listName: string) => string;
  getListBadgeVariant: (listType: string) => string;
  formatDate: (dateString: string) => string;
}

const ListGrid: React.FC<ListGridProps> = ({ 
  data, 
  onItemClick, 
  actions,
  getListType,
  getListBadgeVariant,
  formatDate
}) => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-6">
      {data.map((item) => {
        const listType = getListType(item.listName);
        
        return (
          <Card 
            key={item.id} 
            className="overflow-hidden border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
            onClick={() => onItemClick(item)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                    <ListFilter className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 line-clamp-1">
                      {item.listName}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: LIST-{String(item.id).padStart(4, "0")}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Send Campaign", item);
                    }}
                  >
                    <Mail className="h-4 w-4 text-gray-500" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Share", item);
                    }}
                  >
                    <Share2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                      >
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[160px]">
                      {actions.map((action, index) => (
                        <DropdownMenuItem
                          key={index}
                          onClick={(e) => {
                            e.stopPropagation();
                            action.onClick(item);
                          }}
                          className="cursor-pointer"
                        >
                          <span className="mr-2 h-4 w-4 text-gray-500">
                            {action.icon}
                          </span>
                          <span>{action.label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              <div className="mb-3">
                <Badge
                  variant={getListBadgeVariant(listType)}
                  className="capitalize"
                >
                  {listType}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-3">
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Contacts</div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-3 w-3 text-gray-400" />
                    <span className="font-medium text-gray-900">
                      {Number(item.contactsCount).toLocaleString()}
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Created</div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-700">
                      {formatDate(item.created_at)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default ListGrid;
