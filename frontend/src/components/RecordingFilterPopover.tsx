import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Check, ChevronDown, Clock, Filter, Tag, X, Calendar as CalendarIcon } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

interface RecordingFilterPopoverProps {
  onFilterChange: (filters: any) => void;
}

const RecordingFilterPopover: React.FC<RecordingFilterPopoverProps> = ({ onFilterChange }) => {
  const [open, setOpen] = useState(false);
  const [type, setType] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [duration, setDuration] = useState<[number, number]>([0, 60]); // Duration in minutes

  // Recording types
  const recordingTypes = [
    { value: "sales", label: "Sales" },
    { value: "support", label: "Support" },
    { value: "follow-up", label: "Follow-up" },
    { value: "general", label: "General" },
  ];

  // Apply filters
  const applyFilters = () => {
    onFilterChange({
      type: type,
      dateRange: dateRange,
      duration: duration,
    });
    setOpen(false);
  };

  // Reset filters
  const resetFilters = () => {
    setType(null);
    setDateRange({ from: undefined, to: undefined });
    setDuration([0, 60]);
    onFilterChange({});
    setOpen(false);
  };

  // Format duration for display
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="h-9 border-dashed flex items-center gap-1"
          >
            <Filter className="h-3.5 w-3.5" />
            <span>Filter</span>
            {(type || dateRange.from || duration[0] > 0 || duration[1] < 60) && (
              <Badge
                variant="secondary"
                className="rounded-sm px-1 font-normal lg:hidden"
              >
                {type ? 1 : 0} +
                {dateRange.from ? 1 : 0} +
                {(duration[0] > 0 || duration[1] < 60) ? 1 : 0}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[340px] p-0" align="start">
          <div className="p-4 pb-2">
            <div className="space-y-2">
              <h4 className="font-medium leading-none">Filters</h4>
              <p className="text-sm text-muted-foreground">
                Filter recordings by type, date, and duration
              </p>
            </div>
          </div>
          <div className="p-4 pt-2 space-y-4">
            {/* Recording Type Filter */}
            <div className="space-y-2">
              <div className="flex items-center">
                <Tag className="h-4 w-4 mr-2 text-primary" />
                <h4 className="text-sm font-medium">Recording Type</h4>
              </div>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    className="w-full justify-between"
                  >
                    {type
                      ? recordingTypes.find((item) => item.value === type)?.label
                      : "Select type"}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Search type..." />
                    <CommandEmpty>No type found.</CommandEmpty>
                    <CommandGroup>
                      <CommandList>
                        {recordingTypes.map((item) => (
                          <CommandItem
                            key={item.value}
                            value={item.value}
                            onSelect={(currentValue) => {
                              setType(
                                currentValue === type ? null : currentValue
                              );
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                type === item.value
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                            {item.label}
                          </CommandItem>
                        ))}
                      </CommandList>
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Date Range Filter */}
            <div className="space-y-2">
              <div className="flex items-center">
                <CalendarIcon className="h-4 w-4 mr-2 text-primary" />
                <h4 className="text-sm font-medium">Date Range</h4>
              </div>
              <div className="grid gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !dateRange.from && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y")} -{" "}
                            {format(dateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Select date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="center">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Duration Filter */}
            <div className="space-y-2">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-primary" />
                <h4 className="text-sm font-medium">Duration (minutes)</h4>
              </div>
              <div className="pt-4 px-1">
                <Slider
                  defaultValue={[0, 60]}
                  value={duration}
                  max={60}
                  step={1}
                  onValueChange={setDuration}
                  className="w-full"
                />
                <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                  <span>{formatDuration(duration[0])}</span>
                  <span>{formatDuration(duration[1])}</span>
                </div>
              </div>
            </div>

            {/* Active Filters */}
            {(type || dateRange.from || duration[0] > 0 || duration[1] < 60) && (
              <div className="border-t pt-4 mt-4">
                <h4 className="text-sm font-medium mb-2">Active Filters:</h4>
                <div className="flex flex-wrap gap-1">
                  {type && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      Type: {recordingTypes.find((item) => item.value === type)?.label}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => setType(null)}
                      />
                    </Badge>
                  )}
                  {dateRange.from && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      Date Range
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() =>
                          setDateRange({ from: undefined, to: undefined })
                        }
                      />
                    </Badge>
                  )}
                  {(duration[0] > 0 || duration[1] < 60) && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      Duration: {formatDuration(duration[0])} - {formatDuration(duration[1])}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => setDuration([0, 60])}
                      />
                    </Badge>
                  )}
                </div>
              </div>
            )}

            <div className="flex items-center justify-between pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={resetFilters}
                className="text-xs h-8"
              >
                Reset Filters
              </Button>
              <Button
                size="sm"
                onClick={applyFilters}
                className="text-xs h-8 bg-primary text-primary-foreground hover:bg-primary/90"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default RecordingFilterPopover;