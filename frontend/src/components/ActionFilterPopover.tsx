import React, { useState } from "react";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { SlidersHorizontal } from "lucide-react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

interface FilterOption {
  id: string;
  label: string;
}

interface ActionFilterPopoverProps {
  onFilterChange: (filters: any) => void;
}

const ActionFilterPopover: React.FC<ActionFilterPopoverProps> = ({ onFilterChange }) => {
  const [open, setOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [typeFilter, setTypeFilter] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  
  const typeOptions: FilterOption[] = [
    { id: "Send", label: "Send Email" },
    { id: "Schedule", label: "Schedule Meeting" },
    { id: "Make", label: "Make Call" },
    { id: "Set", label: "Set Reminder" },
    { id: "Send", label: "Send Alert" },
  ];
  
  const statusOptions: FilterOption[] = [
    { id: "Active", label: "Active" },
    { id: "Inactive", label: "Inactive" },
    { id: "Draft", label: "Draft" },
  ];

  const handleApplyFilters = () => {
    const filters = {
      type: typeFilter,
      status: statusFilter,
    };
    
    // Update active filters for badge display
    const newActiveFilters = [];
    if (typeFilter) newActiveFilters.push(`Type: ${typeFilter}`);
    if (statusFilter) newActiveFilters.push(`Status: ${statusFilter}`);
    
    setActiveFilters(newActiveFilters);
    onFilterChange(filters);
    setOpen(false);
  };

  const handleResetFilters = () => {
    setTypeFilter(null);
    setStatusFilter(null);
    setActiveFilters([]);
    onFilterChange({});
  };

  const removeFilter = (filter: string) => {
    const newActiveFilters = activeFilters.filter(f => f !== filter);
    setActiveFilters(newActiveFilters);
    
    // Reset the corresponding filter
    if (filter.startsWith("Type:")) setTypeFilter(null);
    if (filter.startsWith("Status:")) setStatusFilter(null);
    
    onFilterChange({
      type: filter.startsWith("Type:") ? null : typeFilter,
      status: filter.startsWith("Status:") ? null : statusFilter,
    });
  };

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={`h-9 border-gray-200 text-gray-700 hover:bg-gray-50 ${activeFilters.length > 0 ? 'border-primary/50 bg-primary/5' : ''}`}
          >
            <SlidersHorizontal className="h-4 w-4 mr-1" />
            Filters
            {activeFilters.length > 0 && (
              <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] font-medium text-white">
                {activeFilters.length}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[280px] p-4" align="end">
          <div className="space-y-4">
            <h4 className="font-medium text-sm">Filter Actions</h4>
            
            <div className="space-y-2">
              <Label className="text-xs font-medium">Action Type</Label>
              <RadioGroup 
                value={typeFilter || ""} 
                onValueChange={(value) => setTypeFilter(value || null)}
              >
                {typeOptions.map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.id} id={`type-${option.id}`} />
                    <Label htmlFor={`type-${option.id}`} className="text-sm font-normal cursor-pointer">
                      {option.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
            
            <div className="space-y-2">
              <Label className="text-xs font-medium">Status</Label>
              <RadioGroup 
                value={statusFilter || ""} 
                onValueChange={(value) => setStatusFilter(value || null)}
              >
                {statusOptions.map((option) => (
                  <div key={option.id} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.id} id={`status-${option.id}`} />
                    <Label htmlFor={`status-${option.id}`} className="text-sm font-normal cursor-pointer">
                      {option.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
            
            <Separator />
            
            <div className="flex justify-between">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleResetFilters}
                className="text-xs h-8"
              >
                Reset
              </Button>
              <Button 
                size="sm" 
                onClick={handleApplyFilters}
                className="text-xs h-8 bg-primary"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
      
      {/* Active filters display */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {activeFilters.map((filter) => (
            <Badge 
              key={filter} 
              variant="outline" 
              className="px-2 py-1 text-xs bg-primary/5 border-primary/20 text-primary flex items-center gap-1"
            >
              {filter}
              <button 
                className="ml-1 text-primary hover:text-primary/80"
                onClick={() => removeFilter(filter)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

export default ActionFilterPopover;
