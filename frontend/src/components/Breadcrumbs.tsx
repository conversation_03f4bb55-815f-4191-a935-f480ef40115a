import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  ChevronRight,
  Home,
  LayoutDashboard,
  Bot,
  BookOpen,
  Waypoints,
  Users,
  ListTree,
  Megaphone,
  FileAudio,
  HelpCircle,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

// Define route mappings for display names and icons
interface RouteInfo {
  path: string;
  label: string;
  icon?: React.ReactNode;
}

// Map of routes to their display names and icons
const routeMap: Record<string, RouteInfo> = {
  '': { path: '/', label: 'Home', icon: <Home className="h-3.5 w-3.5" /> },
  'dashboard': { path: '/dashboard', label: 'Dashboard', icon: <LayoutDashboard className="h-3.5 w-3.5" /> },
  'agents': { path: '/agents', label: 'Agents', icon: <Bot className="h-3.5 w-3.5" /> },
  'knowledge-bases': { path: '/knowledge-bases', label: 'Knowledge Bases', icon: <BookOpen className="h-3.5 w-3.5" /> },
  'actions': { path: '/actions', label: 'Actions', icon: <Waypoints className="h-3.5 w-3.5" /> },
  'contacts': { path: '/contacts', label: 'Contacts', icon: <Users className="h-3.5 w-3.5" /> },
  'lists': { path: '/lists', label: 'Lists', icon: <ListTree className="h-3.5 w-3.5" /> },
  'campaigns': { path: '/campaigns', label: 'Campaigns', icon: <Megaphone className="h-3.5 w-3.5" /> },
  'recordings': { path: '/recordings', label: 'Recordings', icon: <FileAudio className="h-3.5 w-3.5" /> },
  'support': { path: '/support', label: 'Support', icon: <HelpCircle className="h-3.5 w-3.5" /> },
  'settings': { path: '/settings', label: 'Settings', icon: <Settings className="h-3.5 w-3.5" /> },
};

interface BreadcrumbsProps {
  className?: string;
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ className }) => {
  const location = useLocation();

  // Split the pathname into segments and filter out empty segments
  const pathSegments = location.pathname.split('/').filter(segment => segment);

  // If we're at the root, just show Home
  if (pathSegments.length === 0) {
    return (
      <nav className={cn("flex items-center space-x-1 text-sm", className)} aria-label="Breadcrumbs">
        <span className="flex items-center text-primary font-medium">
          <Home className="h-3.5 w-3.5 mr-1" />
          <span>Home</span>
        </span>
      </nav>
    );
  }

  // Build breadcrumb items
  const breadcrumbItems = pathSegments.map((segment, index) => {
    // Build the path up to this segment
    const path = '/' + pathSegments.slice(0, index + 1).join('/');

    // Get display info from our map, or use the segment as is
    const routeInfo = routeMap[segment] || { path, label: segment.charAt(0).toUpperCase() + segment.slice(1), icon: null };

    // Determine if this is the last item (current page)
    const isLast = index === pathSegments.length - 1;

    return {
      path: routeInfo.path,
      label: routeInfo.label,
      icon: routeInfo.icon,
      isLast
    };
  });

  // Add Home as the first item
  breadcrumbItems.unshift({
    path: '/',
    label: 'Home',
    icon: <Home className="h-3.5 w-3.5" />,
    isLast: false
  });

  return (
    <nav
      className={cn(
        "flex items-center text-sm overflow-x-auto scrollbar-hide",
        className
      )}
      aria-label="Breadcrumbs"
    >
      <div className="flex items-center space-x-1.5">
        {breadcrumbItems.map((item, index) => (
          <React.Fragment key={item.path}>
            {index > 0 && (
              <ChevronRight className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0 opacity-70" />
            )}

            {item.isLast ? (
              <span className="flex items-center text-foreground font-medium px-2 py-1 rounded-md bg-secondary/40">
                {item.icon && (
                  <span className="mr-1.5 text-primary">{item.icon}</span>
                )}
                <span className="truncate max-w-[150px]">{item.label}</span>
              </span>
            ) : (
              <motion.div
                whileHover={{ scale: 1.02 }}
                transition={{ duration: 0.2 }}
                className="group"
              >
                <Link
                  to={item.path}
                  className="flex items-center text-muted-foreground hover:text-primary transition-colors px-2 py-1 rounded-md hover:bg-secondary/50"
                >
                  {item.icon && (
                    <span className="mr-1.5 opacity-70 group-hover:opacity-100 text-muted-foreground group-hover:text-primary transition-colors">{item.icon}</span>
                  )}
                  <span className="truncate max-w-[120px]">{item.label}</span>
                </Link>
              </motion.div>
            )}
          </React.Fragment>
        ))}
      </div>
    </nav>
  );
};

export default Breadcrumbs;
