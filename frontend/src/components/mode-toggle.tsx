import { Sun } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "@/components/theme-provider";
import { cn } from "@/lib/utils";

interface ModeToggleProps {
  showLabel?: boolean;
}

export function ModeToggle({ showLabel = false }: ModeToggleProps) {
  const { theme } = useTheme();

  if (showLabel) {
    return (
      <div className="flex items-center gap-2">
        <Sun className="h-4 w-4 text-primary" />
        <span>Light Mode</span>
      </div>
    );
  }

  return (
    <Button
      variant="outline"
      size="icon"
      className="cursor-default border-border/50 bg-secondary/50 backdrop-blur-sm hover:bg-secondary/80 transition-colors"
    >
      <Sun className="h-[1.2rem] w-[1.2rem] text-primary" />
      <span className="sr-only">Light theme</span>
    </Button>
  );
}
