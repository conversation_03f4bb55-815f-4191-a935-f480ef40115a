import React from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';

interface ModernStatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  trend?: {
    value: string | number;
    direction: 'up' | 'down' | 'neutral';
    label: string;
  };
  iconColor?: string;
  iconBgColor?: string;
  className?: string;
  animationDelay?: number;
}

const ModernStatCard: React.FC<ModernStatCardProps> = ({
  title,
  value,
  icon,
  trend,
  iconColor = 'text-primary',
  iconBgColor = 'bg-gradient-to-br from-primary/20 to-primary/10',
  className = '',
  animationDelay = 0,
}) => {
  const trendColors = {
    up: 'text-green-500',
    down: 'text-red-500',
    neutral: 'text-gray-500',
  };

  return (
    <motion.div
      className={`card overflow-hidden ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.4, 
        delay: animationDelay / 1000,
        ease: "easeOut" 
      }}
    >
      <div className="p-6">
        <div className="flex justify-between items-start">
          <motion.div
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ 
              duration: 0.3, 
              delay: (animationDelay + 200) / 1000,
              ease: "easeOut" 
            }}
          >
            <p className="text-sm font-medium text-muted-foreground">
              {title}
            </p>
            <motion.h3 
              className="text-2xl font-bold mt-1 jakarta"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ 
                duration: 0.4, 
                delay: (animationDelay + 300) / 1000,
                ease: "easeOut" 
              }}
            >
              {value}
            </motion.h3>
            {trend && (
              <motion.p 
                className={`text-xs flex items-center mt-1 ${trendColors[trend.direction]}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ 
                  duration: 0.3, 
                  delay: (animationDelay + 400) / 1000,
                  ease: "easeOut" 
                }}
              >
                {trend.direction === 'up' && (
                  <svg className="h-3 w-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 17L17 7M17 7H7M17 7V17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
                {trend.direction === 'down' && (
                  <svg className="h-3 w-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 7L17 17M17 17H7M17 17V7" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                )}
                {trend.value} {trend.label}
              </motion.p>
            )}
          </motion.div>
          <motion.div 
            className={`${iconBgColor} p-3 rounded-full shadow-sm`}
            initial={{ opacity: 0, scale: 0.5 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ 
              duration: 0.4, 
              delay: (animationDelay + 200) / 1000,
              type: "spring",
              stiffness: 200,
              damping: 15
            }}
          >
            <div className={`${iconColor}`}>
              {icon}
            </div>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
};

export default ModernStatCard;
