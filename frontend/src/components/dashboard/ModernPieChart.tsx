import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Tooltip,
  Legend,
  ResponsiveContainer,
  TooltipProps,
} from 'recharts';
import ModernChartTooltip from './ModernChartTooltip';

interface ModernPieChartProps {
  data: any[];
  dataKey: string;
  nameKey: string;
  colors: string[];
  height?: number;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  className?: string;
  tooltipFormatter?: (value: number, name: string, props: any) => React.ReactNode;
  tooltipLabelFormatter?: (label: string) => React.ReactNode;
  animationDuration?: number;
  labelLine?: boolean;
  label?: boolean | React.ReactNode | ((props: any) => React.ReactNode);
}

const ModernPieChart: React.FC<ModernPieChartProps> = ({
  data,
  dataKey,
  nameKey,
  colors,
  height = 300,
  showLegend = true,
  innerRadius = 0,
  outerRadius = 80,
  className = '',
  tooltipFormatter,
  tooltipLabelFormatter,
  animationDuration = 1000,
  labelLine = false,
  label,
}) => {
  const renderTooltip = (props: TooltipProps<any, any>) => (
    <ModernChartTooltip
      {...props}
      formatter={tooltipFormatter}
      labelFormatter={tooltipLabelFormatter}
    />
  );

  const defaultLabel = ({ name, percent }: { name: string; percent: number }) => 
    `${name}: ${(percent * 100).toFixed(0)}%`;

  const renderLabel = label === true ? defaultLabel : label;

  return (
    <div className={`w-full h-[${height}px] ${className}`}>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={labelLine}
            label={renderLabel}
            outerRadius={outerRadius}
            innerRadius={innerRadius}
            fill="#8884d8"
            dataKey={dataKey}
            nameKey={nameKey}
            isAnimationActive={true}
            animationDuration={animationDuration}
            animationBegin={0}
            animationEasing="ease-out"
          >
            {data.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={colors[index % colors.length]} 
                stroke="none"
              />
            ))}
          </Pie>
          <Tooltip content={renderTooltip} />
          {showLegend && (
            <Legend
              layout="horizontal"
              verticalAlign="bottom"
              align="center"
              wrapperStyle={{ paddingTop: 20 }}
              iconType="circle"
              iconSize={8}
            />
          )}
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ModernPieChart;
