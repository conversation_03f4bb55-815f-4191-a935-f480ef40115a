import React from 'react';
import { motion } from 'framer-motion';

interface ModernInsightCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconColor?: string;
  iconBgColor?: string;
  className?: string;
  animationDelay?: number;
}

const ModernInsightCard: React.FC<ModernInsightCardProps> = ({
  title,
  value,
  icon,
  iconColor = 'text-primary',
  iconBgColor = 'bg-gradient-to-br from-primary/20 to-primary/10',
  className = '',
  animationDelay = 0,
}) => {
  return (
    <motion.div
      className={`flex items-center justify-between p-5 bg-gradient-to-br from-secondary/50 to-secondary/30 rounded-xl border border-border/30 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.4, 
        delay: animationDelay / 1000,
        ease: "easeOut" 
      }}
      whileHover={{ 
        y: -5,
        boxShadow: "0 10px 30px -15px rgba(0, 0, 0, 0.1)",
        transition: { duration: 0.3 }
      }}
    >
      <motion.div
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ 
          duration: 0.3, 
          delay: (animationDelay + 200) / 1000,
          ease: "easeOut" 
        }}
      >
        <p className="text-sm font-medium text-muted-foreground">
          {title}
        </p>
        <motion.p 
          className="text-2xl font-bold jakarta mt-1"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ 
            duration: 0.4, 
            delay: (animationDelay + 300) / 1000,
            ease: "easeOut" 
          }}
        >
          {value}
        </motion.p>
      </motion.div>
      <motion.div 
        className={`h-14 w-14 ${iconBgColor} rounded-full flex items-center justify-center shadow-sm`}
        initial={{ opacity: 0, scale: 0.5, rotate: -10 }}
        animate={{ opacity: 1, scale: 1, rotate: 0 }}
        transition={{ 
          duration: 0.5, 
          delay: (animationDelay + 200) / 1000,
          type: "spring",
          stiffness: 200,
          damping: 15
        }}
      >
        <div className={`${iconColor}`}>
          {icon}
        </div>
      </motion.div>
    </motion.div>
  );
};

export default ModernInsightCard;
