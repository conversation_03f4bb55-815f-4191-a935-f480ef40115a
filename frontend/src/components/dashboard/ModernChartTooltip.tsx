import React from 'react';
import { motion } from 'framer-motion';

interface ModernChartTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
  labelFormatter?: (label: string) => React.ReactNode;
  contentStyle?: React.CSSProperties;
  itemStyle?: React.CSSProperties;
  formatter?: (value: number, name: string, props: any) => React.ReactNode;
  wrapperClassName?: string;
}

const ModernChartTooltip: React.FC<ModernChartTooltipProps> = ({
  active,
  payload,
  label,
  labelFormatter,
  contentStyle,
  itemStyle,
  formatter,
  wrapperClassName,
}) => {
  if (!active || !payload || payload.length === 0) {
    return null;
  }

  const formattedLabel = labelFormatter ? labelFormatter(label || '') : label;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm p-3 rounded-lg shadow-lg border border-gray-100 dark:border-gray-700 ${wrapperClassName || ''}`}
      style={{
        ...contentStyle,
        minWidth: '150px',
      }}
      role="tooltip"
      aria-live="polite"
    >
      {formattedLabel && (
        <div className="font-medium text-sm mb-1 text-gray-700 dark:text-gray-300 border-b border-gray-100 dark:border-gray-700 pb-1">
          {formattedLabel}
        </div>
      )}
      <div className="space-y-1">
        {payload.map((entry, index) => {
          const value = formatter
            ? formatter(entry.value, entry.name, entry)
            : entry.value;
          
          return (
            <div
              key={`tooltip-item-${index}`}
              className="flex items-center justify-between text-sm"
              style={itemStyle}
            >
              <div className="flex items-center">
                <div
                  className="h-2.5 w-2.5 rounded-full mr-2"
                  style={{ backgroundColor: entry.color }}
                  aria-hidden="true"
                ></div>
                <span className="text-gray-600 dark:text-gray-400">
                  {entry.name}:
                </span>
              </div>
              <span className="font-medium text-gray-800 dark:text-gray-200 ml-2">
                {value}
              </span>
            </div>
          );
        })}
      </div>
    </motion.div>
  );
};

export default ModernChartTooltip;
