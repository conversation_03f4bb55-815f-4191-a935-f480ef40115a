import React from 'react';
import { motion } from 'framer-motion';

interface ModernProgressBarProps {
  label: string;
  value: number;
  maxValue?: number;
  status?: 'success' | 'warning' | 'error' | 'info';
  statusText?: string;
  showPercentage?: boolean;
  height?: number;
  className?: string;
  animationDuration?: number;
}

const ModernProgressBar: React.FC<ModernProgressBarProps> = ({
  label,
  value,
  maxValue = 100,
  status = 'success',
  statusText,
  showPercentage = false,
  height = 8,
  className = '',
  animationDuration = 1000,
}) => {
  const percentage = Math.min(100, Math.max(0, (value / maxValue) * 100));
  
  const statusColors = {
    success: {
      text: 'text-green-600',
      bg: 'bg-gradient-to-r from-primary to-primary/80',
    },
    warning: {
      text: 'text-yellow-600',
      bg: 'bg-gradient-to-r from-yellow-500 to-yellow-400',
    },
    error: {
      text: 'text-red-600',
      bg: 'bg-gradient-to-r from-red-500 to-red-400',
    },
    info: {
      text: 'text-blue-600',
      bg: 'bg-gradient-to-r from-blue-500 to-blue-400',
    },
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium jakarta">{label}</span>
        <span className={`text-sm font-medium jakarta ${statusColors[status].text}`}>
          {statusText || (showPercentage ? `${percentage.toFixed(1)}%` : value.toString())}
        </span>
      </div>
      <div 
        className="w-full bg-secondary rounded-full overflow-hidden"
        style={{ height: `${height}px` }}
        role="progressbar"
        aria-valuenow={percentage}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label={`${label}: ${percentage}%`}
      >
        <motion.div
          className={`h-full rounded-full ${statusColors[status].bg}`}
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ 
            duration: animationDuration / 1000, 
            ease: "easeOut" 
          }}
        />
      </div>
    </div>
  );
};

export default ModernProgressBar;
