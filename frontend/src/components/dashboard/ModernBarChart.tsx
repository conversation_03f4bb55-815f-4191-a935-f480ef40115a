import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  TooltipProps,
} from 'recharts';
import { motion } from 'framer-motion';
import ModernChartTooltip from './ModernChartTooltip';

interface ModernBarChartProps {
  data: any[];
  dataKeys: {
    key: string;
    name: string;
    color: string;
  }[];
  xAxisDataKey: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  barSize?: number;
  barGap?: number;
  barCategoryGap?: number | string;
  barRadius?: number[];
  className?: string;
  tooltipFormatter?: (value: number, name: string, props: any) => React.ReactNode;
  tooltipLabelFormatter?: (label: string) => React.ReactNode;
  animationDuration?: number;
}

const ModernBarChart: React.FC<ModernBarChartProps> = ({
  data,
  dataKeys,
  xAxisDataKey,
  height = 300,
  showGrid = true,
  showLegend = true,
  barSize,
  barGap,
  barCategoryGap,
  barRadius = [4, 4, 0, 0],
  className = '',
  tooltipFormatter,
  tooltipLabelFormatter,
  animationDuration = 1000,
}) => {
  const renderTooltip = (props: TooltipProps<any, any>) => (
    <ModernChartTooltip
      {...props}
      formatter={tooltipFormatter}
      labelFormatter={tooltipLabelFormatter}
    />
  );

  return (
    <div className={`w-full h-[${height}px] ${className}`}>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart
          data={data}
          barSize={barSize}
          barGap={barGap}
          barCategoryGap={barCategoryGap}
          margin={{ top: 10, right: 10, left: 0, bottom: 5 }}
        >
          {showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="rgba(0,0,0,0.05)"
              vertical={false}
            />
          )}
          <XAxis
            dataKey={xAxisDataKey}
            axisLine={false}
            tickLine={false}
            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 12 }}
            dy={10}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 12 }}
            dx={-10}
          />
          <Tooltip content={renderTooltip} />
          {showLegend && (
            <Legend
              wrapperStyle={{ paddingTop: 15 }}
              iconType="circle"
              iconSize={8}
            />
          )}
          {dataKeys.map((dataKey, index) => (
            <Bar
              key={dataKey.key}
              dataKey={dataKey.key}
              name={dataKey.name}
              fill={dataKey.color}
              radius={barRadius}
              isAnimationActive={true}
              animationDuration={animationDuration}
              animationBegin={index * 150}
              animationEasing="ease-out"
            />
          ))}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ModernBarChart;
