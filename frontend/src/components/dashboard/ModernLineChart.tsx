import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  TooltipProps,
} from 'recharts';
import ModernChartTooltip from './ModernChartTooltip';

interface ModernLineChartProps {
  data: any[];
  dataKeys: {
    key: string;
    name: string;
    color: string;
    strokeWidth?: number;
    dotRadius?: number;
    activeDotRadius?: number;
  }[];
  xAxisDataKey: string;
  height?: number;
  showGrid?: boolean;
  showLegend?: boolean;
  className?: string;
  tooltipFormatter?: (value: number, name: string, props: any) => React.ReactNode;
  tooltipLabelFormatter?: (label: string) => React.ReactNode;
  animationDuration?: number;
}

const ModernLineChart: React.FC<ModernLineChartProps> = ({
  data,
  dataKeys,
  xAxisDataKey,
  height = 300,
  showGrid = true,
  showLegend = true,
  className = '',
  tooltipFormatter,
  tooltipLabelF<PERSON>atter,
  animationDuration = 1500,
}) => {
  const renderTooltip = (props: TooltipProps<any, any>) => (
    <ModernChartTooltip
      {...props}
      formatter={tooltipFormatter}
      labelFormatter={tooltipLabelFormatter}
    />
  );

  return (
    <div className={`w-full h-[${height}px] ${className}`}>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart
          data={data}
          margin={{ top: 10, right: 10, left: 0, bottom: 5 }}
        >
          {showGrid && (
            <CartesianGrid
              strokeDasharray="3 3"
              stroke="rgba(0,0,0,0.05)"
              vertical={false}
            />
          )}
          <XAxis
            dataKey={xAxisDataKey}
            axisLine={false}
            tickLine={false}
            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 12 }}
            dy={10}
          />
          <YAxis
            axisLine={false}
            tickLine={false}
            tick={{ fill: 'hsl(var(--muted-foreground))', fontSize: 12 }}
            dx={-10}
          />
          <Tooltip content={renderTooltip} />
          {showLegend && (
            <Legend
              wrapperStyle={{ paddingTop: 15 }}
              iconType="circle"
              iconSize={8}
            />
          )}
          {dataKeys.map((dataKey, index) => (
            <Line
              key={dataKey.key}
              type="monotone"
              dataKey={dataKey.key}
              name={dataKey.name}
              stroke={dataKey.color}
              strokeWidth={dataKey.strokeWidth || 2}
              dot={{
                stroke: dataKey.color,
                strokeWidth: 2,
                r: dataKey.dotRadius || 4,
                fill: 'white',
              }}
              activeDot={{
                r: dataKey.activeDotRadius || 6,
                stroke: dataKey.color,
                strokeWidth: 2,
                fill: 'white',
              }}
              isAnimationActive={true}
              animationDuration={animationDuration}
              animationBegin={index * 150}
              animationEasing="ease-out"
            />
          ))}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ModernLineChart;
