import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BellIcon, LogOut, Settings, User } from "lucide-react";
import { ModeToggle } from "./mode-toggle";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";

interface ProfileMenuProps {
  showFullMenu?: boolean;
}

const ProfileMenu: React.FC<ProfileMenuProps> = ({ showFullMenu = false }) => {
  const navigate = useNavigate();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center space-x-3 cursor-pointer p-2 rounded-full hover:bg-primary/5 transition-colors">
          <div className="h-9 w-9 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center text-white font-bold">
            JD
          </div>
          {showFullMenu && <span className="text-sm font-medium">John Doe</span>}
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {/* Only show these items on mobile */}
        {!showFullMenu && (
          <>
            <DropdownMenuItem className="flex items-center gap-2">
              <div className="relative">
                <BellIcon className="w-4 h-4" />
                <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-primary text-[8px] flex items-center justify-center text-white font-bold">
                  3
                </span>
              </div>
              <span>Notifications</span>
            </DropdownMenuItem>
            
            <DropdownMenuItem className="flex items-center gap-2">
              <ModeToggle showLabel={true} />
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
          </>
        )}
        
        <DropdownMenuItem className="flex items-center gap-2" onClick={() => navigate("/profile")}>
          <User className="w-4 h-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem className="flex items-center gap-2" onClick={() => navigate("/settings")}>
          <Settings className="w-4 h-4" />
          <span>Settings</span>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem className="flex items-center gap-2 text-red-500" onClick={handleLogout}>
          <LogOut className="w-4 h-4" />
          <span>Logout</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ProfileMenu;
