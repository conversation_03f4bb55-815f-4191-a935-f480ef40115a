// src/components/ProtectedRoutes.tsx

import React from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import Loading from "@/Loading"; // Ensure the path is correct

interface ProtectedRoutesProps {
  children?: React.ReactNode;
}

const ProtectedRoutes: React.FC<ProtectedRoutesProps> = ({ children }) => {
  const { isLoggedIn, loading } = useAuth();

  if (loading) {
    // Render the Loading component while authentication status is being determined
    return <Loading />;
  }

  if (!isLoggedIn) {
    // Redirect to /login if not authenticated
    return <Navigate to="/login" replace />;
  }

  // Render child components or nested routes if authenticated
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoutes;
