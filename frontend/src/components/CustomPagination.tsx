import React from "react";
import { useSearchParams } from "react-router-dom";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface CustomPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const CustomPagination: React.FC<CustomPaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const pageNumbers = [];

  // Decide how many page numbers to show
  const maxPageNumbersToShow = 5; // Adjust as needed

  let startPage = Math.max(
    1,
    currentPage - Math.floor(maxPageNumbersToShow / 2)
  );
  let endPage = startPage + maxPageNumbersToShow - 1;

  if (endPage > totalPages) {
    endPage = totalPages;
    startPage = Math.max(1, endPage - maxPageNumbersToShow + 1);
  }

  // Generate the list of page numbers
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="flex items-center justify-between my-6 px-2">
      <div className="text-sm text-gray-500">
        1 — {Math.min(25, totalPages)} of {totalPages}
      </div>

      <Pagination className="my-0">
        <PaginationContent>
          {/* Previous Button */}
          {currentPage > 1 ? (
            <PaginationPrevious
              className="cursor-pointer border border-gray-200 bg-white hover:bg-gray-50 transition-colors text-sm"
              onClick={() => {
                onPageChange(currentPage - 1);
                setSearchParams({ page: (currentPage - 1).toString() });
              }}
            />
          ) : (
            <PaginationPrevious className="disabled cursor-not-allowed opacity-50 border border-gray-200 text-sm" />
          )}

          {/* Page Numbers - Simplified for the design */}
          {pageNumbers.slice(0, 2).map((pageNumber) => (
            <PaginationItem key={pageNumber} className="cursor-pointer">
              <PaginationLink
                className={
                  pageNumber === currentPage
                    ? "bg-primary text-white border-primary hover:bg-primary/90 shadow-sm text-sm"
                    : "border border-gray-200 bg-white hover:bg-gray-50 transition-colors text-sm"
                }
                onClick={() => {
                  onPageChange(pageNumber);
                  setSearchParams({ page: pageNumber.toString() });
                }}
              >
                {pageNumber}
              </PaginationLink>
            </PaginationItem>
          ))}

          {/* Next Button */}
          <PaginationNext
            onClick={() => {
              if (currentPage === totalPages) return;
              onPageChange(currentPage + 1);
              setSearchParams({ page: (currentPage + 1).toString() });
            }}
            aria-disabled={currentPage === totalPages}
            className={
              currentPage === totalPages
                ? "disabled cursor-not-allowed opacity-50 border border-gray-200 text-sm"
                : "cursor-pointer border border-gray-200 bg-white hover:bg-gray-50 transition-colors text-sm"
            }
          />
        </PaginationContent>
      </Pagination>
    </div>
  );
};

export default CustomPagination;
