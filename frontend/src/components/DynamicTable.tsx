import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { MoreHorizontal } from "lucide-react";

interface Column<T> {
  header: string;
  accessor: keyof T;
  cell?: (value: T[keyof T]) => React.ReactNode;
}

interface Action<T> {
  label: string;
  icon: React.ReactNode;
  onClick: (item: T) => void;
}

interface DynamicTableProps<T> {
  data: T[];
  columns: Column<T>[];
  actions?: Action<T>[];
}

export function DynamicTable<T extends { id: string | number }>({
  data,
  columns,
  actions,
}: DynamicTableProps<T>) {
  return (
    <div className="mt-6">
      <Table className="divide-y divide-border dark:divide-border-dark">
        <TableHeader className="bg-card dark:bg-card-dark">
          <TableRow>
            {columns.map((column) => (
              <TableHead
                key={column.header}
                className="px-4 py-2 md:px-6 md:py-3 text-left text-sm font-medium text-foreground dark:text-foreground-dark uppercase tracking-wider"
              >
                {column.header}
              </TableHead>
            ))}
            {actions && (
              <TableHead className="px-4 py-2 md:px-6 md:py-3 text-center text-sm font-medium text-foreground dark:text-foreground-dark uppercase tracking-wider">
                Actions
              </TableHead>
            )}
          </TableRow>
        </TableHeader>
        <TableBody className="bg-background dark:bg-background-dark divide-y divide-border dark:divide-border-dark">
          {data.map((item) => (
            <TableRow
              key={item.id}
              className="hover:bg-muted dark:hover:bg-muted-dark transition-colors"
            >
              {columns.map((column) => (
                <TableCell
                  key={`${item.id}-${String(column.accessor)}`}
                  className="px-4 py-2 md:px-6 md:py-4 text-sm text-foreground dark:text-foreground-dark"
                >
                  {column.cell
                    ? column.cell(item[column.accessor])
                    : String(item[column.accessor])}
                </TableCell>
              ))}
              {actions && (
                <TableCell className="px-4 py-2 md:px-6 md:py-4 text-sm text-foreground dark:text-foreground-dark text-center">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 hover:bg-accent hover:bg-opacity-10 dark:hover:bg-accent-dark dark:hover:bg-opacity-20"
                        aria-label="Open menu"
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-56 bg-card dark:bg-card-dark border border-border dark:border-border-dark rounded-md shadow-lg">
                      <div className="grid gap-2 p-2">
                        {actions.map((action, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            className="w-full justify-start text-left hover:bg-muted hover:bg-opacity-10 dark:hover:bg-muted-dark dark:hover:bg-opacity-20"
                            onClick={() => action.onClick(item)}
                          >
                            <span className="mr-2">{action.icon}</span>
                            {action.label}
                          </Button>
                        ))}
                      </div>
                    </PopoverContent>
                  </Popover>
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
