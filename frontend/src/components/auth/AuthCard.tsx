import React from 'react';
import { motion } from 'framer-motion';

interface AuthCardProps {
  children: React.ReactNode;
}

const AuthCard: React.FC<AuthCardProps> = ({ children }) => {
  return (
    <motion.div
      className="w-full rounded-xl p-6 md:p-8 relative overflow-hidden"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      style={{
        background: 'linear-gradient(145deg, #F4F6F9, #FCFDFF)',
        boxShadow: '0 4px 24px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.04)'
      }}
    >
      {/* Subtle decorative elements */}
      <div className="absolute top-0 right-0 w-full h-full overflow-hidden pointer-events-none">
        <div
          className="absolute top-0 right-0 w-full h-1"
          style={{
            background: 'linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.2))'
          }}
        />
        <div
          className="absolute -top-[40%] -right-[40%] w-[80%] h-[80%] rounded-full opacity-[0.03]"
          style={{
            background: 'radial-gradient(circle, rgba(99, 102, 241, 0.8), transparent 70%)'
          }}
        />
        <div
          className="absolute -bottom-[40%] -left-[40%] w-[80%] h-[80%] rounded-full opacity-[0.03]"
          style={{
            background: 'radial-gradient(circle, rgba(139, 92, 246, 0.8), transparent 70%)'
          }}
        />
      </div>

      {/* Content container */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
};

export default AuthCard;
