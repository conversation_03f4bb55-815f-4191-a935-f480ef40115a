import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Share2, MessageSquare, FileAudio, Calendar, Play, Pause, Tag, Clock, Download, User } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import AudioPlayer from "@/components/AudioPlayer";
import type { Recording } from "@/api/services/recordings/recordingService";

interface RecordingGridProps {
  data: Recording[];
  onItemClick: (item: Recording) => void;
  actions: {
    label: string;
    icon: React.ReactNode;
    onClick: (item: Recording) => void;
  }[];
  formatDate: (dateString: string) => string;
  getRecordingType: (recordingName: string) => string;
  getTypeBadgeVariant: (type: string) => string;
}

const RecordingGrid: React.FC<RecordingGridProps> = ({
  data,
  onItemClick,
  actions,
  formatDate,
  getRecordingType,
  getTypeBadgeVariant
}) => {
  const [playingId, setPlayingId] = useState<string | null>(null);

  // Toggle play/pause for a recording
  const togglePlayPause = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (playingId === id) {
      setPlayingId(null);
    } else {
      setPlayingId(id);
    }
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-6">
      {data.map((item) => {
        return (
          <Card
            key={item.id}
            className="overflow-hidden border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
            onClick={() => onItemClick(item)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 bg-gradient-to-br from-primary/20 to-primary/10 rounded-lg flex items-center justify-center">
                    <FileAudio className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900 line-clamp-1">
                      {item.recordingName}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: {item.id}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => togglePlayPause(item.id, e)}
                  >
                    {playingId === item.id ? (
                      <Pause className="h-4 w-4 text-primary" />
                    ) : (
                      <Play className="h-4 w-4 text-primary" />
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Share", item);
                    }}
                  >
                    <Share2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                      >
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[160px]">
                      {actions.map((action, index) => (
                        <DropdownMenuItem
                          key={index}
                          onClick={(e) => {
                            e.stopPropagation();
                            action.onClick(item);
                          }}
                          className="cursor-pointer"
                        >
                          <span className="mr-2 h-4 w-4 text-gray-500">
                            {action.icon}
                          </span>
                          <span>{action.label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              <div className="mb-3">
                <Badge
                  variant={getTypeBadgeVariant(item.type)}
                  className="capitalize"
                >
                  <Tag className="h-3 w-3 mr-1" />
                  {item.type}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Date</div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-700">
                      {formatDate(item.date)}
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Duration</div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-700">
                      {item.duration}
                    </span>
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-100">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-gray-500 hover:text-primary hover:bg-primary/5 px-2 py-1 h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log("Download", item);
                  }}
                >
                  <Download className="h-3 w-3 mr-1" />
                  Download
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-gray-500 hover:text-primary hover:bg-primary/5 px-2 py-1 h-auto"
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log("Transcript", item);
                  }}
                >
                  <MessageSquare className="h-3 w-3 mr-1" />
                  Transcript
                </Button>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default RecordingGrid;