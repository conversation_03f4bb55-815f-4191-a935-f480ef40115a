import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  MoreHorizontal,
  ChevronUp,
  ChevronDown,
  Check,
  ArrowUpDown,
  Share2,
  SlidersHorizontal,
  ListFilter,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface Column<T> {
  header: string;
  accessor: keyof T;
  cell?: (value: T[keyof T], item: T, index?: number) => React.ReactNode;
  sortable?: boolean;
  className?: string;
}

interface Action<T> {
  label: string;
  icon: React.ReactNode;
  onClick: (item: T) => void;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
}

interface ModernTableProps<T> {
  data: T[];
  columns: Column<T>[];
  actions?: Action<T>[];
  onRowClick?: (item: T) => void;
  isSelectable?: boolean;
  isLoading?: boolean;
  emptyState?: React.ReactNode;
  showColumnSelection?: boolean;
}

type SortDirection = "asc" | "desc" | null;

export function ModernTable<T extends { id: string | number }>({
  data,
  columns,
  actions,
  onRowClick,
  isSelectable = false,
  isLoading = false,
  emptyState,
  showColumnSelection = false,
}: ModernTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<keyof T | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [selectedRows, setSelectedRows] = useState<Set<string | number>>(
    new Set()
  );

  // Column visibility state - moved to top to fix hooks order
  const [columnVisibility, setColumnVisibility] = useState<
    Record<string, boolean>
  >(
    columns.reduce(
      (acc, column) => ({ ...acc, [column.accessor as string]: true }),
      {}
    )
  );

  // Handle sorting
  const handleSort = (column: keyof T) => {
    if (sortColumn === column) {
      // Toggle direction if same column
      if (sortDirection === "asc") {
        setSortDirection("desc");
      } else if (sortDirection === "desc") {
        setSortDirection(null);
        setSortColumn(null);
      } else {
        setSortDirection("asc");
      }
    } else {
      // New column, set to ascending
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // Sort data if needed
  const sortedData = React.useMemo(() => {
    if (!sortColumn || !sortDirection) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortColumn];
      const bValue = b[sortColumn];

      if (aValue === bValue) return 0;

      // Handle different types
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // For numbers and other types
      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1;
      return sortDirection === "asc" ? 1 : -1;
    });
  }, [data, sortColumn, sortDirection]);

  // Handle row selection
  const toggleRowSelection = (id: string | number, event: React.MouseEvent) => {
    event.stopPropagation();
    setSelectedRows((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Handle row click
  const handleRowClick = (item: T) => {
    if (onRowClick) {
      onRowClick(item);
    }
  };

  // Render empty state
  if (data.length === 0 && !isLoading) {
    return (
      <div className="mt-6 rounded-lg border border-border/50 bg-card shadow-sm overflow-hidden">
        <div className="p-8 flex flex-col items-center justify-center text-center">
          {emptyState || (
            <>
              <div className="h-16 w-16 rounded-full bg-muted flex items-center justify-center mb-4">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-8 w-8 text-muted-foreground"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-foreground mb-1">
                No data available
              </h3>
              <p className="text-muted-foreground">
                No items found. Try adjusting your filters or add a new item.
              </p>
            </>
          )}
        </div>
      </div>
    );
  }



  // Column selection dropdown
  const ColumnSelectionDropdown = () => {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="ml-auto h-8 flex items-center gap-1 text-xs"
          >
            <ListFilter className="h-3.5 w-3.5" />
            Columns
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[180px]">
          <DropdownMenuCheckboxItem
            checked={Object.values(columnVisibility).every(Boolean)}
            onCheckedChange={(checked) => {
              const newVisibility = { ...columnVisibility };
              Object.keys(newVisibility).forEach((key) => {
                newVisibility[key] = checked;
              });
              setColumnVisibility(newVisibility);
            }}
            className="cursor-pointer"
          >
            Show All
          </DropdownMenuCheckboxItem>
          <DropdownMenuSeparator />
          {columns.map((column) => (
            <DropdownMenuCheckboxItem
              key={column.header}
              checked={columnVisibility[column.accessor as string]}
              onCheckedChange={(checked) => {
                setColumnVisibility((prev) => ({
                  ...prev,
                  [column.accessor as string]: checked,
                }));
              }}
              className="cursor-pointer"
            >
              {column.header}
            </DropdownMenuCheckboxItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  return (
    <div className="mt-6 rounded-lg border border-gray-200 bg-white shadow-sm overflow-hidden">
      {showColumnSelection && (
        <div className="px-4 py-2 flex justify-end border-b border-gray-200">
          <ColumnSelectionDropdown />
        </div>
      )}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader className="bg-gray-50">
            <TableRow className="hover:bg-transparent border-b border-gray-200">
              {isSelectable && (
                <TableHead className="w-[40px] text-center">
                  <div className="flex items-center justify-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary/30"
                      onChange={() => {}} // Add select all functionality here
                    />
                  </div>
                </TableHead>
              )}
              {columns.map(
                (column) =>
                  columnVisibility[column.accessor as string] && (
                    <TableHead
                      key={column.header}
                      className={cn(
                        "px-4 py-3 text-left text-xs font-medium text-gray-500 tracking-wider",
                        column.sortable && "cursor-pointer select-none",
                        column.className
                      )}
                      onClick={() =>
                        column.sortable && handleSort(column.accessor)
                      }
                    >
                      <div className="flex items-center space-x-1">
                        <span>{column.header}</span>
                        {column.sortable && (
                          <span
                            className={
                              sortColumn === column.accessor
                                ? "text-primary"
                                : "text-gray-400"
                            }
                          >
                            {sortColumn === column.accessor ? (
                              sortDirection === "asc" ? (
                                <ChevronUp className="h-4 w-4" />
                              ) : (
                                <ChevronDown className="h-4 w-4" />
                              )
                            ) : (
                              <ArrowUpDown className="h-3 w-3" />
                            )}
                          </span>
                        )}
                      </div>
                    </TableHead>
                  )
              )}
              {actions && (
                <TableHead className="w-[100px] px-4 py-3 text-center text-xs font-medium text-gray-500 tracking-wider">
                  Actions
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading
              ? // Loading state
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={`loading-${index}`} className="animate-pulse">
                    {isSelectable && (
                      <TableCell className="w-[40px] text-center">
                        <div className="h-4 w-4 bg-gray-200 rounded mx-auto" />
                      </TableCell>
                    )}
                    {columns.map(
                      (column, colIndex) =>
                        columnVisibility[column.accessor as string] && (
                          <TableCell
                            key={`loading-cell-${colIndex}`}
                            className={cn(
                              "px-4 py-4",
                              // Right-align numerical placeholders for consistency
                              column.header.includes("Total") ||
                                column.header === "Price"
                                ? "text-right"
                                : ""
                            )}
                          >
                            <div
                              className={cn(
                                "h-4 bg-gray-200 rounded",
                                // Adjust width based on column type
                                column.header.includes("Total") ||
                                  column.header === "Price"
                                  ? "w-1/2 ml-auto"
                                  : "w-3/4"
                              )}
                            />
                          </TableCell>
                        )
                    )}
                    {actions && (
                      <TableCell className="w-[100px] px-4 py-4 text-center">
                        <div className="h-8 w-8 bg-gray-200 rounded-full mx-auto" />
                      </TableCell>
                    )}
                  </TableRow>
                ))
              : // Actual data
                sortedData.map((item) => (
                  <TableRow
                    key={item.id}
                    className={cn(
                      "border-b border-gray-100 transition-colors",
                      onRowClick && "cursor-pointer hover:bg-gray-50"
                    )}
                    onClick={() => handleRowClick(item)}
                  >
                    {isSelectable && (
                      <TableCell
                        className="w-[40px] text-center"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="flex items-center justify-center">
                          <input
                            type="checkbox"
                            className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary/30"
                            checked={selectedRows.has(item.id)}
                            onChange={(e) => toggleRowSelection(item.id, e)}
                          />
                        </div>
                      </TableCell>
                    )}
                    {columns.map(
                      (column, colIndex) =>
                        columnVisibility[column.accessor as string] && (
                          <TableCell
                            key={`${item.id}-${String(column.accessor)}`}
                            className={cn(
                              "px-4 py-4 text-sm",
                              column.className,
                              // Right-align numerical data
                              typeof item[column.accessor] === "number" &&
                                "text-right"
                            )}
                          >
                            {column.cell
                              ? column.cell(
                                  item[column.accessor],
                                  item,
                                  sortedData.indexOf(item)
                                )
                              : String(item[column.accessor])}
                          </TableCell>
                        )
                    )}
                    {actions && (
                      <TableCell
                        className="w-[100px] px-4 py-4 text-center"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <div className="flex items-center justify-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 px-2 text-xs text-gray-700 hover:bg-gray-100"
                          >
                            <Share2 className="h-3 w-3 mr-1" />
                            Share
                          </Button>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                                aria-label="Open menu"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                              align="end"
                              className="w-[160px]"
                            >
                              {actions.map((action, index) => (
                                <DropdownMenuItem
                                  key={index}
                                  onClick={() => action.onClick(item)}
                                  className="cursor-pointer"
                                >
                                  <span className="mr-2 h-4 w-4 text-gray-500">
                                    {action.icon}
                                  </span>
                                  <span>{action.label}</span>
                                </DropdownMenuItem>
                              ))}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    )}
                  </TableRow>
                ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
