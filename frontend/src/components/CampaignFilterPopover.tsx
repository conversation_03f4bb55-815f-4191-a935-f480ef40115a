import React, { useState } from "react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { SlidersHorizontal, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

interface CampaignFilterPopoverProps {
  onFilterChange: (filters: any) => void;
}

const CampaignFilterPopover: React.FC<CampaignFilterPopoverProps> = ({ onFilterChange }) => {
  const [status, setStatus] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<[number, number]>([0, 100]);

  // Apply filters
  const applyFilters = () => {
    onFilterChange({
      status,
      dateRange,
    });
  };

  // Reset filters
  const resetFilters = () => {
    setStatus(null);
    setDateRange([0, 100]);
    onFilterChange({});
  };

  // Count active filters
  const activeFilterCount = [
    status !== null && status !== "all",
    dateRange[0] > 0 || dateRange[1] < 100,
  ].filter(Boolean).length;

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-9 border-gray-200 text-gray-700 hover:bg-gray-50 relative"
        >
          <SlidersHorizontal className="h-4 w-4 mr-2" />
          Filters
          {activeFilterCount > 0 && (
            <Badge
              variant="purple"
              className="ml-2 h-5 w-5 p-0 flex items-center justify-center"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[280px] p-4" align="end">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-sm">Filter Campaigns</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-gray-500"
            onClick={resetFilters}
          >
            Reset
            <X className="h-3 w-3 ml-1" />
          </Button>
        </div>

        <div className="space-y-4">
          <div>
            <Label htmlFor="status" className="text-xs font-medium mb-1.5 block">
              Campaign Status
            </Label>
            <Select
              value={status || "all"}
              onValueChange={(value) => setStatus(value === "all" ? null : value)}
            >
              <SelectTrigger
                id="status"
                className="w-full h-9 text-sm"
              >
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="paused">Paused</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div className="pt-2">
            <Button
              className="w-full bg-primary hover:bg-primary/90"
              onClick={applyFilters}
            >
              Apply Filters
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default CampaignFilterPopover;