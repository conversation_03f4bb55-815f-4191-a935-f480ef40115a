import React from "react";
import { Card } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Circle, MoreHorizontal, Activity, Share2 } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface AgentGridProps {
  data: any[];
  onItemClick: (item: any) => void;
  actions: {
    label: string;
    icon: React.ReactNode;
    onClick: (item: any) => void;
  }[];
}

const AgentGrid: React.FC<AgentGridProps> = ({ data, onItemClick, actions }) => {
  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part.charAt(0))
      .join("")
      .toUpperCase();
  };

  // Function to get random metrics for demo
  const getRandomMetric = (min: number, max: number) => {
    return Math.floor(Math.random() * (max - min + 1) + min);
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-6">
      {data.map((item) => {
        // Generate random data for demo
        const price = `$${(Math.random() * 100).toFixed(2)}`;
        const sales = getRandomMetric(0, 10000);
        const revenue = getRandomMetric(0, 2000000);
        const trend = Math.random() > 0.5 ? "up" : "down";
        
        // Random status
        const statuses = ["active", "inactive", "training"];
        const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
        const statusColor = 
          randomStatus === "active" ? "text-green-500" :
          randomStatus === "inactive" ? "text-gray-400" : "text-yellow-500";

        return (
          <Card 
            key={item.id} 
            className="overflow-hidden border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
            onClick={() => onItemClick(item)}
          >
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10 bg-gradient-to-br from-primary/20 to-primary/10">
                    <AvatarFallback className="text-primary font-medium">
                      {getInitials(String(item.name))}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium text-gray-900">
                      {String(item.name)}
                    </div>
                    <div className="text-xs text-gray-500">
                      ID: {String(item.id).substring(0, 8)}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                    onClick={(e) => {
                      e.stopPropagation();
                      console.log("Share", item);
                    }}
                  >
                    <Share2 className="h-4 w-4 text-gray-500" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                      <Button
                        variant="ghost"
                        className="h-8 w-8 p-0 rounded-full hover:bg-gray-100"
                      >
                        <MoreHorizontal className="h-4 w-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-[160px]">
                      {actions.map((action, index) => (
                        <DropdownMenuItem
                          key={index}
                          onClick={(e) => {
                            e.stopPropagation();
                            action.onClick(item);
                          }}
                          className="cursor-pointer"
                        >
                          <span className="mr-2 h-4 w-4 text-gray-500">
                            {action.icon}
                          </span>
                          <span>{action.label}</span>
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-3 mb-3">
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Price</div>
                  <div className="font-medium text-gray-900">{price}</div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Status</div>
                  <div className="flex items-center">
                    <Circle className={`h-2 w-2 mr-2 fill-current ${statusColor}`} />
                    <span className="capitalize text-gray-700">{randomStatus}</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Total Sales</div>
                  <div className="font-medium text-gray-900">
                    {sales > 0 ? sales.toLocaleString() : "—"}
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-xs text-gray-500">Total Revenue</div>
                  <div className="flex items-center space-x-1">
                    <span className="font-medium text-gray-900">
                      {revenue > 0 ? `$${revenue.toLocaleString()}` : "—"}
                    </span>
                    {revenue > 0 && (
                      <Activity className={`h-3 w-3 ${trend === "up" ? "text-green-500" : "text-red-500"}`} />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        );
      })}
    </div>
  );
};

export default AgentGrid;
